import { z } from "zod"
import { RecursiveKeyOf } from "./helpers"

export const formSchema = z.object({
  farmer: z.object({
    id: z.string().min(1),
    name: z.string().min(2),
  }),
  beehives: z.object({
    numberPresent: z.string().min(1),
    visitedMonthly: z.boolean(),
    visitedRecently: z.array(z.string()),
    areAnyOccupied: z.boolean(),
    awayFromWater: z.boolean(),
    awayFromCrops: z.boolean(),
    adequateFlowering: z.boolean(),
    readyForHarvest: z.string().min(1),
    rebaitMaterial: z.string().min(1),
    enoughHoneyForWinter: z.boolean(),
    beesWereMutilated: z.boolean(),
    organicWaxUsed: z.boolean(),
    provideFoodForBees: z.boolean(),
    hasInfestation: z.boolean(),
    infestationDetails: z.string().max(250).optional(),
  }),
  managerInputs: z.object({
    additionalComments: z.string().max(250).optional(),
    isCompliant: z.boolean(),
    complianceReason: z.string().max(250).optional(),
    nonComplianceReason: z.string().max(250).optional(),
  }),
})

export type FormValues = z.infer<typeof formSchema>

export type FormFieldNames = RecursiveKeyOf<FormValues>
