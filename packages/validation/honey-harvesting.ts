import { z } from "zod"

const bucketSchema = z.object({
  bucketId: z.string({
    required_error: "Bucket ID is required",
  }),
  harvestDate: z.date({
    required_error: "Harvest date is required",
  }),
  moistureContent: z
    .number({ required_error: "Moisture content is required" })
    .min(0, { message: "Moisture content must be between 0 and 100" })
    .max(100, { message: "Moisture content must be between 0 and 100" }),
  weight: z
    .number({ required_error: "Weight is required" })
    .min(1, { message: "Weight must be greater than 0" }),
})

export const formSchema = z.object({
  regionId: z.string().min(1, { message: "Region is required" }),
  chiefdomId: z.string().min(1, { message: "Chiefdom is required" }),
  zoneId: z.string().min(1, { message: "Zone is required" }),
  villageId: z.string().min(1, { message: "Village is required" }),

  farmer: z.object({
    id: z.string().min(1),
    name: z.string().min(2, { message: "Farmer name is required" }),
  }),

  beehives: z
    .array(z.string())
    .refine((value) => value.some((beehive) => beehive), {
      message: "You have to select at least one beehive.",
    }),

  saleDate: z.date({
    required_error: "Sale date is required",
  }),

  pricePerKg: z
    .number({
      required_error: "Price per kg is required",
    })
    .min(0, { message: "Price per kg must be positive" }),

  honeyType: z.enum(["light", "dark"], {
    required_error: "Honey type is required",
  }),

  buckets: z
    .array(bucketSchema)
    .min(1, { message: "At least one bucket must be added" }),

  tempInput: bucketSchema.optional(),
  ipfsHash: z.string().optional(),
  palmyraHash: z.string().optional(),
  createdAt: z.string().optional(),
  createdBy: z.string().optional(),
})

export type FormValues = z.infer<typeof formSchema>

export type Bucket = z.infer<typeof bucketSchema>
