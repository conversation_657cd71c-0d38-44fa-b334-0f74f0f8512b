import { z } from "zod"

export const formSchema = z.object({
  id: z.string().optional(),
  name: z.string(),
  status: z.string(),
  latitude: z.number({
    required_error: "Latitude is required",
  }),
  longitude: z.number({
    required_error: "Longitude is required",
  }),
  needsToBeRepaired: z.boolean(),
  isOccupied: z.boolean(),
  observation: z.string().optional(),
  isFunctional: z.boolean(),
  images: z.array(z.string()).default([]).optional(),
  farmerId: z.string().optional(),
})

export type FormValues = z.infer<typeof formSchema>
