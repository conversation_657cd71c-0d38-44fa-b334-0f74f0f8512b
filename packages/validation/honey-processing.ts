import { z } from "zod"
import { RecursiveKeyOf } from "./helpers"

export const formSchema = z.object({
  factoryStaffLead: z.object({
    id: z.string().min(1),
    name: z.string().optional(),
  }),
  factoryLocation: z.string().min(1, "Factory location is required"),
  processType: z.string().min(1, "Process type is required"),
  regionId: z.string().min(1, "Region is required"),
  chiefdomId: z.string().min(1, "Chiefdom is required"),
  zoneId: z.string().min(1, "Zone is required"),
  villageId: z.string().min(1, "Village is required"),
  pumpHopper: z.number().min(1).max(50),
  wax: z.number().min(1, "Weight of Wax is Required"),
  centrifugeTemperature: z
    .number()
    .min(1, "Temperature of Centrifuge is Required"),
  honeyTemperature: z.number().min(1, "Temperature of Honey is Required"),
  centrifugeSpeed: z
    .number()
    .min(1, {
      message:
        "Centrifuge speed must be positive. Please enter a number between 1 and 99.",
    })
    .max(99, {
      message:
        "Centrifuge speed must not exceed 99. Please enter a number between 1 and 99.",
    }),
  meshSize: z.number(),
  originalTotalWeight: z.number(),
  verifiedTotalWeight: z.number(),
  estimatedHoneyWeight: z.number(),
  buckets: z.record(
    z.object({
      verifiedWeight: z.number().optional(),
      weight: z.number(),
      contaminationStatus: z.enum(["clean", "contaminated"]).optional(),
    })
  ),
  formId: z.string().optional(),
})

export type FormValues = z.infer<typeof formSchema>

export type FormFieldNames = RecursiveKeyOf<FormValues>
