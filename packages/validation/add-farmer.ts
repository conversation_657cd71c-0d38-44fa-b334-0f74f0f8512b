import { z } from "zod"

export const formSchema = z.object({
  id: z.string().optional(),
  firstName: z.string().min(1, { message: "First name is required" }),
  lastName: z.string().min(1, { message: "Last name is required" }),
  gender: z.string().min(1, { message: "Gender is required" }),
  dob: z
    .string({ required_error: "Birth date is required" })
    .regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format"),
  phone: z.string().min(1, { message: "Phone number is required" }),
  country: z.string().min(1),
  nrc: z
    .string()
    .min(1, { message: "NRC number is required" })
    .regex(/^[0-9]{6}\/[0-9]{2}\/[0-9]{1}$/, {
      message: "NRC number must be in format: 123456/78/9",
    }),
  householdSize: z.string().min(1, { message: "Household size is required" }),
  maritalStatus: z.string().min(1, { message: "Marital status is required" }),
  estimatedAnnualIncome: z
    .number({
      required_error: "Estimated Annual Income is required",
    })
    .min(0, { message: "Estimated annual income must be positive" }),
  sourceOfIncome: z
    .string()
    .min(1, { message: "Source of income is required" }),
  regionId: z.string().min(1, { message: "Region is required" }),
  zoneId: z.string().min(1, { message: "Zone is required" }),
  chiefdomId: z.string().min(1, { message: "Chiefdom is required" }),
  villageId: z.string().min(1, { message: "Village is required" }),
  role: z.string().min(1, { message: "Role is required" }),
})

export type FormValues = z.infer<typeof formSchema>
