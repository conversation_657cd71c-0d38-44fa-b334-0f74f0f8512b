import * as z from "zod"

export const formSchema = z.object({
  testingDate: z.date({
    required_error: "Please select a testing date.",
  }),
  testerName: z.string().nonempty("Please select a QA tester's name."),
  moistureContent: z
    .number({ required_error: "Moisture content is required" })
    .min(0, { message: "Moisture content must be between 0 and 100" })
    .max(100, { message: "Moisture content must be between 0 and 100" }),

  smellInspection: z.enum(["yes", "no"], {
    required_error:
      "Please specify if the product passes the smell inspection.",
  }),
  smellComments: z.string().optional(),
  bubbles: z.enum(["yes", "no"], {
    required_error: "Please specify if you see bubbles.",
  }),
  foreignParticles: z.enum(["yes", "no"], {
    required_error: "Please specify if you see foreign particles or foam.",
  }),
  anythingElse: z.enum(["yes", "no"], {
    required_error: "Please specify if there is anything else to report.",
  }),
  additionalComments: z.string().optional(),
  drums: z
    .array(
      z.object({
        drumId: z.string().nonempty("Drum ID is required."),
        weight: z.coerce
          .number({ required_error: "Drum weight is required" })
          .min(0.1, { message: "Drum weight must be greater than 0" }),
        sealStatus: z.enum(["sealed", "unsealed"], {
          required_error: "Please specify the drum seal status.",
        }),
        isLocked: z.boolean(),
        sealedTimestamp: z.string(),
        pouredTimestamp: z.string(),
      })
    )
    .min(1, "Please add at least one drum."),
})

export type FormValues = z.infer<typeof formSchema>

export type FormFieldNames = keyof FormValues
