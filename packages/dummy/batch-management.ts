export type Farmer = {
  id: string
  firstname: string
  lastname: string
  initials: string
}

export interface Image {
  url: string
  file?: File
}

export interface Beehive {
  id: string
  latitude: number
  longitude: number
  images: Image[]
}
export interface Bucket {
  id: string
  weight: number
  dateHarvested: string
  dateProcessed: string
  purchaseDate: string
  farmer: Farmer
  type: "Light" | "Dark"
}

export interface BatchCode {
  batchCode: string
  status: "Finalised" | "In Progress"
  date: string
  country: string
  zones: string
  factoryLocation: string
  processType: string
  inspector: string
  startDate: string
  endDate: string
  type: "Light" | "Dark"
  beehives: Beehive[]
  buckets: Bucket[]
  details: BatchDetail[]
  weight: string
  waxProduced: string
  processingTransactionCode: string
  harvestingTransactionCode: string
}

export const batchCodeTraceability: BatchCode[] = [
  {
    batchCode: "EP-BI-D-23",
    status: "In Progress",
    date: "30-Dec-2024",
    country: "Zambia",
    zones: "Lusaka, Ndola, Kabushi",
    factoryLocation:
      "No : 02, Industrial Complex, Katuwana Road, Homagama, Zambia.",
    processType: "Heating, Centrifuge & 2 Filtrations",
    inspector: "<PERSON><PERSON><PERSON>",
    startDate: "1st Jan 2025",
    endDate: "6th Jan 2025",
    type: "Light",
    processingTransactionCode: "TRX-2024-098-ZM",
    harvestingTransactionCode: "TRX-2024-098-ZM",
    beehives: [
      {
        id: "1",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "2",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "3",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "4",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "5",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "6",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "7",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "8",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "9",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "10",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "11",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "12",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
    ],
    buckets: [
      {
        id: "325520",
        weight: 127.24,
        dateHarvested: "2-Jan-2025",
        dateProcessed: "4-Jan-2025",
        purchaseDate: "2-Jan-2025",
        farmer: {
          id: "1",
          firstname: "Joe",
          lastname: "Smith",
          initials: "JS",
        },
        type: "Light",
      },
      {
        id: "325521",
        weight: 123.45,
        dateHarvested: "19-Dec-2024",
        dateProcessed: "4-Jan-2025",
        purchaseDate: "2-Jan-2025",
        farmer: {
          id: "2",
          firstname: "Joe",
          lastname: "Montana",
          initials: "JM",
        },
        type: "Light",
      },
      {
        id: "325522",
        weight: 98.76,
        dateHarvested: "23-Nov-2024",
        dateProcessed: "5-Jan-2025",
        purchaseDate: "2-Jan-2025",
        farmer: {
          id: "3",
          firstname: "Anne",
          lastname: "Taylor",
          initials: "AT",
        },
        type: "Light",
      },
      {
        id: "325523",
        weight: 112.35,
        dateHarvested: "15-Dec-2024",
        dateProcessed: "5-Jan-2025",
        purchaseDate: "18-Dec-2024",
        farmer: {
          id: "4",
          firstname: "Susan",
          lastname: "Miller",
          initials: "SM",
        },
        type: "Light",
      },
      {
        id: "325524",
        weight: 105.82,
        dateHarvested: "20-Dec-2024",
        dateProcessed: "5-Jan-2025",
        purchaseDate: "22-Dec-2024",
        farmer: {
          id: "5",
          firstname: "Mike",
          lastname: "Johnson",
          initials: "MJ",
        },
        type: "Light",
      },
      {
        id: "325525",
        weight: 118.67,
        dateHarvested: "18-Dec-2024",
        dateProcessed: "5-Jan-2025",
        purchaseDate: "20-Dec-2024",
        farmer: {
          id: "6",
          firstname: "Lucy",
          lastname: "Wilson",
          initials: "LW",
        },
        type: "Light",
      },
      {
        id: "325526",
        weight: 94.23,
        dateHarvested: "22-Dec-2024",
        dateProcessed: "5-Jan-2025",
        purchaseDate: "24-Dec-2024",
        farmer: {
          id: "7",
          firstname: "John",
          lastname: "Doe",
          initials: "JD",
        },
        type: "Light",
      },
      {
        id: "325527",
        weight: 131.45,
        dateHarvested: "17-Dec-2024",
        dateProcessed: "5-Jan-2025",
        purchaseDate: "19-Dec-2024",
        farmer: {
          id: "1",
          firstname: "Joe",
          lastname: "Smith",
          initials: "JS",
        },
        type: "Light",
      },
      {
        id: "325528",
        weight: 108.92,
        dateHarvested: "21-Dec-2024",
        dateProcessed: "5-Jan-2025",
        purchaseDate: "23-Dec-2024",
        farmer: {
          id: "2",
          firstname: "Joe",
          lastname: "Montana",
          initials: "JM",
        },
        type: "Light",
      },
      {
        id: "325529",
        weight: 115.78,
        dateHarvested: "19-Dec-2024",
        dateProcessed: "5-Jan-2025",
        purchaseDate: "21-Dec-2024",
        farmer: {
          id: "3",
          firstname: "Anne",
          lastname: "Taylor",
          initials: "AT",
        },
        type: "Light",
      },
      {
        id: "325530",
        weight: 102.34,
        dateHarvested: "23-Dec-2024",
        dateProcessed: "5-Jan-2025",
        purchaseDate: "25-Dec-2024",
        farmer: {
          id: "4",
          firstname: "Susan",
          lastname: "Miller",
          initials: "SM",
        },
        type: "Light",
      },
      {
        id: "325531",
        weight: 124.56,
        dateHarvested: "16-Dec-2024",
        dateProcessed: "5-Jan-2025",
        purchaseDate: "18-Dec-2024",
        farmer: {
          id: "5",
          firstname: "Mike",
          lastname: "Johnson",
          initials: "MJ",
        },
        type: "Light",
      },
    ],
    details: [
      {
        day: "Mon",
        date: "23-Dec",
        weekNumber: 52,
        buckets: 5,
        bucketWeight: 276.76,
        waxProduced: 52.42,
        expectedWeight: 224.34,
      },
      {
        day: "Mon",
        date: "23-Dec",
        weekNumber: 52,
        buckets: 5,
        bucketWeight: 276.76,
        waxProduced: 52.42,
        expectedWeight: 224.34,
      },
      {
        day: "Tue",
        date: "24-Dec",
        weekNumber: 52,
        buckets: 3,
        bucketWeight: 150.0,
        waxProduced: 30.0,
        expectedWeight: 120.0,
      },
      {
        day: "Wed",
        date: "25-Dec",
        weekNumber: 52,
        buckets: 2,
        bucketWeight: 150.0,
        waxProduced: 20.0,
        expectedWeight: 120.0,
      },
    ],
    weight: "245.78",
    waxProduced: "87.3",
  },
  {
    batchCode: "EP-BI-D-24",
    status: "Finalised",
    date: "15-Nov-2024",
    country: "Zambia",
    zones: "Lusaka, Ndola",
    factoryLocation:
      "No : 02, Industrial Complex, Katuwana Road, Homagama, Zambia.",
    processType: "Cold Press, Single Filtration",
    inspector: "Jane Smith",
    startDate: "20th Nov 2024",
    endDate: "5th Dec 2024",
    type: "Dark",
    processingTransactionCode: "TRX-2024-098-ZM",
    harvestingTransactionCode: "TRX-2024-098-ZM",
    beehives: [
      {
        id: "1",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "2",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "3",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "4",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "5",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "6",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "7",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "8",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "9",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "10",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "11",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "12",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
    ],
    buckets: [
      {
        id: "325525",
        weight: 87.65,
        dateHarvested: "10-Nov-2024",
        dateProcessed: "21-Nov-2024",
        purchaseDate: "12-Nov-2024",
        farmer: {
          id: "6",
          firstname: "David",
          lastname: "Lee",
          initials: "DL",
        },
        type: "Dark",
      },
      {
        id: "325526",
        weight: 69.27,
        dateHarvested: "5-Nov-2024",
        dateProcessed: "22-Nov-2024",
        purchaseDate: "8-Nov-2024",
        farmer: {
          id: "7",
          firstname: "Emily",
          lastname: "Clark",
          initials: "EC",
        },
        type: "Dark",
      },
    ],
    details: [
      {
        day: "Mon",
        date: "23-Dec",
        weekNumber: 52,
        buckets: 5,
        bucketWeight: 276.76,
        waxProduced: 52.42,
        expectedWeight: 224.34,
      },
      {
        day: "Tue",
        date: "24-Dec",
        weekNumber: 52,
        buckets: 3,
        bucketWeight: 150.0,
        waxProduced: 30.0,
        expectedWeight: 120.0,
      },
      {
        day: "Wed",
        date: "25-Dec",
        weekNumber: 52,
        buckets: 2,
        bucketWeight: 150.0,
        waxProduced: 20.0,
        expectedWeight: 120.0,
      },
    ],
    weight: "156.92",
    waxProduced: "42.5",
  },
  {
    batchCode: "EP-BI-D-25",
    status: "In Progress",
    date: "10-Oct-2024",
    country: "Zambia",
    zones: "Kabushi, Kawama",
    factoryLocation: "No : 08, Processing Center, Kitwe Road, Kabushi, Zambia.",
    processType: "Heating, Centrifuge & 2 Filtrations",
    inspector: "Robert Brown",
    startDate: "15th Oct 2024",
    endDate: "30th Oct 2024",
    type: "Light",
    processingTransactionCode: "TRX-2024-098-ZM",
    harvestingTransactionCode: "TRX-2024-098-ZM",
    beehives: [
      {
        id: "1",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "2",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "3",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "4",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "5",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "6",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "7",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "8",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "9",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "10",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "11",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "12",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
    ],
    buckets: [
      {
        id: "325527",
        weight: 95.32,
        dateHarvested: "5-Oct-2024",
        dateProcessed: "16-Oct-2024",
        purchaseDate: "8-Oct-2024",
        farmer: {
          id: "8",
          firstname: "Thomas",
          lastname: "Anderson",
          initials: "TA",
        },
        type: "Light",
      },
      {
        id: "325528",
        weight: 92.33,
        dateHarvested: "3-Oct-2024",
        dateProcessed: "17-Oct-2024",
        purchaseDate: "6-Oct-2024",
        farmer: {
          id: "9",
          firstname: "Lisa",
          lastname: "Taylor",
          initials: "LT",
        },
        type: "Light",
      },
    ],
    details: [
      {
        day: "Mon",
        date: "23-Dec",
        weekNumber: 52,
        buckets: 5,
        bucketWeight: 276.76,
        waxProduced: 52.42,
        expectedWeight: 223.58,
      },
      {
        day: "Tue",
        date: "24-Dec",
        weekNumber: 52,
        buckets: 3,
        bucketWeight: 150.0,
        waxProduced: 30.0,
        expectedWeight: 120.0,
      },
      {
        day: "Wed",
        date: "25-Dec",
        weekNumber: 52,
        buckets: 2,
        bucketWeight: 150.0,
        waxProduced: 20.0,
        expectedWeight: 120.0,
      },
    ],
    weight: "187.65",
    waxProduced: "56.2",
  },
  {
    batchCode: "EP-BI-D-26",
    status: "In Progress",
    date: "5-Sep-2024",
    country: "Zambia",
    zones: "Chifubu, Ndola",
    factoryLocation: "No : 03, Honey Processing Unit, Chifubu, Zambia.",
    processType: "Cold Press, Double Filtration",
    inspector: "Sarah Wilson",
    startDate: "10th Sep 2024",
    endDate: "25th Sep 2024",
    type: "Dark",
    processingTransactionCode: "TRX-2024-098-ZM",
    harvestingTransactionCode: "TRX-2024-098-ZM",
    beehives: [
      {
        id: "1",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "2",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "3",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "4",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "5",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "6",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "7",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "8",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "9",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "10",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "11",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "12",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
    ],
    buckets: [
      {
        id: "325529",
        weight: 68.2,
        dateHarvested: "1-Sep-2024",
        dateProcessed: "11-Sep-2024",
        purchaseDate: "3-Sep-2024",
        farmer: {
          id: "10",
          firstname: "James",
          lastname: "Miller",
          initials: "JM",
        },
        type: "Dark",
      },
      {
        id: "325530",
        weight: 64.2,
        dateHarvested: "2-Sep-2024",
        dateProcessed: "12-Sep-2024",
        purchaseDate: "4-Sep-2024",
        farmer: {
          id: "11",
          firstname: "Patricia",
          lastname: "White",
          initials: "PW",
        },
        type: "Dark",
      },
    ],
    details: [
      {
        day: "Mon",
        date: "23-Dec",
        weekNumber: 52,
        buckets: 5,
        bucketWeight: 276.76,
        waxProduced: 52.42,
        expectedWeight: 224.34,
      },
      {
        day: "Tue",
        date: "24-Dec",
        weekNumber: 52,
        buckets: 3,
        bucketWeight: 150.0,
        waxProduced: 30.0,
        expectedWeight: 120.0,
      },
      {
        day: "Wed",
        date: "25-Dec",
        weekNumber: 52,
        buckets: 2,
        bucketWeight: 150.0,
        waxProduced: 20.0,
        expectedWeight: 120.0,
      },
    ],
    weight: "132.40",
    waxProduced: "38.7",
  },
  {
    batchCode: "EP-BI-D-27",
    status: "Finalised",
    date: "5-Sep-2024",
    country: "Zambia",
    zones: "Chifubu, Ndola",
    factoryLocation: "No : 03, Honey Processing Unit, Chifubu, Zambia.",
    processType: "Cold Press, Double Filtration",
    inspector: "Sarah Wilson",
    startDate: "10th Sep 2024",
    endDate: "25th Sep 2024",
    type: "Dark",
    processingTransactionCode: "TRX-2024-098-ZM",
    harvestingTransactionCode: "TRX-2024-098-ZM",
    beehives: [
      {
        id: "1",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "2",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "3",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "4",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "5",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "6",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "7",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "8",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "9",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "10",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "11",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "12",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
    ],
    buckets: [
      {
        id: "325529",
        weight: 68.2,
        dateHarvested: "1-Sep-2024",
        dateProcessed: "11-Sep-2024",
        purchaseDate: "3-Sep-2024",
        farmer: {
          id: "10",
          firstname: "James",
          lastname: "Miller",
          initials: "JM",
        },
        type: "Dark",
      },
      {
        id: "325530",
        weight: 64.2,
        dateHarvested: "2-Sep-2024",
        dateProcessed: "12-Sep-2024",
        purchaseDate: "4-Sep-2024",
        farmer: {
          id: "11",
          firstname: "Patricia",
          lastname: "White",
          initials: "PW",
        },
        type: "Dark",
      },
    ],
    details: [
      {
        day: "Mon",
        date: "23-Dec",
        weekNumber: 52,
        buckets: 5,
        bucketWeight: 276.76,
        waxProduced: 52.42,
        expectedWeight: 224.34,
      },
      {
        day: "Tue",
        date: "24-Dec",
        weekNumber: 52,
        buckets: 3,
        bucketWeight: 150.0,
        waxProduced: 30.0,
        expectedWeight: 120.0,
      },
      {
        day: "Wed",
        date: "25-Dec",
        weekNumber: 52,
        buckets: 2,
        bucketWeight: 150.0,
        expectedWeight: 120.0,
        waxProduced: 20.0,
      },
    ],
    weight: "132.40",
    waxProduced: "38.7",
  },
  {
    batchCode: "EP-BI-D-28",
    status: "In Progress",
    date: "5-Sep-2024",
    country: "Zambia",
    zones: "Chifubu, Ndola",
    factoryLocation: "No : 03, Honey Processing Unit, Chifubu, Zambia.",
    processType: "Cold Press, Double Filtration",
    inspector: "Sarah Wilson",
    startDate: "10th Sep 2024",
    endDate: "25th Sep 2024",
    type: "Dark",
    processingTransactionCode: "TRX-2024-098-ZM",
    harvestingTransactionCode: "TRX-2024-098-ZM",
    beehives: [
      {
        id: "1",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "2",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "3",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "4",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "5",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "6",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "7",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "8",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "9",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "10",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "11",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
      {
        id: "12",
        latitude: 10,
        longitude: 10,
        images: [
          {
            url: "/images/harvest-1.jpg",
          },
          {
            url: "/images/harvest-2.jpg",
          },
          {
            url: "/images/harvest-3.jpg",
          },
          {
            url: "/images/harvest-4.jpg",
          },
        ],
      },
    ],
    buckets: [
      {
        id: "325529",
        weight: 68.2,
        dateHarvested: "1-Sep-2024",
        dateProcessed: "11-Sep-2024",
        purchaseDate: "3-Sep-2024",
        farmer: {
          id: "10",
          firstname: "James",
          lastname: "Miller",
          initials: "JM",
        },
        type: "Dark",
      },
      {
        id: "325530",
        weight: 64.2,
        dateHarvested: "2-Sep-2024",
        dateProcessed: "12-Sep-2024",
        purchaseDate: "4-Sep-2024",
        farmer: {
          id: "11",
          firstname: "Patricia",
          lastname: "White",
          initials: "PW",
        },
        type: "Dark",
      },
    ],
    details: [
      {
        day: "Mon",
        date: "23-Dec",
        weekNumber: 52,
        buckets: 5,
        bucketWeight: 276.76,
        waxProduced: 52.42,
        expectedWeight: 224.34,
      },
      {
        day: "Tue",
        date: "24-Dec",
        weekNumber: 52,
        buckets: 3,
        bucketWeight: 150.0,
        waxProduced: 30.0,
        expectedWeight: 120.0,
      },
      {
        day: "Wed",
        date: "25-Dec",
        weekNumber: 52,
        buckets: 2,
        bucketWeight: 150.0,
        waxProduced: 20.0,
        expectedWeight: 120.0,
      },
    ],
    weight: "132.40",
    waxProduced: "38.7",
  },
]

// Batch Management
export interface BatchDetail {
  day: string
  date: string
  weekNumber: number
  buckets: number
  bucketWeight: number
  waxProduced: number
  expectedWeight: number
}
export interface BatchManagementListItem {
  id: string
  code: string
  status: string
  weight: string
  batchNumber: string
  zone: string
  dateGenerated: string
  isBlended: string
  details?: BatchDetail[]
  isExpanded?: boolean
}

export const availableBatchCodes = [
  "EP-BI-D-23",
  "EP-BI-D-24",
  "EP-BI-D-25",
  "EP-BI-D-26",
  "EP-BI-D-27",
  "EP-BI-D-28",
  "EP-BI-D-29",
  "EP-BI-D-30",
]

export const availableDrumIds = [
  "DRUM-001",
  "DRUM-002",
  "DRUM-003",
  "DRUM-004",
  "DRUM-005",
  "DRUM-006",
  "DRUM-007",
  "DRUM-008",
  "DRUM-009",
  "DRUM-010",
]

export const qaTesters = [
  "John Doe",
  "Marie Smith",
  "George Wilson",
  "Paul Watson",
  "Thomas Jefferson",
]

export const batchManagementList: BatchManagementListItem[] = [
  {
    id: "#4875993",
    code: "EP-BI-D-23",
    status: "In Progress",
    weight: "576.76",
    batchNumber: "#325523",
    zone: "Lilongwe",
    dateGenerated: "25-Dec-2024",
    isBlended: "Blended",
    details: [
      {
        day: "Mon",
        date: "23-Dec",
        weekNumber: 52,
        buckets: 5,
        bucketWeight: 276.76,
        waxProduced: 52.42,
        expectedWeight: 224.34,
      },
      {
        day: "Tue",
        date: "24-Dec",
        weekNumber: 52,
        buckets: 3,
        bucketWeight: 150.0,
        waxProduced: 30.0,
        expectedWeight: 120.0,
      },
      {
        day: "Wed",
        date: "25-Dec",
        weekNumber: 52,
        buckets: 2,
        bucketWeight: 150.0,
        expectedWeight: 120,
        waxProduced: 20.0,
      },
    ],
  },
  {
    id: "#4875994",
    code: "EP-BI-D-24",
    status: "Finalised",
    weight: "236.5",
    batchNumber: "#325524",
    zone: "Lilongwe",
    dateGenerated: "30-Dec-2024",
    isBlended: "Blended",
    details: [
      {
        day: "Thu",
        date: "26-Dec",
        weekNumber: 52,
        buckets: 4,
        bucketWeight: 86.5,
        waxProduced: 15.32,
        expectedWeight: 120.0,
      },
      {
        day: "Fri",
        date: "27-Dec",
        weekNumber: 52,
        buckets: 5,
        bucketWeight: 100.0,
        waxProduced: 20.0,
        expectedWeight: 120.0,
      },
      {
        day: "Sat",
        date: "28-Dec",
        weekNumber: 52,
        buckets: 3,
        bucketWeight: 50.0,
        waxProduced: 10.0,
        expectedWeight: 120.0,
      },
    ],
  },
  {
    id: "#4875995",
    code: "EP-BI-D-25",
    status: "In Progress",
    weight: "925.45",
    batchNumber: "#325525",
    zone: "Lilongwe",
    dateGenerated: "5-Jan-2025",
    isBlended: "Plain",
    details: [
      {
        day: "Mon",
        date: "30-Dec",
        weekNumber: 1,
        buckets: 15,
        bucketWeight: 325.45,
        waxProduced: 65.09,
        expectedWeight: 260.36,
      },
      {
        day: "Tue",
        date: "31-Dec",
        weekNumber: 1,
        buckets: 12,
        bucketWeight: 250.0,
        waxProduced: 50.0,
        expectedWeight: 200.0,
      },
      {
        day: "Wed",
        date: "1-Jan",
        weekNumber: 1,
        buckets: 10,
        bucketWeight: 200.0,
        waxProduced: 40.0,
        expectedWeight: 210.0,
      },
      {
        day: "Thu",
        date: "2-Jan",
        weekNumber: 1,
        buckets: 9,
        bucketWeight: 150.0,
        waxProduced: 30.0,
        expectedWeight: 120.0,
      },
    ],
  },
  {
    id: "#4875996",
    code: "EP-BI-D-26",
    status: "In Progress",
    weight: "1336.4",
    batchNumber: "#325526",
    zone: "Lilongwe",
    dateGenerated: "10-Jan-2025",
    isBlended: "Plain",
    details: [
      {
        day: "Mon",
        date: "6-Jan",
        weekNumber: 2,
        buckets: 5,
        bucketWeight: 336.4,
        waxProduced: 67.28,
        expectedWeight: 269.12,
      },
      {
        day: "Tue",
        date: "7-Jan",
        weekNumber: 2,
        buckets: 4,
        bucketWeight: 300.0,
        waxProduced: 60.0,
        expectedWeight: 240.0,
      },
      {
        day: "Wed",
        date: "8-Jan",
        weekNumber: 2,
        buckets: 4,
        bucketWeight: 350.0,
        waxProduced: 70.0,
        expectedWeight: 280.0,
      },
      {
        day: "Thu",
        date: "9-Jan",
        weekNumber: 2,
        buckets: 4,
        bucketWeight: 350.0,
        waxProduced: 70.0,
        expectedWeight: 280.0,
      },
    ],
  },
  {
    id: "#4875997",
    code: "EP-BI-D-27",
    status: "Finalised",
    weight: "907.1",
    batchNumber: "#325527",
    zone: "Lilongwe",
    dateGenerated: "15-Jan-2025",
    isBlended: "Plain",
    details: [
      {
        day: "Fri",
        date: "10-Jan",
        weekNumber: 2,
        buckets: 20,
        bucketWeight: 207.1,
        waxProduced: 41.42,
        expectedWeight: 165.68,
      },
      {
        day: "Sat",
        date: "11-Jan",
        weekNumber: 2,
        buckets: 18,
        bucketWeight: 200.0,
        waxProduced: 40.0,
        expectedWeight: 160.0,
      },
      {
        day: "Sun",
        date: "12-Jan",
        weekNumber: 2,
        buckets: 18,
        bucketWeight: 200.0,
        waxProduced: 40.0,
        expectedWeight: 160.0,
      },
      {
        day: "Mon",
        date: "13-Jan",
        weekNumber: 3,
        buckets: 20,
        bucketWeight: 300.0,
        waxProduced: 60.0,
        expectedWeight: 240.0,
      },
    ],
  },
  {
    id: "#4875998",
    code: "EP-BI-D-28",
    status: "In Progress",
    weight: "907.1",
    batchNumber: "#325528",
    zone: "Lilongwe",
    dateGenerated: "20-Jan-2025",
    isBlended: "Plain",
    details: [
      {
        day: "Tue",
        date: "14-Jan",
        weekNumber: 3,
        buckets: 10,
        bucketWeight: 207.1,
        waxProduced: 41.42,
        expectedWeight: 165.68,
      },
      {
        day: "Wed",
        date: "15-Jan",
        weekNumber: 3,
        buckets: 10,
        bucketWeight: 200.0,
        waxProduced: 40.0,
        expectedWeight: 160.0,
      },
      {
        day: "Thu",
        date: "16-Jan",
        weekNumber: 3,
        buckets: 10,
        bucketWeight: 200.0,
        waxProduced: 40.0,
        expectedWeight: 160.0,
      },
      {
        day: "Fri",
        date: "17-Jan",
        weekNumber: 3,
        buckets: 8,
        bucketWeight: 150.0,
        waxProduced: 30.0,
        expectedWeight: 120.0,
      },
      {
        day: "Sat",
        date: "18-Jan",
        weekNumber: 3,
        buckets: 7,
        bucketWeight: 150.0,
        waxProduced: 30.0,
        expectedWeight: 120.0,
      },
    ],
  },
]
