// FOR START PAGE
export type Location = {
  id: string
  name: string
}

export const ProcessTypes = [
  { id: "1", name: "Heating, Centrifuge, First and Second Filtrations" },
]

// Data for locations and staff
export const locations: Location[] = [
  {
    id: "1",
    name: "No : 02, Industrial Complex, Katuwana Road, Homagama, Zambia.",
  },
]

// Types
export type BatchStatus = "OPEN BATCH" | "CLOSED BATCH" | "IN PROCESS"
export type BatchType = "Blended" | "Plain"
export type BucketStatus = "ACTIVE" | "INACTIVE"

export interface Batch {
  id: string
  weight: number
  type: BatchType
  honeyType: string
  status: BatchStatus
  buckets: Bucket[]
}

export type Farmer = {
  id: string
  name: string
}

export interface Bucket {
  id: string
  weight: number
  type: BatchType
  dateHarvested: string
  batchId: string
  purchaseDate: string
  farmer: Farmer
}
