type: object
properties:
  id:
    type: string
    format: uuid
    description: Unique identifier for the generated link
  url:
    type: string
    format: uri
    description: The URL to access the Metabase dashboard
  dashboardId:
    type: integer
    description: The ID of the Metabase dashboard
  createdAt:
    type: string
    format: date-time
    description: When the link was created
  expiresAt:
    type: string
    format: date-time
    description: When the link will expire (if applicable)
required:
  - id
  - url
  - dashboardId
  - createdAt
  - expiresAt
