openapi: 3.0.4

info:
  title: Palmyra Pro API
  description: Alpha version of the Palmyra Pro API
  version: 0.0.1

servers:
  - url: /
  - url: https://staging-nn-api-463523546.asia-southeast1.run.app/
    description: Staging server
  - url: http://localhost:3000
    description: Local environment

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    # FIXME: We should split the definition in multiple files, but looks like swagger-ui-express does not like that.
    Error:
      $ref: "./commons/schema/errors.yaml"

    DashboardLink:
      $ref: "./dashboards/schemas/dashboard-link.yaml"

    FormsComplianceSubmissionsBeehives:
      $ref: "./forms/compliance-submissions/schemas/beehives.yaml"

    FormsComplianceSubmissionsFarmer:
      $ref: "./forms/compliance-submissions/schemas/farmer.yaml"

    FormsComplianceSubmissionsManagerInputs:
      $ref: "./forms/compliance-submissions/schemas/manager-inputs.yaml"

    FormsComplianceSubmissionsCreatedBy:
      $ref: "./forms/compliance-submissions/schemas/created-by.yaml"

    # TODO: We need to be able to extract this as well. The problem is with the refs.
    FormsComplianceSubmissions:
      type: object
      required:
        [
          id,
          name,
          createdBy,
          createdAt,
          updatedAt,
          farmer,
          beehives,
          managerInputs,
        ]
      properties:
        id:
          type: string
          minLength: 1
        name:
          type: string
          minLength: 1
        createdBy:
          $ref: "#/components/schemas/FormsComplianceSubmissionsCreatedBy"
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        farmer:
          $ref: "#/components/schemas/FormsComplianceSubmissionsFarmer"
        beehives:
          $ref: "#/components/schemas/FormsComplianceSubmissionsBeehives"
        managerInputs:
          $ref: "#/components/schemas/FormsComplianceSubmissionsManagerInputs"

    BucketInput:
      $ref: "./bucket/bucket-input.yaml"

    BucketFarmer:
      $ref: "./bucket/farmer.yaml"

    Bucket:
      type: object
      properties:
        id:
          type: string
        bucketId:
          type: string
        harvestDate:
          type: string
          format: date-time
        moistureContent:
          type: number
        weight:
          type: number
        verifiedWeight:
          type: number
        honeyType:
          type: string
        batchCode:
          type: string
        farmer:
          $ref: "#/components/schemas/BucketFarmer"
        contaminationStatus:
          type: string
          enum: ["clean", "contaminated"]
      required:
        - id
        - bucketId
        - harvestDate
        - moistureContent
        - weight
        - honeyType
        - batchCode
        - verifiedWeight

    HoneyHarvestingCreatedBy:
      $ref: "./honey-harvesting/schemas/created-by.yaml"

    HoneyHarvestingFarmer:
      $ref: "./honey-harvesting/schemas/farmer.yaml"

    HoneyHarvestingSearchResult:
      $ref: "./honey-harvesting/schemas/search-result.yaml"

    HoneyHarvestingDetailsBucket:
      $ref: "./honey-harvesting/schemas/details-bucket.yaml"

    HoneyHarvestingDetailsFactory:
      $ref: "./honey-harvesting/schemas/details-factory.yaml"

    HoneyHarvestingDetailsProcessType:
      $ref: "./honey-harvesting/schemas/details-process-type.yaml"

    HoneyHarvestingDetailsFactoryStaffLead:
      $ref: "./honey-harvesting/schemas/details-factory-staff-lead.yaml"

    HoneyProcessingLogDetails:
      type: object
      required:
        [
          id,
          batchCode,
          factorId,
          processTypeId,
          factoryStaffLeadId,
          processStartDate,
          waxKg,
          honeyKg,
          buckets,
          centrifugeTemperature,
          centrifugeSpeed,
          honeyTemperature,
          meshSize,
          pumpHopper,
          regionId,
          chiefdomId,
          zoneId,
          villageId,
        ]
      properties:
        id:
          type: string
          minimum: 1
        factory:
          $ref: "#/components/schemas/HoneyHarvestingDetailsFactory"
        processType:
          $ref: "#/components/schemas/HoneyHarvestingDetailsProcessType"
        factoryStaffLead:
          $ref: "#/components/schemas/HoneyHarvestingDetailsFactoryStaffLead"
        processStartDate:
          type: string
          format: date-time
        processEndDate:
          type: string
          format: date-time
        waxKg:
          type: number
          format: float
        centrifugeTemperature:
          type: number
          format: float
        centrifugeSpeed:
          type: number
          format: float
        honeyTemperature:
          type: number
          format: float
        meshSize:
          type: number
          format: float
        pumpHopper:
          type: number
          format: float
        honeyKg:
          type: number
          format: float
        batchCode:
          type: string
        buckets:
          type: array
          items:
            $ref: "#/components/schemas/HoneyHarvestingDetailsBucket"
        regionId:
          type: string
        chiefdomId:
          type: string
        zoneId:
          type: string
        villageId:
          type: string

    Farmer:
      $ref: "./farmers/schemas/details.yaml"

    FarmerList:
      type: object
      properties:
        results:
          type: array
          items:
            $ref: "#/components/schemas/Farmer"
        total:
          type: integer
          description: Total number of farmers
        page:
          type: integer
          description: Current page number
        pageSize:
          type: integer
          description: Number of items per page
        totalPages:
          type: integer
          description: Total number of pages

    Beehive:
      $ref: "./beehives/schemas/beehive-details.yaml"

    BeehiveList:
      type: object
      properties:
        results:
          type: array
          items:
            $ref: "#/components/schemas/Beehive"
        total:
          type: integer
          description: Total number of items
        page:
          type: integer
          description: Current page number
        pageSize:
          type: integer
          description: Number of items per page
        totalPages:
          type: integer
          description: Total number of pages

    Region:
      type: object
      properties:
        id:
          type: string
          example: "ZMB"
        name:
          type: string
          example: "Zambia"
        children:
          type: array
          items:
            $ref: "#/components/schemas/Region"

    User:
      type: object
      required:
        ["id", "externalUID", "firstName", "lastName", "email", "role", "phone"]
      properties:
        id:
          type: string
        externalUID:
          type: string
        name:
          type: string
        role:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        email:
          type: string
        phone:
          type: string
        jobTitle:
          type: string
          nullable: true
        location:
          type: string
          nullable: true
        image:
          type: string
          nullable: true

    UserUpdate:
      type: object
      required: ["firstName", "lastName", "role", "phone"]
      properties:
        id:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        role:
          type: string
        phone:
          type: string
        jobTitle:
          type: string

    BatchDetailsBeehives:
      type: object
      required: ["id", "name", "latitude", "longitude", "metadata"]
      properties:
        id:
          type: string
        name:
          type: string
        latitude:
          type: number
          format: float
        longitude:
          type: number
          format: float
        metadata:
          type: object
          description: Additional metadata for the beehive

    BatchDetailsSources:
      type: object
      required: ["farmers", "country", "region", "zone", "beehives"]
      properties:
        farmers:
          type: array
          items:
            $ref: "#/components/schemas/Farmer"
        country:
          type: string
        region:
          type: string
        zone:
          type: string
        beehives:
          type: array
          items:
            $ref: "#/components/schemas/BatchDetailsBeehives"

    BatchDetailsBuckets:
      type: object
      required: ["totalWeight", "firstSaleDate", "list"]
      properties:
        totalWeight:
          type: number
          format: float
        firstSaleDate:
          type: string
          format: date-time
        list:
          type: array
          items:
            $ref: "#/components/schemas/Bucket"

    BatchDetailsProcessing:
      required:
        [
          "factoryLocation",
          "processType",
          "startProcessDate",
          "endProcessDate",
          "finalWeightOfHoney",
          "finalWeightOfWax",
          "dailySummaries",
        ]
      properties:
        factoryLocation:
          type: string
        processType:
          type: string
        startProcessDate:
          type: string
          format: date-time
        endProcessDate:
          type: string
          format: date-time
        finalWeightOfHoney:
          type: number
          format: float
        finalWeightOfWax:
          type: number
          format: float
        dailySummaries:
          type: array
          items:
            $ref: "#/components/schemas/DailySummaryItem"

    BatchDetails:
      type: object
      required: ["isProcessed", "sources", "buckets", "processing"]
      properties:
        isProcessed:
          type: boolean
        sources:
          $ref: "#/components/schemas/BatchDetailsSources"
        buckets:
          $ref: "#/components/schemas/BatchDetailsBuckets"
        processing:
          $ref: "#/components/schemas/BatchDetailsProcessing"

    UserList:
      type: object
      properties:
        results:
          type: array
          items:
            $ref: "#/components/schemas/User"
        total:
          type: integer
          description: Total number of items
        page:
          type: integer
          description: Current page number
        pageSize:
          type: integer
          description: Number of items per page
        totalPages:
          type: integer
          description: Total number of pages

    DailySummaryItem:
      type: object
      required:
        [
          "date",
          "totalWeight",
          "numberOfBuckets",
          "waxProduced",
          "estimatedHoneyWeight",
        ]
      properties:
        date:
          type: string
          format: date-time
          description: Date of the summary
        totalWeight:
          type: number
          format: float
          description: Total weight for the day
        numberOfBuckets:
          type: integer
          description: Number of buckets for the day
        waxProduced:
          type: number
          format: float
          description: Total wax produced for the day
        estimatedHoneyWeight:
          type: number
          format: float
          description: Estimated honey weight for the day

    BatchListItem:
      type: object
      required:
        [
          "batchCode",
          "processEndDate",
          "totalWeight",
          "dateGenerated",
          "isBlended",
          "dailySummaries",
        ]
      properties:
        batchCode:
          type: string
          description: Unique identifier for the batch
        isProcessed:
          type: boolean
          description: Indicates if the batch is processed
        totalWeight:
          type: number
          format: float
          description: Total weight of the batch
        dateGenerated:
          type: string
          format: date-time
          description: Date when the batch was generated
        isBlended:
          type: boolean
          description: Indicates if the batch is blended
        dailySummaries:
          type: array
          items:
            $ref: "#/components/schemas/DailySummaryItem"

    BatchList:
      type: object
      required: ["results"]
      properties:
        results:
          type: array
          items:
            $ref: "#/components/schemas/BatchListItem"
        total:
          type: integer
          description: Total number of items
        page:
          type: integer
          description: Current page number
        pageSize:
          type: integer
          description: Number of items per page
        totalPages:
          type: integer
          description: Total number of pages

security:
  - BearerAuth: []

paths:
  /dashboard-links/{id}:
    get:
      summary: Generate a new dashboard link
      description: Creates a new link to access a published Metabase dashboard
      operationId: createDashboardLink
      parameters:
        - name: id
          in: path
          description: Metabase dashboard ID
          required: true
          schema:
            type: integer
      responses:
        "201":
          description: Dashboard link created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DashboardLink"
        default:
          description: Generic error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /forms/compliance-submissions: # Form compliance
    post:
      summary: Create a new compliance form
      description: Creates a new form
      operationId: createFormsComplianceSubmissions
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [farmerId, beehives, managerInputs]
              properties:
                farmerId:
                  type: string
                  minimum: 1
                beehives:
                  $ref: "#/components/schemas/FormsComplianceSubmissionsBeehives"
                managerInputs:
                  $ref: "#/components/schemas/FormsComplianceSubmissionsManagerInputs"
      responses:
        "201":
          description: Form created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                    description: Unique identifier for new complaince form
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

    get:
      summary: Search compliance form submissions
      operationId: searchFormsComplianceSubmissions
      parameters:
        - name: query
          in: query
          description: Fuzzy search query text
          required: false
          schema:
            type: string
        - name: createdAtFrom
          in: query
          description: Filter created after this date, inclusive
          required: false
          schema:
            type: string
            format: date-time
        - name: createdAtTo
          in: query
          description: Filter created before this date, exclusive
          required: false
          schema:
            type: string
            format: date-time
        - name: sortBy
          in: query
          description: Short option
          required: false
          schema:
            type: string
            #            default: "newestFirst",
            enum: ["oldestFirst", "newestFirst"]
        - name: page
          in: query
          description: Page number
          required: false
          schema:
            type: integer
            default: 1
        - name: pageSize
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            default: 10
      responses:
        "200":
          description: Search results
          content:
            application/json:
              schema:
                type: object
                properties:
                  results:
                    type: array
                    items:
                      $ref: "#/components/schemas/FormsComplianceSubmissions"
                  total:
                    type: integer
                    description: Total number of farmers
                  page:
                    type: integer
                    description: Current page number
                  pageSize:
                    type: integer
                    description: Number of items per page
                  totalPages:
                    type: integer
                    description: Total number of pages
        default:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /forms/compliance-submissions/{id}:
    get:
      summary: Get a form by ID
      description: Returns a form by ID
      operationId: getFormsComplianceSubmissionById
      parameters:
        - name: id
          in: path
          description: Form ID
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Form found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FormsComplianceSubmissions"
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  #    put:
  #      summary: Update a form
  #      description: Updates an existing form
  #      operationId: updateForm
  #      parameters:
  #        - name: id
  #          in: path
  #          description: Form ID
  #          required: true
  #          schema:
  #            type: string
  #      requestBody:
  #        required: true
  #        content:
  #          application/json:
  #            schema:
  #              $ref: '#/components/schemas/FormUpdateRequest'
  #      responses:
  #        '200':
  #          description: Form updated successfully
  #          content:
  #            application/json:
  #              schema:
  #                $ref: '#/components/schemas/Form'
  #        '400':
  #          description: Invalid request
  #          content:
  #            application/json:
  #              schema:
  #                $ref: '#/components/schemas/Error'
  #        '401':
  #          description: Unauthorized
  #          content:
  #            application/json:
  #              schema:
  #                $ref: '#/components/schemas/Error'
  #        '404':
  #          description: Form not found
  #          content:
  #            application/json:
  #              schema:
  #                $ref: '#/components/schemas/Error'
  #    delete:
  #      summary: Delete a form
  #      description: Deletes an existing form
  #      operationId: deleteForm
  #      parameters:
  #        - name: id
  #          in: path
  #          description: Form ID
  #          required: true
  #          schema:
  #            type: string
  #      responses:
  #        '204':
  #          description: Form deleted successfully
  #        '401':
  #          description: Unauthorized
  #          content:
  #            application/json:
  #              schema:
  #                $ref: '#/components/schemas/Error'
  #        '404':
  #          description: Form not found
  #          content:
  #            application/json:
  #              schema:
  #                $ref: '#/components/schemas/Error'

  #  /farmers/{id}:
  #    get:
  #      summary: Get a farmer by ID
  #      description: Returns a farmer by ID
  #      operationId: getFarmerById
  #      parameters:
  #        - name: id
  #          in: path
  #          description: Farmer ID
  #          required: true
  #          schema:
  #            type: string
  #      responses:
  #        '200':
  #          description: Farmer found
  #          content:
  #            application/json:
  #              schema:
  #                $ref: '#/components/schemas/Farmer'
  #        '401':
  #          description: Unauthorized
  #          content:
  #            application/json:
  #              schema:
  #                $ref: '#/components/schemas/Error'
  #        '404':
  #          description: Farmer not found
  #          content:
  #            application/json:
  #              schema:
  #                $ref: '#/components/schemas/Error'

  /honey-harvesting:
    post:
      summary: Create a new honey harvesting transaction
      description: Creates a new honey harvesting transaction
      operationId: createHoneyHarvesting
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                [
                  farmerId,
                  beehives,
                  saleDate,
                  pricePerKg,
                  regionId,
                  chiefdomId,
                  zoneId,
                  villageId,
                  honeyType,
                  buckets,
                  createdBy,
                ]
              properties:
                farmerId:
                  type: string
                  minimum: 1
                beehives:
                  type: array
                saleDate:
                  type: string
                  format: date-time
                pricePerKg:
                  type: number
                  format: float
                regionId:
                  type: string
                chiefdomId:
                  type: string
                zoneId:
                  type: string
                villageId:
                  type: string
                buckets:
                  type: array
                  items:
                    $ref: "#/components/schemas/BucketInput"
                createdBy:
                  type: string
      responses:
        "201":
          description: Honey harvesting data created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                    description: Unique identifier for a new honey harvesting transaction
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

    get:
      summary: Search honey harvesting transaction
      operationId: searchHoneyHarvestingTransactions
      parameters:
        - name: query
          in: query
          description: Fuzzy search query text
          required: false
          schema:
            type: string
        - name: createdAtFrom
          in: query
          description: Filter created after this date, inclusive
          required: false
          schema:
            type: string
            format: date-time
        - name: createdAtTo
          in: query
          description: Filter created before this date, exclusive
          required: false
          schema:
            type: string
            format: date-time
        - name: sortBy
          in: query
          description: Short option
          required: false
          schema:
            type: string
            # default: "newestFirst",
            enum: ["oldestFirst", "newestFirst"]
        - name: page
          in: query
          description: Page number
          required: false
          schema:
            type: integer
            default: 1
        - name: pageSize
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            default: 10
      responses:
        "200":
          description: Search results
          content:
            application/json:
              schema:
                type: object
                properties:
                  results:
                    type: array
                    items:
                      $ref: "#/components/schemas/HoneyHarvestingSearchResult"
                  total:
                    type: integer
                    description: Total number of farmers
                  page:
                    type: integer
                    description: Current page number
                  pageSize:
                    type: integer
                    description: Number of items per page
                  totalPages:
                    type: integer
                    description: Total number of pages
        default:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /honey-harvesting/{id}:
    get:
      summary: Get honey harvesting transaction by ID
      description: Returns a honey harvesting transaction by ID
      operationId: getHoneyHarvestingById
      parameters:
        - name: id
          in: path
          description: Honey harvesting transaction ID
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Honey harvesting transaction found
          content:
            application/json:
              schema:
                type: object
                required:
                  [
                    id,
                    regionId,
                    chiefdomId,
                    zoneId,
                    villageId,
                    farmer,
                    beehives,
                    saleDate,
                    pricePerKg,
                    buckets,
                    createdBy,
                  ]
                properties:
                  id:
                    type: string
                  regionId:
                    type: string
                  chiefdomId:
                    type: string
                  zoneId:
                    type: string
                  villageId:
                    type: string
                  farmer:
                    $ref: "#/components/schemas/HoneyHarvestingFarmer"
                  beehives:
                    type: array
                    items:
                      type: string
                  saleDate:
                    type: string
                    format: date-time
                  pricePerKg:
                    type: number
                    format: float
                  buckets:
                    type: array
                    items:
                      $ref: "#/components/schemas/Bucket"
                  createdBy:
                    $ref: "#/components/schemas/HoneyHarvestingCreatedBy"
                  ipfsHash:
                    type: string
                  palmyraHash:
                    type: string
                  createdAt:
                    type: string
                    format: date-time
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /farmers:
    post:
      summary: Create a new farmer
      description: Creates a farmer
      operationId: createFarmer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./farmers/schemas/creation_update.yaml"

      responses:
        "201":
          description: Farmer created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                    description: Unique identifier for the new farmer
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
    get:
      summary: Get farmers
      description: Get farmers with filters
      operationId: getFarmers
      parameters:
        - name: query
          in: query
          description: Fuzzy search query text
          required: false
          schema:
            type: string
        - name: businessId
          in: query
          description: Filter by business ID
          required: false
          schema:
            type: string
        - name: createdBy
          in: query
          description: Filter by creator ID
          required: false
          schema:
            type: string
        - name: createdAtFrom
          in: query
          description: Filter created after this date, inclusive
          required: false
          schema:
            type: string
            format: date-time
        - name: createdAtTo
          in: query
          description: Filter created before this date, exclusive
          required: false
          schema:
            type: string
            format: date-time
        - name: page
          in: query
          description: Page number
          required: false
          schema:
            type: integer
            default: 1
        - name: pageSize
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            default: 10

      responses:
        "200":
          description: Search results
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FarmerList"
        default:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /farmers/offline:
    get:
      summary: Download all farmers for offline usage.
      operationId: downloadFarmersOffline
      responses:
        "200":
          description: Search results
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FarmerList"

        default:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /farmers/{id}:
    delete:
      summary: Soft delete Farmer by ID
      description: Execute a soft delete of the farmer.
      operationId: deleteFarmerById
      parameters:
        - name: id
          in: path
          description: Farmer ID
          required: true
          schema:
            type: string
      responses:
        "204":
          description: Farmer deleted
        404:
          description: Farmer not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
    post:
      summary: Update a existing farmer
      description: Update a existing farmer
      operationId: updateFarmer
      parameters:
        - name: id
          in: path
          description: Farmer ID
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./farmers/schemas/creation_update.yaml"

      responses:
        "200":
          description: Farmer created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                    description: Unique identifier for the new farmer
        404:
          description: Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
    get:
      summary: Get Farmer by ID
      description: Returns a farmer by ID
      operationId: getFarmerById
      parameters:
        - name: id
          in: path
          description: Farmer ID
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Farmer found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Farmer"
        404:
          description: Farmer not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /farmers/search:
    get:
      summary: Search farmers
      description: Search farmers with filters
      operationId: searchFarmers
      parameters:
        - name: query
          in: query
          description: Fuzzy search query text
          required: false
          schema:
            type: string
        - name: businessId
          in: query
          description: Filter by business ID
          required: false
          schema:
            type: string
        - name: createdBy
          in: query
          description: Filter by creator ID
          required: false
          schema:
            type: string
        - name: createdAtFrom
          in: query
          description: Filter created after this date, inclusive
          required: false
          schema:
            type: string
            format: date-time
        - name: createdAtTo
          in: query
          description: Filter created before this date, exclusive
          required: false
          schema:
            type: string
            format: date-time
        - name: page
          in: query
          description: Page number
          required: false
          schema:
            type: integer
            default: 1
        - name: pageSize
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            default: 10

      responses:
        "200":
          description: Search results
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FarmerList"
        default:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /beehives:
    post:
      summary: Create a new beehive
      description: Creates a beehive
      operationId: createBeehive
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./beehives/schemas/beehive-creation_update.yaml"

      responses:
        "201":
          description: Beehive created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                    description: Unique identifier for the new beehive
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

    get:
      summary: Get beehive
      description: Get farmers with beehive
      operationId: getBeehives
      parameters:
        - name: query
          in: query
          description: Fuzzy search query text
          required: false
          schema:
            type: string
        - name: farmerId
          in: query
          description: Owner id
          required: false
          schema:
            type: string
        - name: createdAtFrom
          in: query
          description: Filter created after this date, inclusive
          required: false
          schema:
            type: string
            format: date-time
        - name: createdAtTo
          in: query
          description: Filter created before this date, exclusive
          required: false
          schema:
            type: string
            format: date-time
        - name: page
          in: query
          description: Page number
          required: false
          schema:
            type: integer
            default: 1
        - name: pageSize
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            default: 10

      responses:
        "200":
          description: Search results
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BeehiveList"
        default:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /beehives/{id}:
    delete:
      summary: Soft delete Beehive by ID
      description: Execute a soft delete of the beehive.
      operationId: deleteBeehiveById
      parameters:
        - name: id
          in: path
          description: Beehive ID
          required: true
          schema:
            type: string
      responses:
        "204":
          description: Beehive deleted
        404:
          description: Beehive not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
    post:
      summary: Update a existing Beehive
      description: Update a existing Beehive
      operationId: updateBeehive
      parameters:
        - name: id
          in: path
          description: Beehive ID
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./beehives/schemas/beehive-creation_update.yaml"

      responses:
        "200":
          description: Beehive created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                    description: Unique identifier for the new Beehive
        404:
          description: Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
    get:
      summary: Get Beehive by ID
      description: Returns a Beehive by ID
      operationId: getBeehiveById
      parameters:
        - name: id
          in: path
          description: Beehive ID
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Farmer found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Beehive"
        404:
          description: Farmer not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  #  /beehives:
  #    get:
  #      summary: Get beehives by farmer ID
  #      description: Retrieves a list of beehives that belong to a specific farmer.
  #      operationId: getBeehivesByFarmerId
  #      parameters:
  #        - name: farmerId
  #          in: query
  #          required: true
  #          description: The ID of the farmer to filter beehives by.
  #          schema:
  #            type: string
  #      responses:
  #        "200":
  #          description: A list of beehives belonging to the specified farmer.
  #          content:
  #            application/json:
  #              schema:
  #                type: array
  #                items:
  #                  $ref: "#/components/schemas/Beehive"
  #        "400":
  #          description: Invalid request, missing farmerId query parameter.
  #        "404":
  #          description: No beehives found for the given farmer ID.
  #        default:
  #          description: Unexpected error
  #          content:
  #            application/json:
  #              schema:
  #                $ref: "#/components/schemas/Error"

  /honey-processing:
    post:
      summary: Create a new honey processing log
      description: Creates a new honey processing log
      operationId: createHoneyProcessingLog
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                [
                  factorId,
                  processTypeId,
                  factoryStaffLeadId,
                  processStartDate,
                  buckets,
                ]
              properties:
                factorId:
                  type: string
                  minimum: 1
                processTypeId:
                  type: string
                  minimum: 1
                factoryStaffLeadId:
                  type: string
                  minimum: 1
                processStartDate:
                  type: string
                  format: date-time
                processEndDate:
                  type: string
                  format: date-time
                waxKg:
                  type: number
                  format: float
                centrifugeTemperature:
                  type: number
                  format: float
                centrifugeSpeed:
                  type: number
                  format: float
                honeyTemperature:
                  type: number
                  format: float
                meshSize:
                  type: number
                  format: float
                pumpHopper:
                  type: number
                  format: float
                honeyKg:
                  type: number
                  format: float
                buckets:
                  type: array
                  items:
                    type: string
      responses:
        "201":
          description: Honey processing log created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                    description: Unique identifier for a new honey processing log created
                  batchCode:
                    type: string
                    description: Unique identifier for a new honey processing batch created
                required:
                  - id
                  - batchCode
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

    get:
      summary: Search honey processing logs
      operationId: searchHoneyProcessingLog
      parameters:
        - name: query
          in: query
          description: Fuzzy search query text
          required: false
          schema:
            type: string
        - name: processStartTSFrom
          in: query
          description: Filter processes started after this date, inclusive
          required: false
          schema:
            type: string
            format: date-time
        - name: processStartTSTo
          in: query
          description: Filter processes started before this date, exclusive
          required: false
          schema:
            type: string
            format: date-time
        - name: onlyOpen
          in: query
          description: Exclude processes that are closed
          required: false
          schema:
            type: boolean
            default: false
        - name: sortBy
          in: query
          description: Short option
          required: false
          schema:
            type: string
            #            default: "newestFirst",
            enum: ["oldestFirst", "newestFirst"]
        - name: page
          in: query
          description: Page number
          required: false
          schema:
            type: integer
            default: 1
        - name: pageSize
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            default: 10
      responses:
        "200":
          description: Search results
          content:
            application/json:
              schema:
                type: object
                properties:
                  results:
                    type: array
                    items:
                      $ref: "#/components/schemas/HoneyProcessingLogDetails"
                  total:
                    type: integer
                    description: Total number of processes
                  page:
                    type: integer
                    description: Current page number
                  pageSize:
                    type: integer
                    description: Number of items per page
                  totalPages:
                    type: integer
                    description: Total number of pages
        default:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /honey-processing/{id}:
    get:
      summary: Get honey harvesting processing by ID
      description: Returns a honey processing by ID
      operationId: getHoneyProcessingById
      parameters:
        - name: id
          in: path
          description: Honey harvesting processing log ID
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Honey harvesting transaction found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/HoneyProcessingLogDetails"
        "404":
          description: Not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

    post:
      summary: Update the Create a new honey processing log
      description: Update a new honey processing log
      operationId: updateHoneyProcessingLog
      parameters:
        - name: id
          in: path
          description: Honey harvesting processing log ID
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                processEndDate:
                  type: string
                  format: date-time
                waxKg:
                  type: number
                  format: float
                centrifugeTemperature:
                  type: number
                  format: float
                centrifugeSpeed:
                  type: number
                  format: float
                honeyTemperature:
                  type: number
                  format: float
                meshSize:
                  type: number
                  format: float
                pumpHopper:
                  type: number
                  format: float
                honeyKg:
                  type: number
                  format: float
                buckets:
                  type: object
                  additionalProperties:
                    type: object
                    properties:
                      verifiedWeight:
                        type: number
                        format: float
                      contaminationStatus:
                        type: string
                        enum: ["clean", "contaminated"]
      responses:
        "200":
          description: Honey processing log updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                    description: Unique identifier for a updated honey processing log created
                  batchCode:
                    type: string
                    description: Unique identifier for a updated honey processing batch created
                required:
                  - id
                  - batchCode
        "404":
          description: Not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /regions:
    get:
      summary: Get all regions in Zambia
      operationId: getAllRegions
      responses:
        "200":
          description: A nested list of regions
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Region"
        "400":
          description: Invalid request
        default:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /regions/tree:
    get:
      summary: Get the full tree in Zambia
      operationId: getAllRegionsTree
      responses:
        "200":
          description: A nested list of regions
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Region"
        "400":
          description: Invalid request
        default:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /regions/{id}/chiefdoms:
    get:
      summary: Get all chiefdoms by region ID
      operationId: getAllChiefdomsByRegionId
      parameters:
        - name: id
          in: path
          required: true
          description: The ID of the region to filter chiefdoms by.
          schema:
            type: string
      responses:
        "200":
          description: List of chiefdoms in the specified region
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Region"
        "400":
          description: Invalid request, missing or invalid ID.
        "404":
          description: Region not found for the given ID.
        default:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /regions/{id}/zones:
    get:
      summary: Get all zones by region ID
      operationId: getAllZonesByRegionId
      parameters:
        - name: id
          in: path
          required: true
          description: The ID of the region to filter zones by.
          schema:
            type: string
      responses:
        "200":
          description: List of zones in the specified region
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Region"
        "400":
          description: Invalid request, missing or invalid ID.
        "404":
          description: Region not found for the given ID.
        default:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /chiefdoms/{id}/zones:
    get:
      summary: Get all zones by chiefdom ID
      operationId: getAllZonesByChiefdomId
      parameters:
        - name: id
          in: path
          required: true
          description: The ID of the chiefdom to filter zones by.
          schema:
            type: string
      responses:
        "200":
          description: List of zones in the specified chiefdom
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Region"
        "400":
          description: Invalid request, missing or invalid ID.
        "404":
          description: Chiefdom not found for the given ID.
        default:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /zones/{id}/villages:
    get:
      summary: Get all villages by zone ID
      operationId: getAllVillagesByZoneId
      parameters:
        - name: id
          in: path
          required: true
          description: The ID of the zone to filter villages by.
          schema:
            type: string
      responses:
        "200":
          description: List of villages in the specified zone
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Region"
        "400":
          description: Invalid request, missing or invalid ID.
        "404":
          description: Zone not found for the given ID.
        default:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /buckets:
    get:
      summary: Get all buckets
      operationId: getAllBuckets
      parameters:
        - name: region
          in: query
          required: false
          description: Filter buckets by region
          schema:
            type: string
        - name: chiefdom
          in: query
          required: false
          description: Filter buckets by chiefdom
          schema:
            type: string
        - name: zone
          in: query
          required: false
          description: Filter buckets by zone
          schema:
            type: string
        - name: village
          in: query
          required: false
          description: Filter buckets by village
          schema:
            type: string
        - name: honeyType
          in: query
          required: false
          description: Filter buckets by honey type
          schema:
            type: string
            enum: ["light", "dark"]
        - name: query
          in: query
          required: false
          description: Fuzzy search query text
          schema:
            type: string
      responses:
        "200":
          description: A list of buckets
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Bucket"
        "400":
          description: Invalid request
        default:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /users:
    get:
      summary: Search Users
      operationId: SearchUsers
      parameters:
        - name: query
          in: query
          description: Fuzzy search query text
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Page number
          required: false
          schema:
            type: integer
            default: 1
        - name: pageSize
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            default: 10
        - name: role
          in: query
          description: Filter users by one or more roles
          required: false
          schema:
            type: array
            items:
              type: string
            style: form
            explode: true
      responses:
        "200":
          description: A list of users
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserList"
        "400":
          description: Invalid request
        default:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
    post:
      summary: Create a new user
      description: Creates a user
      operationId: CreateUser
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./auth/schemas/creation_update.yaml"
      responses:
        "201":
          description: User created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                    description: Unique identifier for the new user
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /users/{id}:
    delete:
      summary: Soft delete User by ID
      description: Execute a soft delete of the user.
      operationId: deleteUserById
      parameters:
        - name: id

          in: path
          description: User ID
          required: true
          schema:
            type: string
      responses:
        "204":
          description: User deleted
        404:
          description: User not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
    post:
      summary: Update a existing user
      description: Update a existing user
      operationId: updateUser
      parameters:
        - name: id
          in: path
          description: User ID
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UserUpdate"

      responses:
        "200":
          description: User created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                    description: Unique identifier for the new user
        404:
          description: Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
    get:
      summary: Get User by ID
      description: Returns a user by ID
      operationId: getUserById
      parameters:
        - name: id
          in: path
          description: User ID
          required: true
          schema:
            type: string
      responses:
        "200":
          description: User found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/User"
        404:
          description: User not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /batches:
    get:
      summary: Search all batches
      operationId: searchBatches
      parameters:
        - name: query
          in: query
          description: Fuzzy search query text
          required: false
          schema:
            type: string
        - name: createdAtFrom
          in: query
          description: Filter created after this date, inclusive
          required: false
          schema:
            type: string
            format: date-time
        - name: createdAtTo
          in: query
          description: Filter created before this date, exclusive
          required: false
          schema:
            type: string
            format: date-time
        - name: page
          in: query
          description: Page number
          required: false
          schema:
            type: integer
            default: 1
        - name: pageSize
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            default: 10
        - name: sortBy
          in: query
          description: Short option
          required: false
          schema:
            type: string
            enum: ["oldestFirst", "newestFirst"]
      responses:
        "200":
          description: A list of batches
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BatchList"
        "400":
          description: Invalid request
        default:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /batches/{batchCode}:
    get:
      summary: Get information regarding batch by ID
      operationId: getBatchByCode
      parameters:
        - name: batchCode
          in: path
          required: true
          description: The batch code of the batch to retrieve.
          schema:
            type: string
      responses:
        "200":
          description: Batch details
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BatchDetails"
        "400":
          description: Invalid request, missing or invalid ID.
        "404":
          description: Batch not found for the given ID.
        default:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
