type: object
required: [id, origin, createdAt, moisture, bucketCount, honeyType, totalWeight, pricePerKg, amountPaid]
properties:
  id:
    type: string
  region:
    type: string
  chiefdom:
    type: string
  zone:
    type: string
  village:
    type: string
  createdAt:
    type: string
    format: date-time
  moistureRange:
    type: string
  bucketCount:
    type: integer
  honeyType:
    type: string
    enum: ["light", "dark"]
  totalWeight:
    type: number
    format: float
  pricePerKg:
    type: number
    format: float
  amountPaid:
    type: number
    format: float   
