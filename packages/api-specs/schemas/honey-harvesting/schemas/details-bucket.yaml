type: object
properties:
  id:
    type: string
    description: "Unique identifier for the bucket contents."
  bucketId:
    type: string
    description: "Physical identifier for the actual bucket."
  harvestDate:
    type: string
    format: date-time
    description: "Date when the honey was harvested."
  honeyType:
    type: string
    description: "Type of honey collected in the bucket."
  moistureContent:
    type: number
    format: float
    description: "Moisture content percentage of the honey."
  weight:
    type: number
    format: float
    description: "Weight of the honey in the bucket."
  verifiedWeight:
    type: number
  contaminationStatus:
    type: string
    enum: ["clean", "contaminated"]
    description: "Status indicating if the honey is clean or contaminated."
required:
  - bucketId
  - harvestDate
  - honeyType
  - moistureContent
  - weight
  - verifiedWeight
