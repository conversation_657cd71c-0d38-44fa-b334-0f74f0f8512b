type: object
required:
  - numberPresent
  - visitedMonthly
  - visitedRecently
  - areAnyOccupied
  - awayFromWater
  - awayFromCrops
  - adequateFlowering
  - readyForHarvest
  - rebaitMaterial
  - enoughHoneyForWinter
  - beesWereMutilated
  - organicWaxUsed
  - provideFoodForBees
  - hasInfestation
properties:
  numberPresent:
    type: string
    minLength: 1
  visitedMonthly:
    type: boolean
  visitedRecently:
    type: array
    items:
      type: string
  areAnyOccupied:
    type: boolean
  awayFromWater:
    type: boolean
  awayFromCrops:
    type: boolean
  adequateFlowering:
    type: boolean
  readyForHarvest:
    type: string
    minLength: 1
  rebaitMaterial:
    type: string
    minLength: 1
  enoughHoneyForWinter:
    type: boolean
  beesWereMutilated:
    type: boolean
  organicWaxUsed:
    type: boolean
  provideFoodForBees:
    type: boolean
  hasInfestation:
    type: boolean
  infestationDetails:
    type: string
    maxLength: 250
    nullable: true
