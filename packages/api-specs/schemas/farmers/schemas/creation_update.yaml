type: object
required:
  [
    firstName,
    lastName,
    gender,
    phone,
    maritalStatus,
    householdSize,
    estimatedAnnualIncome,
    sourceOfIncome,
    nrc,
    role,
    countryId,
    regionId,
    chiefdomId,
    zoneId,
    villageId,
  ]
properties:
  firstName:
    type: string
    description: First name of the farmer
  lastName:
    type: string
    description: Last name of the farmer
  gender:
    type: string
    description: Gender of the farmer
  phone:
    type: string
    description: Phone number of the farmer
  maritalStatus:
    type: string
    description: Marital status of the farmer
  dob:
    type: string
    description: Date of birth of the farmer
    format: date
  householdSize:
    type: string
    description: Size of the farmer's household
  estimatedAnnualIncome:
    type: number
    format: float
    description: Estimated annual income of the farmer
  sourceOfIncome:
    type: string
    description: Source of income for the farmer
  nrc:
    type: string
    description: National ID number

  role:
    type: string
    description: ID of the role associated with the farmer

  countryId:
    type: string
    description: ID of the country associated with the farmer
  regionId:
    type: string
    description: Farmer's region
  chiefdomId:
    type: string
    description: Farmer's chiefdom
  zoneId:
    type: string
    description: Farmer's zone
  villageId:
    type: string
    description: Village's zone

  #        userId:
  #          type: string
  #          description: ID of the user associated with the farmer
  #        farmId:
  #          type: string
  #          description: ID of the farm associated with the farmer
  #        metadata:
  #          type: object
  #          description: Additional metadata for the farmer
