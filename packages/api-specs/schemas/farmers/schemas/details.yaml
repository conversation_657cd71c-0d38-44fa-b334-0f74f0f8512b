type: object
required:
  - id
  - firstName
  - lastName
  - gender
  - phone
  - maritalStatus
  - householdSize
  - estimatedAnnualIncome
  - sourceOfIncome
  - roleId
  - roleDisplayName
  - countryId
  - countryName
  - regionId
  - chiefdomId
  - zoneId
  - villageId
  - nrc
  - createdAt
  - updatedAt
properties:
  id:
    type: string
    description: Unique identifier for the farmer
  firstName:
    type: string
    description: First name of the farmer
  lastName:
    type: string
    description: Last name of the farmer
  gender:
    type: string
    description: Gender of the farmer
  phone:
    type: string
    description: Phone number of the farmer
  maritalStatus:
    type: string
    description: Marital status of the farmer
  dob:
    type: string
    format: date
    description: Date of birth of the farmer
  householdSize:
    type: string
    description: Size of the farmer's household
  estimatedAnnualIncome:
    type: number
    format: float
    description: Estimated annual income of the farmer
  sourceOfIncome:
    type: string
    description: Source of income for the farmer
  roleId:
    type: string
    description: ID of the role associated with the farmer
  roleDisplayName:
    type: string
    description: Display name of the role associated with the farmer
  countryId:
    type: string
    description: ID of the country associated with the farmer
  countryName:
    type: string
    description: Display name of the country associated with the farmer
  regionId:
    type: string
    description: ID of the region associated with the farmer
  chiefdomId:
    type: string
    description: Farmer's chiefdom
  zoneId:
    type: string
    description: Farmer's zone id
  villageId:
    type: string
    description: Village's village id
  nrc:
    type: string
    description: National ID number
  createdAt:
    type: string
    format: date-time
    description: When the farmer was created
  updatedAt:
    type: string
    format: date-time
    description: When the farmer was last updated
  beehives:
    type: array
    items:
      type: object
      required: [id, name]
      properties:
        id:
          type: string
        name:
          type: string
