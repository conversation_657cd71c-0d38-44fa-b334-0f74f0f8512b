type: object
required: [
  id, name, status, latitude, longitude, farmerId, isFunctional, needsToBeRepaired, isOccupied, farmerId
]

properties:
  id:
    type: string
    description: Beehive id
  name:
    type: string
    description: Beehive name
  status:
    type: string
  latitude:
    type: number
    format: float
  longitude:
    type: number
    format: float
  isFunctional:
    type: boolean
  needsToBeRepaired:
    type: boolean
  isOccupied:
    type: boolean
  observations:
    type: string
  farmerId:
    type: string
