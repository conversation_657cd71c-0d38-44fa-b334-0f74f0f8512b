/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
    "/dashboard-links/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Generate a new dashboard link
         * @description Creates a new link to access a published Metabase dashboard
         */
        get: operations["createDashboardLink"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/forms/compliance-submissions": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Search compliance form submissions */
        get: operations["searchFormsComplianceSubmissions"];
        put?: never;
        /**
         * Create a new compliance form
         * @description Creates a new form
         */
        post: operations["createFormsComplianceSubmissions"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/forms/compliance-submissions/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get a form by ID
         * @description Returns a form by ID
         */
        get: operations["getFormsComplianceSubmissionById"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/honey-harvesting": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Search honey harvesting transaction */
        get: operations["searchHoneyHarvestingTransactions"];
        put?: never;
        /**
         * Create a new honey harvesting transaction
         * @description Creates a new honey harvesting transaction
         */
        post: operations["createHoneyHarvesting"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/honey-harvesting/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get honey harvesting transaction by ID
         * @description Returns a honey harvesting transaction by ID
         */
        get: operations["getHoneyHarvestingById"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/farmers": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get farmers
         * @description Get farmers with filters
         */
        get: operations["getFarmers"];
        put?: never;
        /**
         * Create a new farmer
         * @description Creates a farmer
         */
        post: operations["createFarmer"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/farmers/offline": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Download all farmers for offline usage. */
        get: operations["downloadFarmersOffline"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/farmers/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Farmer by ID
         * @description Returns a farmer by ID
         */
        get: operations["getFarmerById"];
        put?: never;
        /**
         * Update a existing farmer
         * @description Update a existing farmer
         */
        post: operations["updateFarmer"];
        /**
         * Soft delete Farmer by ID
         * @description Execute a soft delete of the farmer.
         */
        delete: operations["deleteFarmerById"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/farmers/search": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Search farmers
         * @description Search farmers with filters
         */
        get: operations["searchFarmers"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/beehives": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get beehive
         * @description Get farmers with beehive
         */
        get: operations["getBeehives"];
        put?: never;
        /**
         * Create a new beehive
         * @description Creates a beehive
         */
        post: operations["createBeehive"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/beehives/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Beehive by ID
         * @description Returns a Beehive by ID
         */
        get: operations["getBeehiveById"];
        put?: never;
        /**
         * Update a existing Beehive
         * @description Update a existing Beehive
         */
        post: operations["updateBeehive"];
        /**
         * Soft delete Beehive by ID
         * @description Execute a soft delete of the beehive.
         */
        delete: operations["deleteBeehiveById"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/honey-processing": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Search honey processing logs */
        get: operations["searchHoneyProcessingLog"];
        put?: never;
        /**
         * Create a new honey processing log
         * @description Creates a new honey processing log
         */
        post: operations["createHoneyProcessingLog"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/honey-processing/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get honey harvesting processing by ID
         * @description Returns a honey processing by ID
         */
        get: operations["getHoneyProcessingById"];
        put?: never;
        /**
         * Update the Create a new honey processing log
         * @description Update a new honey processing log
         */
        post: operations["updateHoneyProcessingLog"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/regions": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Get all regions in Zambia */
        get: operations["getAllRegions"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/regions/tree": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Get the full tree in Zambia */
        get: operations["getAllRegionsTree"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/regions/{id}/chiefdoms": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Get all chiefdoms by region ID */
        get: operations["getAllChiefdomsByRegionId"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/regions/{id}/zones": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Get all zones by region ID */
        get: operations["getAllZonesByRegionId"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/chiefdoms/{id}/zones": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Get all zones by chiefdom ID */
        get: operations["getAllZonesByChiefdomId"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/zones/{id}/villages": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Get all villages by zone ID */
        get: operations["getAllVillagesByZoneId"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/buckets": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Get all buckets */
        get: operations["getAllBuckets"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/users": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Search Users */
        get: operations["SearchUsers"];
        put?: never;
        /**
         * Create a new user
         * @description Creates a user
         */
        post: operations["CreateUser"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/users/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get User by ID
         * @description Returns a user by ID
         */
        get: operations["getUserById"];
        put?: never;
        /**
         * Update a existing user
         * @description Update a existing user
         */
        post: operations["updateUser"];
        /**
         * Soft delete User by ID
         * @description Execute a soft delete of the user.
         */
        delete: operations["deleteUserById"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/batches": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Search all batches */
        get: operations["searchBatches"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/batches/{batchCode}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Get information regarding batch by ID */
        get: operations["getBatchByCode"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
}
export type webhooks = Record<string, never>;
export interface components {
    schemas: {
        Error: components["schemas"]["errors"];
        DashboardLink: components["schemas"]["dashboard-link"];
        FormsComplianceSubmissionsBeehives: components["schemas"]["beehives"];
        FormsComplianceSubmissionsFarmer: components["schemas"]["farmer"];
        FormsComplianceSubmissionsManagerInputs: components["schemas"]["manager-inputs"];
        FormsComplianceSubmissionsCreatedBy: components["schemas"]["created-by"];
        FormsComplianceSubmissions: {
            id: string;
            name: string;
            createdBy: components["schemas"]["created-by"];
            /** Format: date-time */
            createdAt: string;
            /** Format: date-time */
            updatedAt: string;
            farmer: components["schemas"]["farmer"];
            beehives: components["schemas"]["beehives"];
            managerInputs: components["schemas"]["manager-inputs"];
        };
        BucketInput: components["schemas"]["bucket-input"];
        BucketFarmer: components["schemas"]["farmer-2"];
        Bucket: {
            id: string;
            bucketId: string;
            /** Format: date-time */
            harvestDate: string;
            moistureContent: number;
            weight: number;
            verifiedWeight: number;
            honeyType: string;
            batchCode: string;
            farmer?: components["schemas"]["farmer-2"];
            /** @enum {string} */
            contaminationStatus?: "clean" | "contaminated";
        };
        HoneyHarvestingCreatedBy: components["schemas"]["created-by"];
        HoneyHarvestingFarmer: components["schemas"]["farmer-2"];
        HoneyHarvestingSearchResult: components["schemas"]["search-result"];
        HoneyHarvestingDetailsBucket: components["schemas"]["details-bucket"];
        HoneyHarvestingDetailsFactory: components["schemas"]["details-factory"];
        HoneyHarvestingDetailsProcessType: components["schemas"]["details-process-type"];
        HoneyHarvestingDetailsFactoryStaffLead: components["schemas"]["details-factory-staff-lead"];
        HoneyProcessingLogDetails: {
            id: string;
            factory?: components["schemas"]["details-factory"];
            processType?: components["schemas"]["details-process-type"];
            factoryStaffLead?: components["schemas"]["details-factory-staff-lead"];
            /** Format: date-time */
            processStartDate: string;
            /** Format: date-time */
            processEndDate?: string;
            /** Format: float */
            waxKg: number;
            /** Format: float */
            centrifugeTemperature: number;
            /** Format: float */
            centrifugeSpeed: number;
            /** Format: float */
            honeyTemperature: number;
            /** Format: float */
            meshSize: number;
            /** Format: float */
            pumpHopper: number;
            /** Format: float */
            honeyKg: number;
            batchCode: string;
            buckets: components["schemas"]["details-bucket"][];
            regionId: string;
            chiefdomId: string;
            zoneId: string;
            villageId: string;
        };
        Farmer: components["schemas"]["details"];
        FarmerList: {
            results?: components["schemas"]["details"][];
            /** @description Total number of farmers */
            total?: number;
            /** @description Current page number */
            page?: number;
            /** @description Number of items per page */
            pageSize?: number;
            /** @description Total number of pages */
            totalPages?: number;
        };
        Beehive: components["schemas"]["beehive-details"];
        BeehiveList: {
            results?: components["schemas"]["beehive-details"][];
            /** @description Total number of items */
            total?: number;
            /** @description Current page number */
            page?: number;
            /** @description Number of items per page */
            pageSize?: number;
            /** @description Total number of pages */
            totalPages?: number;
        };
        Region: {
            /** @example ZMB */
            id?: string;
            /** @example Zambia */
            name?: string;
            children?: components["schemas"]["Region"][];
        };
        User: {
            id: string;
            externalUID: string;
            name?: string;
            role: string;
            firstName: string;
            lastName: string;
            email: string;
            phone: string;
            jobTitle?: string | null;
            location?: string | null;
            image?: string | null;
        };
        UserUpdate: {
            id?: string;
            firstName: string;
            lastName: string;
            role: string;
            phone: string;
            jobTitle?: string;
        };
        BatchDetailsBeehives: {
            id: string;
            name: string;
            /** Format: float */
            latitude: number;
            /** Format: float */
            longitude: number;
            /** @description Additional metadata for the beehive */
            metadata: Record<string, never>;
        };
        BatchDetailsSources: {
            farmers: components["schemas"]["details"][];
            country: string;
            region: string;
            zone: string;
            beehives: components["schemas"]["BatchDetailsBeehives"][];
        };
        BatchDetailsBuckets: {
            /** Format: float */
            totalWeight: number;
            /** Format: date-time */
            firstSaleDate: string;
            list: components["schemas"]["Bucket"][];
        };
        BatchDetailsProcessing: {
            factoryLocation: string;
            processType: string;
            /** Format: date-time */
            startProcessDate: string;
            /** Format: date-time */
            endProcessDate: string;
            /** Format: float */
            finalWeightOfHoney: number;
            /** Format: float */
            finalWeightOfWax: number;
            dailySummaries: components["schemas"]["DailySummaryItem"][];
        };
        BatchDetails: {
            isProcessed: boolean;
            sources: components["schemas"]["BatchDetailsSources"];
            buckets: components["schemas"]["BatchDetailsBuckets"];
            processing: components["schemas"]["BatchDetailsProcessing"];
        };
        UserList: {
            results?: components["schemas"]["User"][];
            /** @description Total number of items */
            total?: number;
            /** @description Current page number */
            page?: number;
            /** @description Number of items per page */
            pageSize?: number;
            /** @description Total number of pages */
            totalPages?: number;
        };
        DailySummaryItem: {
            /**
             * Format: date-time
             * @description Date of the summary
             */
            date: string;
            /**
             * Format: float
             * @description Total weight for the day
             */
            totalWeight: number;
            /** @description Number of buckets for the day */
            numberOfBuckets: number;
            /**
             * Format: float
             * @description Total wax produced for the day
             */
            waxProduced: number;
            /**
             * Format: float
             * @description Estimated honey weight for the day
             */
            estimatedHoneyWeight: number;
        };
        BatchListItem: {
            /** @description Unique identifier for the batch */
            batchCode: string;
            /** @description Indicates if the batch is processed */
            isProcessed?: boolean;
            /**
             * Format: float
             * @description Total weight of the batch
             */
            totalWeight: number;
            /**
             * Format: date-time
             * @description Date when the batch was generated
             */
            dateGenerated: string;
            /** @description Indicates if the batch is blended */
            isBlended: boolean;
            dailySummaries: components["schemas"]["DailySummaryItem"][];
        };
        BatchList: {
            results: components["schemas"]["BatchListItem"][];
            /** @description Total number of items */
            total?: number;
            /** @description Current page number */
            page?: number;
            /** @description Number of items per page */
            pageSize?: number;
            /** @description Total number of pages */
            totalPages?: number;
        };
        /** @example {
         *       "code": "INVALID_REQUEST",
         *       "message": "The request contains invalid parameters"
         *     } */
        errors: {
            /** @description Error code identifier */
            code: string;
            /** @description Human-readable error message */
            message: string;
        };
        "dashboard-link": {
            /**
             * Format: uuid
             * @description Unique identifier for the generated link
             */
            id: string;
            /**
             * Format: uri
             * @description The URL to access the Metabase dashboard
             */
            url: string;
            /** @description The ID of the Metabase dashboard */
            dashboardId: number;
            /**
             * Format: date-time
             * @description When the link was created
             */
            createdAt: string;
            /**
             * Format: date-time
             * @description When the link will expire (if applicable)
             */
            expiresAt: string;
        };
        "created-by": {
            id: string;
            name: string;
        };
        farmer: {
            id: string;
            name: string;
            zoneId: string;
            village: string;
            gender: string;
        };
        beehives: {
            numberPresent: string;
            visitedMonthly: boolean;
            visitedRecently: string[];
            areAnyOccupied: boolean;
            awayFromWater: boolean;
            awayFromCrops: boolean;
            adequateFlowering: boolean;
            readyForHarvest: string;
            rebaitMaterial: string;
            enoughHoneyForWinter: boolean;
            beesWereMutilated: boolean;
            organicWaxUsed: boolean;
            provideFoodForBees: boolean;
            hasInfestation: boolean;
            infestationDetails?: string | null;
        };
        "manager-inputs": {
            additionalComments?: string | null;
            isCompliant: boolean;
            complianceReason?: string | null;
            nonComplianceReason?: string | null;
        };
        "search-result": {
            id: string;
            region?: string;
            chiefdom?: string;
            zone?: string;
            village?: string;
            /** Format: date-time */
            createdAt: string;
            moistureRange?: string;
            bucketCount: number;
            /** @enum {string} */
            honeyType: "light" | "dark";
            /** Format: float */
            totalWeight: number;
            /** Format: float */
            pricePerKg: number;
            /** Format: float */
            amountPaid: number;
        };
        "bucket-input": {
            bucketId: string;
            /** Format: date-time */
            harvestDate: string;
            moistureContent: number;
            weight: number;
            honeyType: string;
        };
        "farmer-2": {
            id: string;
            name: string;
        };
        details: {
            /** @description Unique identifier for the farmer */
            id: string;
            /** @description First name of the farmer */
            firstName: string;
            /** @description Last name of the farmer */
            lastName: string;
            /** @description Gender of the farmer */
            gender: string;
            /** @description Phone number of the farmer */
            phone: string;
            /** @description Marital status of the farmer */
            maritalStatus: string;
            /**
             * Format: date
             * @description Date of birth of the farmer
             */
            dob?: string;
            /** @description Size of the farmer's household */
            householdSize: string;
            /**
             * Format: float
             * @description Estimated annual income of the farmer
             */
            estimatedAnnualIncome: number;
            /** @description Source of income for the farmer */
            sourceOfIncome: string;
            /** @description The Role associated with the farmer */
            role: string;
            /** @description ID of the country associated with the farmer */
            countryId: string;
            /** @description Display name of the country associated with the farmer */
            countryName: string;
            /** @description ID of the region associated with the farmer */
            regionId: string;
            /** @description Farmer's chiefdom */
            chiefdomId: string;
            /** @description Farmer's zone id */
            zoneId: string;
            /** @description Village's village id */
            villageId: string;
            /** @description National ID number */
            nrc: string;
            /**
             * Format: date-time
             * @description When the farmer was created
             */
            createdAt: string;
            /**
             * Format: date-time
             * @description When the farmer was last updated
             */
            updatedAt: string;
            beehives?: {
                id: string;
                name: string;
            }[];
        };
        creation_update: {
            /** @description First name of the farmer */
            firstName: string;
            /** @description Last name of the farmer */
            lastName: string;
            /** @description Gender of the farmer */
            gender: string;
            /** @description Phone number of the farmer */
            phone: string;
            /** @description Marital status of the farmer */
            maritalStatus: string;
            /**
             * Format: date
             * @description Date of birth of the farmer
             */
            dob?: string;
            /** @description Size of the farmer's household */
            householdSize: string;
            /**
             * Format: float
             * @description Estimated annual income of the farmer
             */
            estimatedAnnualIncome: number;
            /** @description Source of income for the farmer */
            sourceOfIncome: string;
            /** @description National ID number */
            nrc: string;
            /** @description ID of the role associated with the farmer */
            role: string;
            /** @description ID of the country associated with the farmer */
            countryId: string;
            /** @description Farmer's region */
            regionId: string;
            /** @description Farmer's chiefdom */
            chiefdomId: string;
            /** @description Farmer's zone */
            zoneId: string;
            /** @description Village's zone */
            villageId: string;
        };
        "beehive-details": {
            /** @description Beehive id */
            id: string;
            /** @description Beehive name */
            name: string;
            status: string;
            /** Format: float */
            latitude: number;
            /** Format: float */
            longitude: number;
            isFunctional: boolean;
            needsToBeRepaired: boolean;
            isOccupied: boolean;
            observations?: string;
            farmerId: string;
        };
        "beehive-creation_update": {
            /** @description Beehive name */
            name: string;
            status: string;
            /** Format: float */
            latitude: number;
            /** Format: float */
            longitude: number;
            isFunctional?: boolean;
            needsToBeRepaired?: boolean;
            isOccupied?: boolean;
            observations?: string;
            farmerId: string;
        };
        "details-factory": {
            id?: string;
            name?: string;
        };
        "details-process-type": {
            id?: string;
            name?: string;
        };
        "details-factory-staff-lead": {
            id?: string;
            name?: string;
        };
        "details-bucket": {
            /** @description Unique identifier for the bucket contents. */
            id?: string;
            /** @description Physical identifier for the actual bucket. */
            bucketId: string;
            /**
             * Format: date-time
             * @description Date when the honey was harvested.
             */
            harvestDate: string;
            /** @description Type of honey collected in the bucket. */
            honeyType: string;
            /**
             * Format: float
             * @description Moisture content percentage of the honey.
             */
            moistureContent: number;
            /**
             * Format: float
             * @description Weight of the honey in the bucket.
             */
            weight: number;
            verifiedWeight: number;
            /**
             * @description Status indicating if the honey is clean or contaminated.
             * @enum {string}
             */
            contaminationStatus?: "clean" | "contaminated";
        };
        "creation_update-2": {
            password: string;
            /** @description First name of the user */
            firstName: string;
            /** @description Last name of the user */
            lastName: string;
            /** @description Email of the user */
            email: string;
            /** @description Phone number of the user */
            phone: string;
            /** @description Job Title of the user */
            jobTitle?: string;
            /** @description Role of the user */
            role: string;
        };
    };
    responses: never;
    parameters: never;
    requestBodies: never;
    headers: never;
    pathItems: never;
}
export type $defs = Record<string, never>;
export interface operations {
    createDashboardLink: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Metabase dashboard ID */
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Dashboard link created successfully */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["dashboard-link"];
                };
            };
            /** @description Generic error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    searchFormsComplianceSubmissions: {
        parameters: {
            query?: {
                /** @description Fuzzy search query text */
                query?: string;
                /** @description Filter created after this date, inclusive */
                createdAtFrom?: string;
                /** @description Filter created before this date, exclusive */
                createdAtTo?: string;
                /** @description Short option */
                sortBy?: "oldestFirst" | "newestFirst";
                /** @description Page number */
                page?: number;
                /** @description Number of items per page */
                pageSize?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Search results */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        results?: components["schemas"]["FormsComplianceSubmissions"][];
                        /** @description Total number of farmers */
                        total?: number;
                        /** @description Current page number */
                        page?: number;
                        /** @description Number of items per page */
                        pageSize?: number;
                        /** @description Total number of pages */
                        totalPages?: number;
                    };
                };
            };
            /** @description Unexpected error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    createFormsComplianceSubmissions: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": {
                    farmerId: string;
                    beehives: components["schemas"]["beehives"];
                    managerInputs: components["schemas"]["manager-inputs"];
                };
            };
        };
        responses: {
            /** @description Form created successfully */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @description Unique identifier for new complaince form */
                        id?: string;
                    };
                };
            };
            /** @description Error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    getFormsComplianceSubmissionById: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Form ID */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Form found */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["FormsComplianceSubmissions"];
                };
            };
            /** @description Error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    searchHoneyHarvestingTransactions: {
        parameters: {
            query?: {
                /** @description Fuzzy search query text */
                query?: string;
                /** @description Filter created after this date, inclusive */
                createdAtFrom?: string;
                /** @description Filter created before this date, exclusive */
                createdAtTo?: string;
                /** @description Short option */
                sortBy?: "oldestFirst" | "newestFirst";
                /** @description Page number */
                page?: number;
                /** @description Number of items per page */
                pageSize?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Search results */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        results?: components["schemas"]["search-result"][];
                        /** @description Total number of farmers */
                        total?: number;
                        /** @description Current page number */
                        page?: number;
                        /** @description Number of items per page */
                        pageSize?: number;
                        /** @description Total number of pages */
                        totalPages?: number;
                    };
                };
            };
            /** @description Unexpected error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    createHoneyHarvesting: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": {
                    farmerId: string;
                    beehives: unknown[];
                    /** Format: date-time */
                    saleDate: string;
                    /** Format: float */
                    pricePerKg: number;
                    regionId: string;
                    chiefdomId: string;
                    zoneId: string;
                    villageId: string;
                    buckets: components["schemas"]["bucket-input"][];
                    createdBy: string;
                };
            };
        };
        responses: {
            /** @description Honey harvesting data created successfully */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @description Unique identifier for a new honey harvesting transaction */
                        id?: string;
                    };
                };
            };
            /** @description Error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    getHoneyHarvestingById: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Honey harvesting transaction ID */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Honey harvesting transaction found */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        id: string;
                        regionId: string;
                        chiefdomId: string;
                        zoneId: string;
                        villageId: string;
                        farmer: components["schemas"]["farmer-2"];
                        beehives: string[];
                        /** Format: date-time */
                        saleDate: string;
                        /** Format: float */
                        pricePerKg: number;
                        buckets: components["schemas"]["Bucket"][];
                        createdBy: components["schemas"]["created-by"];
                        ipfsHash?: string;
                        palmyraHash?: string;
                        /** Format: date-time */
                        createdAt?: string;
                    };
                };
            };
            /** @description Error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    getFarmers: {
        parameters: {
            query?: {
                /** @description Fuzzy search query text */
                query?: string;
                /** @description Filter by business ID */
                businessId?: string;
                /** @description Filter by creator ID */
                createdBy?: string;
                /** @description Filter created after this date, inclusive */
                createdAtFrom?: string;
                /** @description Filter created before this date, exclusive */
                createdAtTo?: string;
                /** @description Page number */
                page?: number;
                /** @description Number of items per page */
                pageSize?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Search results */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["FarmerList"];
                };
            };
            /** @description Unexpected error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    createFarmer: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["creation_update"];
            };
        };
        responses: {
            /** @description Farmer created successfully */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @description Unique identifier for the new farmer */
                        id?: string;
                    };
                };
            };
            /** @description Error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    downloadFarmersOffline: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Search results */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["FarmerList"];
                };
            };
            /** @description Unexpected error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    getFarmerById: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Farmer ID */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Farmer found */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["details"];
                };
            };
            /** @description Farmer not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
            /** @description Error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    updateFarmer: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Farmer ID */
                id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["creation_update"];
            };
        };
        responses: {
            /** @description Farmer created successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @description Unique identifier for the new farmer */
                        id?: string;
                    };
                };
            };
            /** @description Error */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
            /** @description Error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    deleteFarmerById: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Farmer ID */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Farmer deleted */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Farmer not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
            /** @description Error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    searchFarmers: {
        parameters: {
            query?: {
                /** @description Fuzzy search query text */
                query?: string;
                /** @description Filter by business ID */
                businessId?: string;
                /** @description Filter by creator ID */
                createdBy?: string;
                /** @description Filter created after this date, inclusive */
                createdAtFrom?: string;
                /** @description Filter created before this date, exclusive */
                createdAtTo?: string;
                /** @description Page number */
                page?: number;
                /** @description Number of items per page */
                pageSize?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Search results */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["FarmerList"];
                };
            };
            /** @description Unexpected error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    getBeehives: {
        parameters: {
            query?: {
                /** @description Fuzzy search query text */
                query?: string;
                /** @description Owner id */
                farmerId?: string;
                /** @description Filter created after this date, inclusive */
                createdAtFrom?: string;
                /** @description Filter created before this date, exclusive */
                createdAtTo?: string;
                /** @description Page number */
                page?: number;
                /** @description Number of items per page */
                pageSize?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Search results */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["BeehiveList"];
                };
            };
            /** @description Unexpected error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    createBeehive: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["beehive-creation_update"];
            };
        };
        responses: {
            /** @description Beehive created successfully */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @description Unique identifier for the new beehive */
                        id?: string;
                    };
                };
            };
            /** @description Error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    getBeehiveById: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Beehive ID */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Farmer found */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["beehive-details"];
                };
            };
            /** @description Farmer not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
            /** @description Error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    updateBeehive: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Beehive ID */
                id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["beehive-creation_update"];
            };
        };
        responses: {
            /** @description Beehive created successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @description Unique identifier for the new Beehive */
                        id?: string;
                    };
                };
            };
            /** @description Error */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
            /** @description Error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    deleteBeehiveById: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Beehive ID */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Beehive deleted */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Beehive not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
            /** @description Error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    searchHoneyProcessingLog: {
        parameters: {
            query?: {
                /** @description Fuzzy search query text */
                query?: string;
                /** @description Filter processes started after this date, inclusive */
                processStartTSFrom?: string;
                /** @description Filter processes started before this date, exclusive */
                processStartTSTo?: string;
                /** @description Exclude processes that are closed */
                onlyOpen?: boolean;
                /** @description Short option */
                sortBy?: "oldestFirst" | "newestFirst";
                /** @description Page number */
                page?: number;
                /** @description Number of items per page */
                pageSize?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Search results */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        results?: components["schemas"]["HoneyProcessingLogDetails"][];
                        /** @description Total number of processes */
                        total?: number;
                        /** @description Current page number */
                        page?: number;
                        /** @description Number of items per page */
                        pageSize?: number;
                        /** @description Total number of pages */
                        totalPages?: number;
                    };
                };
            };
            /** @description Unexpected error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    createHoneyProcessingLog: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": {
                    factorId: string;
                    processTypeId: string;
                    factoryStaffLeadId: string;
                    /** Format: date-time */
                    processStartDate: string;
                    /** Format: date-time */
                    processEndDate?: string;
                    /** Format: float */
                    waxKg?: number;
                    /** Format: float */
                    centrifugeTemperature?: number;
                    /** Format: float */
                    centrifugeSpeed?: number;
                    /** Format: float */
                    honeyTemperature?: number;
                    /** Format: float */
                    meshSize?: number;
                    /** Format: float */
                    pumpHopper?: number;
                    /** Format: float */
                    honeyKg?: number;
                    buckets: string[];
                };
            };
        };
        responses: {
            /** @description Honey processing log created successfully */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @description Unique identifier for a new honey processing log created */
                        id: string;
                        /** @description Unique identifier for a new honey processing batch created */
                        batchCode: string;
                    };
                };
            };
            /** @description Error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    getHoneyProcessingById: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Honey harvesting processing log ID */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Honey harvesting transaction found */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HoneyProcessingLogDetails"];
                };
            };
            /** @description Not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
            /** @description Error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    updateHoneyProcessingLog: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Honey harvesting processing log ID */
                id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": {
                    /** Format: date-time */
                    processEndDate?: string;
                    /** Format: float */
                    waxKg?: number;
                    /** Format: float */
                    centrifugeTemperature?: number;
                    /** Format: float */
                    centrifugeSpeed?: number;
                    /** Format: float */
                    honeyTemperature?: number;
                    /** Format: float */
                    meshSize?: number;
                    /** Format: float */
                    pumpHopper?: number;
                    /** Format: float */
                    honeyKg?: number;
                    buckets?: {
                        [key: string]: {
                            /** Format: float */
                            verifiedWeight?: number;
                            /** @enum {string} */
                            contaminationStatus?: "clean" | "contaminated";
                        };
                    };
                };
            };
        };
        responses: {
            /** @description Honey processing log updated successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @description Unique identifier for a updated honey processing log created */
                        id: string;
                        /** @description Unique identifier for a updated honey processing batch created */
                        batchCode: string;
                    };
                };
            };
            /** @description Not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
            /** @description Error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    getAllRegions: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description A nested list of regions */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Region"][];
                };
            };
            /** @description Invalid request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unexpected error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    getAllRegionsTree: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description A nested list of regions */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Region"];
                };
            };
            /** @description Invalid request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unexpected error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    getAllChiefdomsByRegionId: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description The ID of the region to filter chiefdoms by. */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description List of chiefdoms in the specified region */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Region"][];
                };
            };
            /** @description Invalid request, missing or invalid ID. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Region not found for the given ID. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unexpected error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    getAllZonesByRegionId: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description The ID of the region to filter zones by. */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description List of zones in the specified region */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Region"][];
                };
            };
            /** @description Invalid request, missing or invalid ID. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Region not found for the given ID. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unexpected error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    getAllZonesByChiefdomId: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description The ID of the chiefdom to filter zones by. */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description List of zones in the specified chiefdom */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Region"][];
                };
            };
            /** @description Invalid request, missing or invalid ID. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Chiefdom not found for the given ID. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unexpected error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    getAllVillagesByZoneId: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description The ID of the zone to filter villages by. */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description List of villages in the specified zone */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Region"][];
                };
            };
            /** @description Invalid request, missing or invalid ID. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Zone not found for the given ID. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unexpected error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    getAllBuckets: {
        parameters: {
            query?: {
                /** @description Filter buckets by region */
                region?: string;
                /** @description Filter buckets by chiefdom */
                chiefdom?: string;
                /** @description Filter buckets by zone */
                zone?: string;
                /** @description Filter buckets by village */
                village?: string;
                /** @description Filter buckets by honey type */
                honeyType?: "light" | "dark";
                /** @description Fuzzy search query text */
                query?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description A list of buckets */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Bucket"][];
                };
            };
            /** @description Invalid request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unexpected error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    SearchUsers: {
        parameters: {
            query?: {
                /** @description Fuzzy search query text */
                query?: string;
                /** @description Page number */
                page?: number;
                /** @description Number of items per page */
                pageSize?: number;
                /** @description Filter users by one or more roles */
                role?: string[];
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description A list of users */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserList"];
                };
            };
            /** @description Invalid request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unexpected error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    CreateUser: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["creation_update-2"];
            };
        };
        responses: {
            /** @description User created successfully */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @description Unique identifier for the new user */
                        id?: string;
                    };
                };
            };
            /** @description Error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    getUserById: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description User ID */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description User found */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["User"];
                };
            };
            /** @description User not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
            /** @description Error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    updateUser: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description User ID */
                id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UserUpdate"];
            };
        };
        responses: {
            /** @description User created successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @description Unique identifier for the new user */
                        id?: string;
                    };
                };
            };
            /** @description Error */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
            /** @description Error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    deleteUserById: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description User ID */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description User deleted */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description User not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
            /** @description Error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    searchBatches: {
        parameters: {
            query?: {
                /** @description Fuzzy search query text */
                query?: string;
                /** @description Filter created after this date, inclusive */
                createdAtFrom?: string;
                /** @description Filter created before this date, exclusive */
                createdAtTo?: string;
                /** @description Page number */
                page?: number;
                /** @description Number of items per page */
                pageSize?: number;
                /** @description Short option */
                sortBy?: "oldestFirst" | "newestFirst";
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description A list of batches */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["BatchList"];
                };
            };
            /** @description Invalid request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unexpected error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    getBatchByCode: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description The batch code of the batch to retrieve. */
                batchCode: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Batch details */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["BatchDetails"];
                };
            };
            /** @description Invalid request, missing or invalid ID. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Batch not found for the given ID. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unexpected error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
}
