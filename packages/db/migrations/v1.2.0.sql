-- Unify farmer location related field names
ALTER TABLE farmer RENAME COLUMN "regionId" TO region_id;
ALTER TABLE farmer RENAME COLUMN chiefdom_code TO chiefdom_id;
ALTER TABLE farmer RENAME COLUMN "zoneCode" TO zone_id;
ALTER TABLE farmer RENAME COLUMN village_code TO village_id;

-- Add location related fields to transaction
ALTER TABLE public.transaction
ADD COLUMN region_id TEXT,
ADD COLUMN chiefdom_id TEXT,
ADD COLUMN zone_id TEXT,
ADD COLUMN village_id TEXT;

-- Fill location related fields from farmer table (that is how they are currently set)
UPDATE public.transaction t
SET 
  region_id = f.region_id,
  chiefdom_id = f.chiefdom_id,
  zone_id = f.zone_id,
  village_id = f.village_id
FROM farmer f
WHERE t.farmer_id = f.id;

-- Remove batch_code from transaction
ALTER TABLE bucket
DROP COLUMN batch_code;
