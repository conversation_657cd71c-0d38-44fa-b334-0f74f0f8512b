-- 10/04/2025

alter table farmer add is_deleted bool default false;
alter table farmer add chiefdom_code text;
alter table farmer add village_code text;
alter table farmer add nrc text;

alter table farmer drop constraint farmers_country_id_countries_id_fk;

update farmer
set
    "chiefdom_code" = (
        select
            upper(replace(trim(r.chiefdom), ' ', '_')) zc
        from farmer f
                 join farmer_2 f2 on f.id = f2.id
                 join regions_zambia_staging_v2 r on f2."regionId"::integer = r.id
        where f.id = farmer.id
    ),
    "village_code" = (
        select
            upper(replace(trim(r.village_name), ' ', '_')) vc
        from farmer f
                 join farmer_2 f2 on f.id = f2.id
                 join regions_zambia_staging_v2 r on f2."regionId"::integer = r.id
        where f.id = farmer.id
    )
;

-- select
--     f.id,
--     r.chiefdom,
--     upper(replace(trim(r.zone_code), ' ', '_')) zc,
--     upper(replace(trim(r.village_name), ' ', '_')) vc
-- from farmer f
--          join farmer_2 f2 on f.id = f2.id
--          join regions_zambia_staging_v2 r on f2."regionId"::integer = r.id;
--
-- select count(*) from farmer;

-- 14/04/2024

alter table "asset" drop column status;
alter table "asset" drop column longitude;
alter table "asset" drop column latitude;
alter table "asset" drop column metadata;

-- 16/04/2025

alter table transaction_asset rename column asset_id to asset_status_id;
alter table transaction_asset drop constraint fk_transaction_asset_asset;
alter table transaction_asset add constraint fk_transaction_asset_status foreign key (asset_status_id) references "assetStatus";

-- 24/04/2025

alter table bucket add column verified_weight  double precision NOT NULL default 0;

--30/04/2025

alter table honey_processing_log add column centrifuge_temperature  double precision NOT NULL default 0;
alter table honey_processing_log add column centrifuge_speed  double precision NOT NULL default 0;
alter table honey_processing_log add column honey_temperature  double precision NOT NULL default 0;
alter table honey_processing_log add column mesh_size  double precision NOT NULL default 0;
alter table honey_processing_log add column pump_hopper  double precision NOT NULL default 0;