

CREATE TABLE honey_processing_log
(
    id                     text                     NOT NULL,
    factory_id             text                     NOT NULL,
    process_type_id        text                     NOT NULL,
    factory_staff_lead_id  text                     NOT NULL,
    wax_weight             double precision         NOT NULL,
    honey_weight           double precision         NOT NULL,
    batch_code             text                     NOT NULL,
    centrifuge_temperature double precision	        NOT NULL,
    centrifuge_speed       double precision	        NOT NULL,
    honey_temperature	   double precision	        NOT NULL,	
    mesh_size	           double precision	        NOT NULL,	
    pump_hopper            double precision	        NOT NULL,	
    process_start_ts       timestamp,
    process_end_ts         timestamp,                 -- If null, it is in processing status.

    CONSTRAINT honey_processing_log_pkey PRIMARY KEY (id),
    CONSTRAINT fk_factory_id FOREIGN KEY (factory_id) REFERENCES factory (id),
    CONSTRAINT fk_process_type_id FOREIGN KEY (process_type_id) REFERENCES honey_process_type (id),
    CONSTRAINT fk_factory_staff_lead_id FOREIGN KEY (factory_staff_lead_id) REFERENCES "user" (id)
);

CREATE TABLE honey_processing_log_buckets
(
    bucket_id               text NOT NULL,
    honey_processing_log_id text NOT NULL,
    CONSTRAINT honey_processing_log_buckets_pkey PRIMARY KEY (bucket_id, honey_processing_log_id),
    CONSTRAINT fk_bucket_id FOREIGN KEY (bucket_id) REFERENCES bucket (id),
    CONSTRAINT fk_honey_processing_log_id FOREIGN KEY (honey_processing_log_id) REFERENCES honey_processing_log (id)
);

CREATE TABLE post_processing_inventory
(
    id         text             NOT NULL,
    batch_code text             NOT NULL,
    pour_date  timestamp        NOT NULL,
    weight     double precision NOT NULL, -- Should be volume or liters?

    CONSTRAINT honey_processing_drum_pkey PRIMARY KEY (id)
);

-- Ask:
-- Do we need to store wax?
-- Should inventory be volume or liters?
-- assetStatus is the right table. Create a view to replace asset.
