CREATE TABLE "transaction"
(
    id           text                                NOT NULL,
    farmer_id    text                                NOT NULL,
    sale_date    timestamp                           NOT NULL,
    price_per_kg double precision                    NOT NULL,
    created_by   text                                NULL,
    created_at   timestamp DEFAULT CURRENT_TIMESTAMP NULL,
    updated_at   timestamp DEFAULT CURRENT_TIMESTAMP NULL,
    ipfs_hash    text                                NULL,
    palmyra_hash text                                NULL,
    region_id    text NULL,
    chiefdom_id text NULL,
    zone_id    text NULL,
    village_id  text NULL,
    CONSTRAINT transaction_pkey PRIMARY KEY (id),
    CONSTRAINT fk_transaction_farmer FOREIGN KEY (farmer_id) REFERENCES farmer (id) ON DELETE CASCADE
);

-- TODO: This is not representing a physical bucket. It's representing the bucket collected in a transaction. The correct name could be `bucket_content` or `transaction_bucket`
CREATE TABLE "bucket"
(
    id               text                                NOT NULL,
    bucket_id        text                                NOT NULL, -- reference code for the physical bucket
    harvest_date     timestamp                           NOT NULL,
    moisture_content double precision                    NOT NULL,
    honey_type       text                                NOT NULL,
    weight           double precision                    NOT NULL,
    verified_weight  double precision                    NOT NULL default 0,
    transaction_id   text                                NOT NULL,
    created_at       timestamp DEFAULT CURRENT_TIMESTAMP NULL,
    updated_at       timestamp DEFAULT CURRENT_TIMESTAMP NULL,
    contamination_status TEXT NOT NULL DEFAULT 'clean',
    CONSTRAINT bucket_pkey PRIMARY KEY (id),
    CONSTRAINT fk_bucket_transaction FOREIGN KEY (transaction_id) REFERENCES "transaction" (id) ON DELETE CASCADE
);

CREATE TABLE "transaction_asset"
(
    transaction_id  text NOT NULL,
    asset_status_id text NOT NULL,
    CONSTRAINT fk_transaction_asset_transaction FOREIGN KEY (transaction_id) REFERENCES "transaction" (id),
    CONSTRAINT fk_transaction_asset_status FOREIGN KEY (asset_status_id) REFERENCES "assetStatus" (id)
);
