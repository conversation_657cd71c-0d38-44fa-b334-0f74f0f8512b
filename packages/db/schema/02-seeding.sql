INSERT INTO currency (code, symbol, "symbolNative", name)
VALUES ('usd', '$', '$', 'US Dollar'),
       ('cad', 'CA$', '$', 'Canadian Dollar'),
       ('eur', '€', '€', 'Euro'),
       ('aed', 'AED', 'د.إ.', 'United Arab Emirates Dirham'),
       ('afn', 'Af', '؋', 'Afghan Afghani'),
       ('all', 'ALL', 'Lek', 'Albanian Lek'),
       ('amd', 'AMD', 'դր.', 'Armenian Dram'),
       ('ars', 'AR$', '$', 'Argentine Peso'),
       ('aud', 'AU$', '$', 'Australian Dollar'),
       ('azn', 'man.', 'ман.', 'Azerbaijani Manat'),
       ('bam', 'KM', 'KM', 'Bosnia-Herzegovina Convertible Mark'),
       ('bdt', 'Tk', '৳', 'Bangladeshi Taka'),
       ('bgn', 'BGN', 'лв.', 'Bulgarian Lev'),
       ('bhd', 'BD', 'د.ب.', 'Bahraini Dinar'),
       ('bif', 'FBu', 'FBu', 'Burundian Franc'),
       ('bnd', 'BN$', '$', 'Brunei Dollar'),
       ('bob', 'Bs', 'Bs', 'Bolivian Boliviano'),
       ('brl', 'R$', 'R$', 'Brazilian Real'),
       ('bwp', 'BWP', 'P', 'Botswanan Pula'),
       ('byn', 'Br', 'руб.', 'Belarusian Ruble'),
       ('bzd', 'BZ$', '$', 'Belize Dollar'),
       ('cdf', 'CDF', 'FrCD', 'Congolese Franc'),
       ('chf', 'CHF', 'CHF', 'Swiss Franc'),
       ('clp', 'CL$', '$', 'Chilean Peso'),
       ('cny', 'CN¥', 'CN¥', 'Chinese Yuan'),
       ('cop', 'CO$', '$', 'Colombian Peso'),
       ('crc', '₡', '₡', 'Costa Rican Colón'),
       ('cve', 'CV$', 'CV$', 'Cape Verdean Escudo'),
       ('czk', 'Kč', 'Kč', 'Czech Republic Koruna'),
       ('djf', 'Fdj', 'Fdj', 'Djiboutian Franc'),
       ('dkk', 'Dkr', 'kr', 'Danish Krone'),
       ('dop', 'RD$', 'RD$', 'Dominican Peso'),
       ('dzd', 'DA', 'د.ج.', 'Algerian Dinar'),
       ('eek', 'Ekr', 'kr', 'Estonian Kroon'),
       ('egp', 'EGP', 'ج.م.', 'Egyptian Pound'),
       ('ern', 'Nfk', 'Nfk', 'Eritrean Nakfa'),
       ('etb', 'Br', 'Br', 'Ethiopian Birr'),
       ('gbp', '£', '£', 'British Pound Sterling'),
       ('gel', 'GEL', 'GEL', 'Georgian Lari'),
       ('ghs', 'GH₵', 'GH₵', 'Ghanaian Cedi'),
       ('gnf', 'FG', 'FG', 'Guinean Franc'),
       ('gtq', 'GTQ', 'Q', 'Guatemalan Quetzal'),
       ('hkd', 'HK$', '$', 'Hong Kong Dollar'),
       ('hnl', 'HNL', 'L', 'Honduran Lempira'),
       ('hrk', 'kn', 'kn', 'Croatian Kuna'),
       ('huf', 'Ft', 'Ft', 'Hungarian Forint'),
       ('idr', 'Rp', 'Rp', 'Indonesian Rupiah'),
       ('ils', '₪', '₪', 'Israeli New Sheqel'),
       ('inr', 'Rs', 'টকা', 'Indian Rupee'),
       ('iqd', 'IQD', 'د.ع.', 'Iraqi Dinar'),
       ('irr', 'IRR', '﷼', 'Iranian Rial'),
       ('isk', 'Ikr', 'kr', 'Icelandic Króna'),
       ('jmd', 'J$', '$', 'Jamaican Dollar'),
       ('jod', 'JD', 'د.أ.', 'Jordanian Dinar'),
       ('jpy', '¥', '￥', 'Japanese Yen'),
       ('kes', 'Ksh', 'Ksh', 'Kenyan Shilling'),
       ('khr', 'KHR', '៛', 'Cambodian Riel'),
       ('kmf', 'CF', 'FC', 'Comorian Franc'),
       ('krw', '₩', '₩', 'South Korean Won'),
       ('kwd', 'KD', 'د.ك.', 'Kuwaiti Dinar'),
       ('kzt', 'KZT', 'тңг.', 'Kazakhstani Tenge'),
       ('lbp', 'LB£', 'ل.ل.', 'Lebanese Pound'),
       ('lkr', 'SLRs', 'SL Re', 'Sri Lankan Rupee'),
       ('ltl', 'Lt', 'Lt', 'Lithuanian Litas'),
       ('lvl', 'Ls', 'Ls', 'Latvian Lats'),
       ('lyd', 'LD', 'د.ل.', 'Libyan Dinar'),
       ('mad', 'MAD', 'د.م.', 'Moroccan Dirham'),
       ('mdl', 'MDL', 'MDL', 'Moldovan Leu'),
       ('mga', 'MGA', 'MGA', 'Malagasy Ariary'),
       ('mkd', 'MKD', 'MKD', 'Macedonian Denar'),
       ('mmk', 'MMK', 'K', 'Myanma Kyat'),
       ('mnt', 'MNT', '₮', 'Mongolian Tugrig'),
       ('mop', 'MOP$', 'MOP$', 'Macanese Pataca'),
       ('mur', 'MURs', 'MURs', 'Mauritian Rupee'),
       ('mxn', 'MX$', '$', 'Mexican Peso'),
       ('myr', 'RM', 'RM', 'Malaysian Ringgit'),
       ('mzn', 'MTn', 'MTn', 'Mozambican Metical'),
       ('nad', 'N$', 'N$', 'Namibian Dollar'),
       ('ngn', '₦', '₦', 'Nigerian Naira'),
       ('nio', 'C$', 'C$', 'Nicaraguan Córdoba'),
       ('nok', 'Nkr', 'kr', 'Norwegian Krone'),
       ('npr', 'NPRs', 'नेरू', 'Nepalese Rupee'),
       ('nzd', 'NZ$', '$', 'New Zealand Dollar'),
       ('omr', 'OMR', 'ر.ع.', 'Omani Rial'),
       ('pab', 'B/.', 'B/.', 'Panamanian Balboa'),
       ('pen', 'S/.', 'S/.', 'Peruvian Nuevo Sol'),
       ('php', '₱', '₱', 'Philippine Peso'),
       ('pkr', 'PKRs', '₨', 'Pakistani Rupee'),
       ('pln', 'zł', 'zł', 'Polish Zloty'),
       ('pyg', '₲', '₲', 'Paraguayan Guarani'),
       ('qar', 'QR', 'ر.ق.', 'Qatari Rial'),
       ('ron', 'RON', 'RON', 'Romanian Leu'),
       ('rsd', 'din.', 'дин.', 'Serbian Dinar'),
       ('rub', 'RUB', '₽.', 'Russian Ruble'),
       ('rwf', 'RWF', 'FR', 'Rwandan Franc'),
       ('sar', 'SR', 'ر.س.', 'Saudi Riyal'),
       ('sdg', 'SDG', 'SDG', 'Sudanese Pound'),
       ('sek', 'Skr', 'kr', 'Swedish Krona'),
       ('sgd', 'S$', '$', 'Singapore Dollar'),
       ('sos', 'Ssh', 'Ssh', 'Somali Shilling');

-- Insert into country table
INSERT INTO country (id, code, name, "displayName")
VALUES ('1', 'af', 'AFGHANISTAN', 'Afghanistan'),
       ('2', 'al', 'ALBANIA', 'Albania'),
       ('3', 'dz', 'ALGERIA', 'Algeria'),
       ('4', 'as', 'AMERICAN SAMOA', 'American Samoa'),
       ('5', 'ad', 'ANDORRA', 'Andorra'),
       ('6', 'ao', 'ANGOLA', 'Angola'),
       ('7', 'ai', 'ANGUILLA', 'Anguilla'),
       ('8', 'aq', 'ANTARCTICA', 'Antarctica'),
       ('9', 'ag', 'ANTIGUA AND BARBUDA', 'Antigua and Barbuda'),
       ('10', 'ar', 'ARGENTINA', 'Argentina'),
       ('11', 'am', 'ARMENIA', 'Armenia'),
       ('12', 'aw', 'ARUBA', 'Aruba'),
       ('13', 'au', 'AUSTRALIA', 'Australia'),
       ('14', 'az', 'AZERBAIJAN', 'Azerbaijan'),
       ('15', 'bs', 'BAHAMAS', 'Bahamas'),
       ('16', 'bh', 'BAHRAIN', 'Bahrain'),
       ('17', 'bd', 'BANGLADESH', 'Bangladesh'),
       ('18', 'bb', 'BARBADOS', 'Barbados'),
       ('19', 'by', 'BELARUS', 'Belarus'),
       ('20', 'bz', 'BELIZE', 'Belize'),
       ('21', 'bj', 'BENIN', 'Benin'),
       ('22', 'bm', 'BERMUDA', 'Bermuda'),
       ('23', 'bt', 'BHUTAN', 'Bhutan'),
       ('24', 'bo', 'BOLIVIA', 'Bolivia'),
       ('25', 'bq', 'BONAIRE, SINT EUSTATIUS AND SABA', 'Bonaire, Sint Eustatius and Saba'),
       ('26', 'ba', 'BOSNIA AND HERZEGOVINA', 'Bosnia and Herzegovina'),
       ('27', 'bw', 'BOTSWANA', 'Botswana'),
       ('28', 'bv', 'BOUVET ISLAND', 'Bouvet Island'),
       ('29', 'br', 'BRAZIL', 'Brazil'),
       ('30', 'io', 'BRITISH INDIAN OCEAN TERRITORY', 'British Indian Ocean Territory'),
       ('31', 'bn', 'BRUNEI DARUSSALAM', 'Brunei Darussalam'),
       ('32', 'bf', 'BURKINA FASO', 'Burkina Faso'),
       ('33', 'bi', 'BURUNDI', 'Burundi'),
       ('34', 'kh', 'CAMBODIA', 'Cambodia'),
       ('35', 'cm', 'CAMEROON', 'Cameroon'),
       ('36', 'cv', 'CAPE VERDE', 'Cape Verde'),
       ('37', 'ky', 'CAYMAN ISLANDS', 'Cayman Islands'),
       ('38', 'cf', 'CENTRAL AFRICAN REPUBLIC', 'Central African Republic'),
       ('39', 'td', 'CHAD', 'Chad'),
       ('40', 'cl', 'CHILE', 'Chile'),
       ('41', 'cn', 'CHINA', 'China'),
       ('42', 'cx', 'CHRISTMAS ISLAND', 'Christmas Island'),
       ('43', 'cc', 'COCOS (KEELING) ISLANDS', 'Cocos (Keeling) Islands'),
       ('44', 'co', 'COLOMBIA', 'Colombia'),
       ('45', 'km', 'COMOROS', 'Comoros'),
       ('46', 'cg', 'CONGO', 'Congo'),
       ('47', 'cd', 'CONGO, THE DEMOCRATIC REPUBLIC OF THE', 'Congo, the Democratic Republic of the'),
       ('48', 'ck', 'COOK ISLANDS', 'Cook Islands'),
       ('49', 'cr', 'COSTA RICA', 'Costa Rica'),
       ('50', 'ci', 'COTE DIVOIRE', 'Cote DIvoire'),
       ('51', 'cu', 'CUBA', 'Cuba'),
       ('52', 'cw', 'CURAÇAO', 'Curaçao'),
       ('53', 'dj', 'DJIBOUTI', 'Djibouti'),
       ('54', 'dm', 'DOMINICA', 'Dominica'),
       ('55', 'do', 'DOMINICAN REPUBLIC', 'Dominican Republic'),
       ('56', 'ec', 'ECUADOR', 'Ecuador'),
       ('57', 'eg', 'EGYPT', 'Egypt'),
       ('58', 'sv', 'EL SALVADOR', 'El Salvador'),
       ('59', 'gq', 'EQUATORIAL GUINEA', 'Equatorial Guinea'),
       ('60', 'er', 'ERITREA', 'Eritrea'),
       ('61', 'et', 'ETHIOPIA', 'Ethiopia'),
       ('62', 'fk', 'FALKLAND ISLANDS (MALVINAS)', 'Falkland Islands (Malvinas)'),
       ('63', 'fo', 'FAROE ISLANDS', 'Faroe Islands'),
       ('64', 'fj', 'FIJI', 'Fiji'),
       ('65', 'gf', 'FRENCH GUIANA', 'French Guiana'),
       ('66', 'pf', 'FRENCH POLYNESIA', 'French Polynesia'),
       ('67', 'tf', 'FRENCH SOUTHERN TERRITORIES', 'French Southern Territories'),
       ('68', 'ga', 'GABON', 'Gabon'),
       ('69', 'gm', 'GAMBIA', 'Gambia'),
       ('70', 'ge', 'GEORGIA', 'Georgia'),
       ('71', 'gh', 'GHANA', 'Ghana'),
       ('72', 'gi', 'GIBRALTAR', 'Gibraltar'),
       ('73', 'gl', 'GREENLAND', 'Greenland'),
       ('74', 'gd', 'GRENADA', 'Grenada'),
       ('75', 'gp', 'GUADELOUPE', 'Guadeloupe'),
       ('76', 'gu', 'GUAM', 'Guam'),
       ('77', 'gt', 'GUATEMALA', 'Guatemala'),
       ('78', 'gg', 'GUERNSEY', 'Guernsey'),
       ('79', 'gn', 'GUINEA', 'Guinea'),
       ('80', 'gw', 'GUINEA-BISSAU', 'Guinea-Bissau'),
       ('81', 'gy', 'GUYANA', 'Guyana'),
       ('82', 'ht', 'HAITI', 'Haiti'),
       ('83', 'hm', 'HEARD ISLAND AND MCDONALD ISLANDS', 'Heard Island And Mcdonald Islands'),
       ('84', 'va', 'HOLY SEE (VATICAN CITY STATE)', 'Holy See (Vatican City State)'),
       ('85', 'hn', 'HONDURAS', 'Honduras'),
       ('86', 'hk', 'HONG KONG', 'Hong Kong'),
       ('87', 'is', 'ICELAND', 'Iceland'),
       ('88', 'ir', 'IRAN, ISLAMIC REPUBLIC OF', 'Iran, Islamic Republic of'),
       ('89', 'iq', 'IRAQ', 'Iraq'),
       ('90', 'im', 'ISLE OF MAN', 'Isle Of Man'),
       ('91', 'il', 'ISRAEL', 'Israel'),
       ('92', 'jm', 'JAMAICA', 'Jamaica'),
       ('93', 'je', 'JERSEY', 'Jersey'),
       ('94', 'jo', 'JORDAN', 'Jordan'),
       ('95', 'kz', 'KAZAKHSTAN', 'Kazakhstan'),
       ('96', 'ki', 'KIRIBATI', 'Kiribati'),
       ('97', 'kp', 'KOREA, DEMOCRATIC PEOPLES REPUBLIC OF', 'Korea, Democratic Peoples Republic of'),
       ('98', 'jp', 'JAPAN', 'Japan'),
       ('99', 'ke', 'KENYA', 'Kenya'),
       ('100', 'in', 'INDIA', 'India'),
       ('101', 'id', 'INDONESIA', 'Indonesia'),
       ('102', 'xk', 'KOSOVO', 'Kosovo'),
       ('103', 'kw', 'KUWAIT', 'Kuwait'),
       ('104', 'kg', 'KYRGYZSTAN', 'Kyrgyzstan'),
       ('105', 'la', 'LAO PEOPLES DEMOCRATIC REPUBLIC', 'Lao Peoples Democratic Republic'),
       ('106', 'lb', 'LEBANON', 'Lebanon'),
       ('107', 'ls', 'LESOTHO', 'Lesotho'),
       ('108', 'lr', 'LIBERIA', 'Liberia'),
       ('109', 'ly', 'LIBYA', 'Libya'),
       ('110', 'li', 'LIECHTENSTEIN', 'Liechtenstein'),
       ('111', 'mo', 'MACAO', 'Macao'),
       ('112', 'mk', 'MACEDONIA, THE FORMER YUGOSLAV REPUBLIC OF', 'Macedonia, the Former Yugoslav Republic of'),
       ('113', 'mg', 'MADAGASCAR', 'Madagascar'),
       ('114', 'mw', 'MALAWI', 'Malawi'),
       ('115', 'my', 'MALAYSIA', 'Malaysia'),
       ('116', 'mv', 'MALDIVES', 'Maldives'),
       ('117', 'ml', 'MALI', 'Mali'),
       ('118', 'mh', 'MARSHALL ISLANDS', 'Marshall Islands'),
       ('119', 'mq', 'MARTINIQUE', 'Martinique'),
       ('120', 'mr', 'MAURITANIA', 'Mauritania'),
       ('121', 'mu', 'MAURITIUS', 'Mauritius'),
       ('122', 'yt', 'MAYOTTE', 'Mayotte'),
       ('123', 'mx', 'MEXICO', 'Mexico'),
       ('124', 'fm', 'MICRONESIA, FEDERATED STATES OF', 'Micronesia, Federated States of'),
       ('125', 'md', 'MOLDOVA, REPUBLIC OF', 'Moldova, Republic of'),
       ('126', 'mc', 'MONACO', 'Monaco'),
       ('127', 'mn', 'MONGOLIA', 'Mongolia'),
       ('128', 'me', 'MONTENEGRO', 'Montenegro'),
       ('129', 'ms', 'MONTSERRAT', 'Montserrat'),
       ('130', 'ma', 'MOROCCO', 'Morocco'),
       ('131', 'mz', 'MOZAMBIQUE', 'Mozambique'),
       ('132', 'mm', 'MYANMAR', 'Myanmar'),
       ('133', 'na', 'NAMIBIA', 'Namibia'),
       ('134', 'nr', 'NAURU', 'Nauru'),
       ('135', 'nc', 'NEW CALEDONIA', 'New Caledonia'),
       ('136', 'nz', 'NEW ZEALAND', 'New Zealand'),
       ('137', 'ni', 'NICARAGUA', 'Nicaragua'),
       ('138', 'ne', 'NIGER', 'Niger'),
       ('139', 'ng', 'NIGERIA', 'Nigeria'),
       ('140', 'nu', 'NIUE', 'Niue'),
       ('141', 'nf', 'NORFOLK ISLAND', 'Norfolk Island'),
       ('142', 'mp', 'NORTHERN MARIANA ISLANDS', 'Northern Mariana Islands'),
       ('143', 'no', 'NORWAY', 'Norway'),
       ('144', 'om', 'OMAN', 'Oman'),
       ('145', 'pw', 'PALAU', 'Palau'),
       ('146', 'ps', 'PALESTINIAN TERRITORY, OCCUPIED', 'Palestinian Territory, Occupied'),
       ('147', 'pa', 'PANAMA', 'Panama'),
       ('148', 'pg', 'PAPUA NEW GUINEA', 'Papua New Guinea'),
       ('149', 'py', 'PARAGUAY', 'Paraguay'),
       ('150', 'pe', 'PERU', 'Peru'),
       ('151', 'ph', 'PHILIPPINES', 'Philippines'),
       ('152', 'pn', 'PITCAIRN', 'Pitcairn'),
       ('153', 'pr', 'PUERTO RICO', 'Puerto Rico'),
       ('154', 'qa', 'QATAR', 'Qatar'),
       ('155', 're', 'REUNION', 'Reunion'),
       ('156', 'ru', 'RUSSIAN FEDERATION', 'Russian Federation'),
       ('157', 'rw', 'RWANDA', 'Rwanda'),
       ('158', 'bl', 'SAINT BARTHÉLEMY', 'Saint Barthélemy'),
       ('159', 'sh', 'SAINT HELENA', 'Saint Helena'),
       ('160', 'kn', 'SAINT KITTS AND NEVIS', 'Saint Kitts and Nevis'),
       ('161', 'lc', 'SAINT LUCIA', 'Saint Lucia'),
       ('162', 'mf', 'SAINT MARTIN (FRENCH PART)', 'Saint Martin (French part)'),
       ('163', 'pm', 'SAINT PIERRE AND MIQUELON', 'Saint Pierre and Miquelon'),
       ('164', 'vc', 'SAINT VINCENT AND THE GRENADINES', 'Saint Vincent and the Grenadines'),
       ('165', 'ws', 'SAMOA', 'Samoa'),
       ('166', 'sm', 'SAN MARINO', 'San Marino'),
       ('167', 'st', 'SAO TOME AND PRINCIPE', 'Sao Tome and Principe'),
       ('168', 'sa', 'SAUDI ARABIA', 'Saudi Arabia'),
       ('169', 'sn', 'SENEGAL', 'Senegal'),
       ('170', 'rs', 'SERBIA', 'Serbia'),
       ('171', 'sc', 'SEYCHELLES', 'Seychelles'),
       ('172', 'sl', 'SIERRA LEONE', 'Sierra Leone'),
       ('173', 'sg', 'SINGAPORE', 'Singapore'),
       ('174', 'sx', 'SINT MAARTEN', 'Sint Maarten'),
       ('175', 'sb', 'SOLOMON ISLANDS', 'Solomon Islands'),
       ('176', 'so', 'SOMALIA', 'Somalia'),
       ('177', 'za', 'SOUTH AFRICA', 'South Africa'),
       ('178', 'gs', 'SOUTH GEORGIA AND THE SOUTH SANDWICH ISLANDS', 'South Georgia and the South Sandwich Islands'),
       ('179', 'ss', 'SOUTH SUDAN', 'South Sudan'),
       ('180', 'sd', 'SUDAN', 'Sudan'),
       ('181', 'sr', 'SURINAME', 'Suriname'),
       ('182', 'sj', 'SVALBARD AND JAN MAYEN', 'Svalbard and Jan Mayen'),
       ('183', 'sz', 'SWAZILAND', 'Swaziland'),
       ('184', 'ch', 'SWITZERLAND', 'Switzerland'),
       ('185', 'sy', 'SYRIAN ARAB REPUBLIC', 'Syrian Arab Republic'),
       ('186', 'tw', 'TAIWAN, PROVINCE OF CHINA', 'Taiwan, Province of China'),
       ('187', 'tj', 'TAJIKISTAN', 'Tajikistan'),
       ('188', 'th', 'THAILAND', 'Thailand'),
       ('189', 'tl', 'TIMOR LESTE', 'Timor Leste'),
       ('190', 'tg', 'TOGO', 'Togo'),
       ('191', 'tk', 'TOKELAU', 'Tokelau'),
       ('192', 'to', 'TONGA', 'Tonga'),
       ('193', 'tt', 'TRINIDAD AND TOBAGO', 'Trinidad and Tobago'),
       ('194', 'tn', 'TUNISIA', 'Tunisia'),
       ('195', 'tm', 'TURKMENISTAN', 'Turkmenistan'),
       ('196', 'tc', 'TURKS AND CAICOS ISLANDS', 'Turks and Caicos Islands'),
       ('197', 'np', 'NEPAL', 'Nepal'),
       ('198', 'tz', 'TANZANIA, UNITED REPUBLIC OF', 'Tanzania, United Republic of'),
       ('199', 'kr', 'KOREA, REPUBLIC OF', 'Korea, Republic of'),
       ('200', 'tv', 'TUVALU', 'Tuvalu'),
       ('201', 'ug', 'UGANDA', 'Uganda'),
       ('202', 'ua', 'UKRAINE', 'Ukraine'),
       ('203', 'ae', 'UNITED ARAB EMIRATES', 'United Arab Emirates'),
       ('204', 'um', 'UNITED STATES MINOR OUTLYING ISLANDS', 'United States Minor Outlying Islands'),
       ('205', 'uy', 'URUGUAY', 'Uruguay'),
       ('206', 'uz', 'UZBEKISTAN', 'Uzbekistan'),
       ('207', 'vu', 'VANUATU', 'Vanuatu'),
       ('208', 've', 'VENEZUELA', 'Venezuela'),
       ('209', 'vn', 'VIET NAM', 'Viet Nam'),
       ('210', 'vg', 'VIRGIN ISLANDS, BRITISH', 'Virgin Islands, British'),
       ('211', 'vi', 'VIRGIN ISLANDS, U.S.', 'Virgin Islands, U.S.'),
       ('212', 'wf', 'WALLIS AND FUTUNA', 'Wallis and Futuna'),
       ('213', 'eh', 'WESTERN SAHARA', 'Western Sahara'),
       ('214', 'ye', 'YEMEN', 'Yemen'),
       ('215', 'zw', 'ZIMBABWE', 'Zimbabwe'),
       ('216', 'ax', 'ÅLAND ISLANDS', 'Åland Islands'),
       ('217', 'ca', 'CANADA', 'Canada'),
       ('218', 'lk', 'SRI LANKA', 'Sri Lanka'),
       ('219', 'tr', 'TURKEY', 'Turkey'),
       ('220', 'gb', 'UNITED KINGDOM', 'United Kingdom'),
       ('221', 'us', 'UNITED STATES', 'United States'),
       ('222', 'at', 'AUSTRIA', 'Austria'),
       ('223', 'be', 'BELGIUM', 'Belgium'),
       ('224', 'bg', 'BULGARIA', 'Bulgaria'),
       ('225', 'hr', 'CROATIA', 'Croatia'),
       ('226', 'cy', 'CYPRUS', 'Cyprus'),
       ('227', 'cz', 'CZECH REPUBLIC', 'Czech Republic'),
       ('228', 'dk', 'DENMARK', 'Denmark'),
       ('229', 'ee', 'ESTONIA', 'Estonia'),
       ('230', 'fi', 'FINLAND', 'Finland'),
       ('231', 'fr', 'FRANCE', 'France'),
       ('232', 'de', 'GERMANY', 'Germany'),
       ('233', 'gr', 'GREECE', 'Greece'),
       ('234', 'hu', 'HUNGARY', 'Hungary'),
       ('235', 'ie', 'IRELAND', 'Ireland'),
       ('236', 'it', 'ITALY', 'Italy'),
       ('237', 'lv', 'LATVIA', 'Latvia'),
       ('238', 'lt', 'LITHUANIA', 'Lithuania'),
       ('239', 'lu', 'LUXEMBOURG', 'Luxembourg'),
       ('240', 'mt', 'MALTA', 'Malta'),
       ('241', 'nl', 'NETHERLANDS', 'Netherlands'),
       ('242', 'pl', 'POLAND', 'Poland'),
       ('243', 'pt', 'PORTUGAL', 'Portugal'),
       ('244', 'ro', 'ROMANIA', 'Romania'),
       ('245', 'sk', 'SLOVAKIA', 'Slovakia'),
       ('246', 'si', 'SLOVENIA', 'Slovenia'),
       ('247', 'es', 'SPAIN', 'Spain'),
       ('248', 'se', 'SWEDEN', 'Sweden'),
       ('249', 'zm', 'ZAMBIA', 'Zambia');

-- Insert into role table
INSERT INTO role (id, name, "displayName", permissions)
VALUES ('1', 'owner', 'Owner', '[
  {
    "action": "manage",
    "subject": "all"
  }
]'),
       ('2', 'manager', 'Manager', '[
         {
           "action": "manage",
           "subject": "all"
         }
       ]'),
       ('3', 'factory_staff', 'Factory Staff', '[
         {
           "action": "read",
           "subject": "all"
         }
       ]'),
       ('4', 'field_supervisor', 'Field Supervisor', '[
         {
           "action": "read",
           "subject": "all"
         }
       ]'),
       ('5', 'zone_lead_farmer', 'Zone Lead Farmer', '[
         {
           "action": "create",
           "subject": "Farm"
         },
         {
           "action": "read",
           "subject": "Farm"
         },
         {
           "action": "update",
           "subject": "Farm"
         }
       ]'),
       ('6', 'farmer', 'Farmer', '[
         {
           "action": "read",
           "subject": "Farm"
         }
       ]');



-- For Natures Nectar

-- Insert Business
INSERT INTO business (id, name, industry, description, bio, photo, city, "countryId", "ownerId", "createdAt",
                      "updatedAt")
VALUES ('1', 'Natures Nectar', 'Honey', 'Zambias most sustainable & impactful honey',
        'Natures nectar works with rural communities in Zambia to create a sustainable income and enhance the protection of local forests through beekeeping. With few resources and little access to market, rural Zambian farmer are challenged to find a viable income that doesnt negatively impact the environment. Honey has been a major crop and income source for many communities in Zambia for hundreds of years, and with just a few changes, we can keep this practice alive. Nature’s nectar was founded with the core belief that sustainability and impact are essential to success. It is the intention of nature’s nectar to make top bar beekeeping the most prevalent way for rural communities to produce honey all over Zambia. This will ensure the tradition of beekeeping is kept alive, advanced, and a long term value is placed directly on forests and accompanying ecosystems.',
        'https://t.ly/Pe0GE', 'Lusaka', '249', '1', NOW(), NOW());

-- Admin user
INSERT INTO "user" (id, "firstName", "lastName", email, phone, dob, "jobTitle", "isOnboarded", location, latitude,
                    longitude, "roleId", "employerId", "createdAt", "updatedAt")
VALUES ('ADMIN', 'Admin', 'Initial User', '<EMAIL>', '*********', '1975-01-01', 'App Admin',
        TRUE, 'Chisengisengi', '-12.2074435', '24.9057937', '1', '1', NOW(), NOW());

INSERT INTO account
(id,"accountId","providerId","userId","accessToken","refreshToken","idToken","accessTokenExpiresAt","refreshTokenExpiresAt","scope","password","createdAt","updatedAt")
VALUES
    ('ADMIN','ADMIN','credential','ADMIN',NULL,NULL,NULL,NULL,NULL,NULL,'b0a1ec9dc0c566d5598593f18b3ea0b9:f65f597b451b6f10ce69a9036743f3f50c5240599a730da87ce2fe52df680183c826b53d353494c5a7324ed96fb5a2ff6f3a7a414d7003402eb4257e340656f6','2025-01-29 08:40:00.575','2025-01-29 08:40:00.575')
;

-- Insert Beehive asset type
INSERT INTO "assetType" (id, type, description, "createdAt", "updatedAt")
VALUES ('1', 'beehive', 'Beehive asset', NOW(), NOW());
