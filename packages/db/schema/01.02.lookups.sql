CREATE TABLE factory
(
    id          text NOT NULL,
    name        text NOT NULL,
    business_id text NOT NULL,

    CONSTRAINT factory_pkey PRIMARY KEY (id),
    CONSTRAINT certificates_business_id_businesses_id_fk FOREIGN KEY (business_id) REFERENCES business (id) ON DELETE CASCADE
);

CREATE TABLE honey_process_type
(
    id   text NOT NULL,
    name text NOT NULL,

    CONSTRAINT haney_process_type_pkey PRIMARY KEY (id)
);
