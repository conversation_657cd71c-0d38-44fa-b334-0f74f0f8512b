
-- Insert Farm
INSERT INTO farm (id, name, city, "countryId", latitude, longitude, "createdAt", "updatedAt")
VALUES ('1', 'Farm A', 'Chisengisengi', '249', '-12.2074435', '24.9057937', NOW(), NOW()),
       ('2', 'Farm B', 'Lusaka', '249', '-12.2074000', '24.9057000', NOW(), NOW()),
       ('3', 'Farm C', 'Lusaka', '249', '-12.2074100', '24.9057100', NOW(), NOW()),
       ('4', 'Farm D', 'Lusaka', '249', '-12.2074200', '24.9057200', NOW(), NOW()),
       ('5', 'Farm E', 'Lusaka', '249', '-12.2074300', '24.9057300', NOW(), NOW()),
       ('6', 'Farm F', 'Lusaka', '249', '-12.2074400', '24.9057400', NOW(), NOW()),
       ('7', 'Farm G', '<PERSON>sa<PERSON>', '249', '-12.2074500', '24.9057500', NOW(), NOW()),
       ('8', 'Farm H', 'Lusaka', '249', '-12.2074600', '24.9057600', NOW(), NOW()),
       ('9', 'Farm I', 'Lusaka', '249', '-12.2074700', '24.9057700', NOW(), NOW()),
       ('10', 'Farm J', 'Lusaka', '249', '-12.2074800', '24.9057800', NOW(), NOW()),
       ('11', 'Farm K', 'Lusaka', '249', '-12.2074900', '24.9057900', NOW(), NOW());

-- Insert businessFarm join table
INSERT INTO "businessFarm" ("businessId", "farmId", "createdAt", "updatedAt")
VALUES ('1', '1', NOW(), NOW()),
       ('1', '2', NOW(), NOW()),
       ('1', '3', NOW(), NOW()),
       ('1', '4', NOW(), NOW()),
       ('1', '5', NOW(), NOW()),
       ('1', '6', NOW(), NOW()),
       ('1', '7', NOW(), NOW()),
       ('1', '8', NOW(), NOW()),
       ('1', '9', NOW(), NOW()),
       ('1', '10', NOW(), NOW()),
       ('1', '11', NOW(), NOW());

-- Insert Factory Staff
INSERT INTO "user" (id, first_name, last_name, email, phone, dob, job_title, "isOnboarded", location, latitude,
                    longitude, "role", "employerId", created_at, updated_at)
VALUES ('2', 'Bill', 'Jones', '<EMAIL>', '762733711', '1980-01-01', 'Factory Staff', TRUE,
        'Chisengisengi', '-12.2074435', '24.9057937', 'Factory Staff', '1', NOW(), NOW());

-- Insert Field Supervisor
INSERT INTO "user" (id, first_name, last_name, email, phone, dob, job_title, "isOnboarded", location, latitude,
                    longitude, "role", "employerId", created_at, updated_at)
VALUES ('3', 'Alice', 'Smith', '<EMAIL>', '762733711', '1980-01-01', 'Field Supervisor',
        TRUE, 'Chisengisengi', '-12.2074435', '24.9057937', 'Field Supervisor', '1', NOW(), NOW());

-- Insert Zone Lead Farmer
INSERT INTO "user" (id, first_name, last_name, email, phone, dob, job_title, "isOnboarded", location, latitude,
                    longitude, "role", created_at, updated_at)
VALUES ('4', 'John', 'Smith', '<EMAIL>', '762733712', '1983-01-01', 'Zone Lead Farmer',
        TRUE, 'Chisengisengi', '-12.2074435', '24.9057937', 'Zone Lead Farmer', NOW(), NOW());

INSERT INTO farmer (id, "firstName", "lastName", gender, phone, "maritalStatus", dob, "householdSize",
                    "estimatedAnnualIncome", "sourceOfIncome", "countryId", "role", "userId", "farmId", region_id, zone_id,
                    "metadata", "createdAt", "updatedAt", chiefdom_id, village_id)
VALUES ('4', 'Joe', 'Smith', 'male', '762733712', 'single', '1983-01-02T22:00:00', 4, 1200.00, 'Farming', '249', 'Zone Lead Farmer', '4', '1',
        'EP', 'SIM', '{}', NOW(), NOW(), 'LUEMBE', 'CHILEKANI' );

-- Insert farmer
INSERT INTO "user" (id, first_name, last_name, email, phone, dob, job_title, "isOnboarded", location, latitude,
                    longitude, "role", created_at, updated_at)
VALUES ('5', 'Joe', 'Montana', '<EMAIL>', '762733712', '1983-01-02', 'Farmer', TRUE,
        'Chisengisengi', '-12.2074435', '24.9057937', 'Farmer', NOW(), NOW()),
       ('6', 'Anne', 'Taylor', '<EMAIL>', '*********', '1984-01-01', 'Farmer', TRUE, 'Mumbwa',
        '-13.132123', '26.9057937', 'Farmer', NOW(), NOW()),
       ('7', 'Susan', 'Miller', '<EMAIL>', '762733714', '1985-02-02', 'Farmer', TRUE, 'Kitwe',
        '-12.809667', '28.213564', 'Farmer', NOW(), NOW()),
       ('8', 'Mike', 'Johnson', '<EMAIL>', '762733715', '1986-03-03', 'Farmer', TRUE, 'Ndola',
        '-13.192345', '27.859333', 'Farmer', NOW(), NOW()),
       ('9', 'Lucy', 'Wilson', '<EMAIL>', '762733716', '1987-04-04', 'Farmer', TRUE, 'Lusaka',
        '-15.416667', '28.283333', 'Farmer', NOW(), NOW()),
       ('10', 'John', 'Doe', '<EMAIL>', '762733717', '1988-05-05', 'Farmer', TRUE, 'Kafue',
        '-15.766667', '28.183333', 'Farmer', NOW(), NOW()),
       ('11', 'Alex', 'Smith', '<EMAIL>', '762733718', '1989-06-06', 'Farmer', TRUE,
        'Livingstone', '-17.856667', '25.867667', 'Farmer', NOW(), NOW()),
       ('12', 'Mary', 'Jones', '<EMAIL>', '762733719', '1990-07-07', 'Farmer', TRUE, 'Chipata',
        '-13.633333', '32.633333', 'Farmer', NOW(), NOW()),
       ('13', 'Tom', 'Brown', '<EMAIL>', '762733720', '1991-08-08', 'Farmer', TRUE, 'Kasama',
        '-10.212', '31.180', 'Farmer', NOW(), NOW()),
       ('14', 'Ron', 'Bates', '<EMAIL>', '762733712', '1983-01-01', 'Farmer', TRUE,
        'Chisengisengi', '-12.2074435', '24.9057937', 'Farmer', NOW(), NOW());

INSERT INTO farmer (id, "firstName", "lastName", gender, phone, "maritalStatus", dob, "householdSize",
                    "estimatedAnnualIncome", "sourceOfIncome", "countryId", "role", "userId", "farmId", region_id,
                    zone_id, "metadata", "createdAt", "updatedAt", chiefdom_id, village_id)
VALUES ('5', 'Joe', 'Montana', 'male', '762733712', 'single', '1983-01-02T22:00:00', 4, 1200.00, 'Farming', '249','Farmer', '5', '1',
        'EP', 'SIM', '{}', NOW(), NOW(), 'LUEMBE', 'CHILEKANI'),
       ('6', 'Anne', 'Taylor', 'female', '*********', 'married', '1984-01-01T22:00:00', 5, 1500.00, 'Farming', '249', 'Farmer', '6',
        '1', 'EP', 'SIM', '{}', NOW(), NOW(), 'LUEMBE', 'CHILEKANI'),
       ('7', 'Susan', 'Miller', 'female', '762733714', 'married', '1985-02-02T22:00:00', 6, 1800.00, 'Farming', '249', 'Farmer', '7',
        '1', 'EP', 'SIM', '{}', NOW(), NOW(), 'LUEMBE', 'CHILEKANI'),
       ('8', 'Mike', 'Johnson', 'male', '762733715', 'single', '1986-03-03T22:00:00', 3, 1100.00, 'Farming', '249', 'Farmer', '8',
        '1', 'EP', 'SIM', '{}', NOW(), NOW(), 'LUEMBE', 'CHILEKANI'),
       ('9', 'Lucy', 'Wilson', 'female', '762733716', 'married', '1987-04-04T22:00:00', 4, 1300.00, 'Farming', '249', 'Farmer', '9',
        '1', 'EP', 'SIM', '{}', NOW(), NOW(), 'LUEMBE', 'CHILEKANI'),
       ('10', 'John', 'Doe', 'male', '762733717', 'single', '1988-05-05T22:00:00', 5, 1250.00, 'Farming', '249', 'Farmer', '10', '1',
        'EP', 'SIM', '{}', NOW(), NOW(), 'LUEMBE', 'CHILEKANI'),
       ('11', 'Alex', 'Smith', 'male', '762733718', 'single', '1989-06-06T22:00:00', 2, 1000.00, 'Farming', '249', 'Farmer', '11',
        '1', 'EP', 'SIM', '{}', NOW(), NOW(), 'LUEMBE', 'CHILEKANI'),
       ('12', 'Mary', 'Jones', 'female', '762733719', 'married', '1990-07-07T22:00:00', 6, 1600.00, 'Farming', '249', 'Farmer', '12',
        '1', 'EP', 'SIM', '{}', NOW(), NOW(), 'LUEMBE', 'CHILEKANI'),
       ('13', 'Tom', 'Brown', 'male', '762733720', 'single', '1991-08-08T22:00:00', 3, 1150.00, 'Farming', '249', 'Farmer', '13', '1',
        'EP', 'SIM', '{}', NOW(), NOW(), 'LUEMBE', 'CHILEKANI'),
       ('14', 'Ron', 'Bates', 'male', '762733712', 'single', '1983-01-01T22:00:00', 4, 1220.00, 'Farming', '249', 'Farmer', '14', '1',
        'EP', 'SIM', '{}', NOW(), NOW(), 'LUEMBE', 'CHILEKANI');

INSERT INTO public."user" (id,"external_uid",username,"name",first_name,last_name,email,phone,dob,job_title,photos,"location",latitude,longitude,role,"employerId",state,updated_at,"isOnboarded","emailVerified",image,created_at) VALUES
('817Hs1WZ9PwCkeRwipPOINTDIeYN_002','lmlH0RQcq3VrDQi9OrvpRjJsp7P2',NULL,'The Manager 2','Manager name 2','Manager lastname','<EMAIL>','*********','1980-01-01','Manager','{}','Chisengisengi','-12.2074435','24.9057937','Manager','1','{}','2025-02-13 10:43:19',true,false,NULL,'2025-02-13 10:43:19'),
('817Hs1WZ9PwCkeRwipPOINTDIeYN_003','sqMqB3sGuOhK6IZKBR1ezPJCW1n2',NULL,'The Manager 3','Manager name 3','Manager lastname','<EMAIL>','*********','1980-01-01','Manager','{}','Chisengisengi','-12.2074435','24.9057937','Manager','1','{}','2025-02-13 10:43:19',true,false,NULL,'2025-02-13 10:43:19'),
('817Hs1WZ9PwCkeRwipPOINTDIeYN_004','91vTe3rqrbfUwYmKqDlN5107YcT2',NULL,'The Manager 4','Manager name 4','Manager lastname','<EMAIL>','*********','1980-01-01','Manager','{}','Chisengisengi','-12.2074435','24.9057937','Manager','1','{}','2025-02-13 10:43:19',true,false,NULL,'2025-02-13 10:43:19'),
('817Hs1WZ9PwCkeRwipPOINTDIeYN_005','rE0WtFWeyqaySpjjaIhZ6nc6hIt2',NULL,'The Manager 5','Manager name 5','Manager lastname','<EMAIL>','*********','1980-01-01','Manager','{}','Chisengisengi','-12.2074435','24.9057937','Manager','1','{}','2025-02-13 10:43:19',true,false,NULL,'2025-02-13 10:43:19'),
('817Hs1WZ9PwCkeRwipPOINTDIeYN_006',NULL,NULL,'The Manager 6','Manager name 6','Manager lastname','<EMAIL>','*********','1980-01-01','Manager','{}','Chisengisengi','-12.2074435','24.9057937','Manager','1','{}','2025-02-13 10:43:19',true,false,NULL,'2025-02-13 10:43:19')
;

INSERT INTO account
(id,"accountId","providerId","userId","accessToken","refreshToken","idToken","accessTokenExpiresAt","refreshTokenExpiresAt","scope","password","createdAt","updatedAt")
VALUES
    ('MDCxSACz0Zp02mt3pkrvwdsHAYNxquDa','817Hs1WZ9PwCkeRwipPOINTDIeYN15fq','credential','817Hs1WZ9PwCkeRwipPOINTDIeYN15fq',NULL,NULL,NULL,NULL,NULL,NULL,'b0a1ec9dc0c566d5598593f18b3ea0b9:f65f597b451b6f10ce69a9036743f3f50c5240599a730da87ce2fe52df680183c826b53d353494c5a7324ed96fb5a2ff6f3a7a414d7003402eb4257e340656f6','2025-01-29 08:40:00.575','2025-01-29 08:40:00.575'),
    ('MDCxSACz0Zp02mt3pkrvwdsHAYNx_001','817Hs1WZ9PwCkeRwipPOINTDIeYN_001','credential','817Hs1WZ9PwCkeRwipPOINTDIeYN_001',NULL,NULL,NULL,NULL,NULL,NULL,'b0a1ec9dc0c566d5598593f18b3ea0b9:f65f597b451b6f10ce69a9036743f3f50c5240599a730da87ce2fe52df680183c826b53d353494c5a7324ed96fb5a2ff6f3a7a414d7003402eb4257e340656f6','2025-02-13 10:46:41','2025-02-13 10:46:30')
;

INSERT INTO account
(id,"accountId","providerId","userId","accessToken","refreshToken","idToken","accessTokenExpiresAt","refreshTokenExpiresAt","scope","password","createdAt","updatedAt")
VALUES
    ('MDCxSACz0Zp02mt3pkrvwdsHAYNx_002','817Hs1WZ9PwCkeRwipPOINTDIeYN_002','credential','817Hs1WZ9PwCkeRwipPOINTDIeYN_002',NULL,NULL,NULL,NULL,NULL,NULL,'b0a1ec9dc0c566d5598593f18b3ea0b9:f65f597b451b6f10ce69a9036743f3f50c5240599a730da87ce2fe52df680183c826b53d353494c5a7324ed96fb5a2ff6f3a7a414d7003402eb4257e340656f6','2025-02-13 10:46:41','2025-02-13 10:46:30'),
    ('MDCxSACz0Zp02mt3pkrvwdsHAYNx_003','817Hs1WZ9PwCkeRwipPOINTDIeYN_003','credential','817Hs1WZ9PwCkeRwipPOINTDIeYN_003',NULL,NULL,NULL,NULL,NULL,NULL,'b0a1ec9dc0c566d5598593f18b3ea0b9:f65f597b451b6f10ce69a9036743f3f50c5240599a730da87ce2fe52df680183c826b53d353494c5a7324ed96fb5a2ff6f3a7a414d7003402eb4257e340656f6','2025-02-13 10:46:41','2025-02-13 10:46:30'),
    ('MDCxSACz0Zp02mt3pkrvwdsHAYNx_004','817Hs1WZ9PwCkeRwipPOINTDIeYN_004','credential','817Hs1WZ9PwCkeRwipPOINTDIeYN_004',NULL,NULL,NULL,NULL,NULL,NULL,'b0a1ec9dc0c566d5598593f18b3ea0b9:f65f597b451b6f10ce69a9036743f3f50c5240599a730da87ce2fe52df680183c826b53d353494c5a7324ed96fb5a2ff6f3a7a414d7003402eb4257e340656f6','2025-02-13 10:46:41','2025-02-13 10:46:30'),
    ('MDCxSACz0Zp02mt3pkrvwdsHAYNx_005','817Hs1WZ9PwCkeRwipPOINTDIeYN_005','credential','817Hs1WZ9PwCkeRwipPOINTDIeYN_005',NULL,NULL,NULL,NULL,NULL,NULL,'b0a1ec9dc0c566d5598593f18b3ea0b9:f65f597b451b6f10ce69a9036743f3f50c5240599a730da87ce2fe52df680183c826b53d353494c5a7324ed96fb5a2ff6f3a7a414d7003402eb4257e340656f6','2025-02-13 10:46:41','2025-02-13 10:46:30'),
    ('MDCxSACz0Zp02mt3pkrvwdsHAYNx_006','817Hs1WZ9PwCkeRwipPOINTDIeYN_006','credential','817Hs1WZ9PwCkeRwipPOINTDIeYN_006',NULL,NULL,NULL,NULL,NULL,NULL,'b0a1ec9dc0c566d5598593f18b3ea0b9:f65f597b451b6f10ce69a9036743f3f50c5240599a730da87ce2fe52df680183c826b53d353494c5a7324ed96fb5a2ff6f3a7a414d7003402eb4257e340656f6','2025-02-13 10:46:41','2025-02-13 10:46:30')
;



-- Insert certificate
INSERT INTO certificate (id, "businessId", name, description, "certificateNumber", "certificateDate", "createdAt",
                         "updatedAt")
VALUES ('1', '1', 'African Honey Board Member Certificate', 'African Honey Board', 'Q257hfn756', '2022-01-01', NOW(),
        NOW()),
       ('2', '1', 'Certified Organic', 'Certified Organic Honey', 'Q1567fn756', '2020-01-09', NOW(), NOW()),
       ('3', '1', 'Honey Board Member Certificate', 'Certificate description', '88834782384hhh8asdF', '2018-05-17',
        NOW(), NOW());

-- Insert Harvest Location
INSERT INTO "harvestLocation" (id, name, latitude, longitude, "farmId", "createdAt", "updatedAt")
VALUES ('1', 'CS_ - Chisengisengi', '-12.2074435', '24.9057937', '1', NOW(), NOW());

INSERT INTO asset (id, name, "assetTypeId", "businessId", "createdBy", "createdAt", "updatedAt")
VALUES ('1', 'Beehive A','1', '1', '3', NOW(), NOW()),
       ('2', 'Beehive B','1', '1', '3', NOW(), NOW()),
       ('3', 'Beehive C','1', '1', '3', NOW(), NOW()),
       ('4', 'Beehive D','1', '1', '3', NOW(), NOW()),
       ('5', 'Beehive E','1', '1', '3', NOW(), NOW());

INSERT INTO "assetStatus" (id, status, latitude, longitude, metadata, "assetId", "farmerId", "createdBy", "createdAt",
                           "updatedAt")
VALUES ('1', 'active', '-12.2074435', '24.9057937', '{
  "id": "1",
  "beehive_functional": true,
  "beehive_repair": true,
  "beehive_observation": "Beehive is ok",
  "beehive_occupied": true,
  "photos": [
    "https://t.ly/Pe0GE",
    "https://t.ly/dC1Ja",
    "https://t.ly/q5AYJ"
  ]
}', '1', '4', '3', NOW(), NOW()),
       ('2', 'active', '-12.2074435', '24.9057937', '{
         "id": "2",
         "beehive_functional": true,
         "beehive_repair": true,
         "beehive_observation": "Beehive needs to be hanged",
         "beehive_occupied": false,
         "photos": [
           "https://t.ly/Pe0GE",
           "https://t.ly/dC1Ja",
           "https://t.ly/q5AYJ"
         ]
       }', '2', '4', '3', NOW(), NOW()),
       ('3', 'active', '-12.2074435', '24.9057937', '{
         "id": "3",
         "beehive_functional": true,
         "beehive_repair": false,
         "beehive_observation": "Beehive needs to be moved",
         "beehive_occupied": true,
         "photos": [
           "https://t.ly/Pe0GE",
           "https://t.ly/dC1Ja",
           "https://t.ly/q5AYJ"
         ]
       }', '3', '4', '3', NOW(), NOW()),
       ('4', 'active', '-12.2074435', '24.9057937', '{
         "id": "4",
         "beehive_functional": false,
         "beehive_repair": true,
         "beehive_observation": "Beehive needs a new hook",
         "beehive_occupied": false,
         "photos": [
           "https://t.ly/Pe0GE",
           "https://t.ly/dC1Ja",
           "https://t.ly/q5AYJ"
         ]
       }', '4', '4', '3', NOW(), NOW()),
       ('5', 'active', '-12.2074435', '24.9057937', '{
         "id": "5",
         "beehive_functional": false,
         "beehive_repair": false,
         "beehive_observation": "Beehive needs to be wired",
         "beehive_occupied": true,
         "photos": [
           "https://t.ly/Pe0GE",
           "https://t.ly/dC1Ja",
           "https://t.ly/q5AYJ"
         ]
       }', '5', '4', '3', NOW(), NOW());
