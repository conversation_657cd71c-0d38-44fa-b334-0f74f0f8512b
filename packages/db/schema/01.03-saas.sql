-- Create a dedicated schema for PalmyraPro administration and configuration
CREATE SCHEMA IF NOT EXISTS admin_palmyrapro;

-- Stores categories for organizing and grouping per commodities, features, products, etc.
CREATE TABLE admin_palmyrapro.categories
(
    id          UUID PRIMARY KEY, -- Unique identifier for the category entry
    name        TEXT NOT NULL,    -- Display name or title of the category
    description TEXT              -- Optional description providing additional category details
);

-- Manages multi-tenant capability, storing information about different tenant spaces
CREATE TABLE admin_palmyrapro.tenants
(
    id          UUID PRIMARY KEY,     -- Unique identifier for the tenant
    space_name  TEXT NOT NULL UNIQUE, -- Unique namespace for the tenant. Also used as PostgreSQL schema
    name        TEXT NOT NULL,        -- Display name of the tenant
    description TEXT                  -- Optional description of the tenant
);

-- Stores versioned definitions of items with their schemas and configuration
CREATE TABLE admin_palmyrapro.log_item_definition
(
    id                  UUID NOT NULL,                                    -- Unique identifier for the item definition
    version             INT  NOT NULL,                                    -- Version number of the item definition
    is_latest           BOOLEAN,                                          -- Flag indicating if this is the latest version
    creation_ts         TIMESTAMP,                                        -- Timestamp when this version was created
    schema              JSONB,                                            -- JSON schema defining the item structure
    enable_traceability BOOLEAN DEFAULT TRUE,                             -- Flag to enable/disable traceability features
    enable_versioning   BOOLEAN DEFAULT TRUE,                             -- Flag to enable/disable versioning features
    description         TEXT,                                             -- Optional description of the item definition

    PRIMARY KEY (id, version)                                             -- Composite primary key of id and version
);


-- Junction table for many-to-many relationship between item definitions and categories
CREATE TABLE admin_palmyrapro.item_definition_categories
(
    item_definition_id UUID NOT NULL, -- Reference to the item definition
    item_version       INT  NOT NULL, -- Version of the item definition
    category_id        UUID NOT NULL, -- Reference to the category
    PRIMARY KEY (item_definition_id, item_version, category_id),
    FOREIGN KEY (item_definition_id, item_version) REFERENCES admin_palmyrapro.log_item_definition (id, version),
    FOREIGN KEY (category_id) REFERENCES admin_palmyrapro.categories (id)
);

-- Latest version view of every item definition.
CREATE VIEW admin_palmyrapro.item_definitions AS
SELECT *
FROM admin_palmyrapro.log_item_definition
WHERE is_latest = TRUE;

