INSERT INTO public.region (id, name, level, parent_id) VALUES ('ZMB', 'Zambia', 1, '');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('EP', 'Eastern', 2, 'ZMB');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NWP', 'Northwestern', 2, 'ZMB');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SALANDA', 'Sal<PERSON>', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 5, 'LUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SIMBEYA', '<PERSON><PERSON><PERSON>', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('BONSO', 'Bonso', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('<PERSON><PERSON><PERSON><PERSON>', '<PERSON>vwala', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('<PERSON>OPOL<PERSON>', '<PERSON>polo', 5, 'CHI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MAYEMBE', 'Mayembe', 5, 'CHN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('YINKALA', 'Yinkala', 5, 'MKA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('EDIWARD', 'Ediward', 5, 'MUC');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('WADZINGWA', 'Wadzingwa', 5, 'CHI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIZOKA', 'Chizoka', 5, 'CHG');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MILIKA', 'Milika', 5, 'CHI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIMYANGO', 'Chimyango', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MAT', 'Matula', 4, 'MKHANYA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KWY', 'Kawalya', 4, 'MKHANYA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIULONGO', 'Chiulongo', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUNDAWANGA_FARM', 'Mundawanga Farm', 5, 'KAS');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('POMPHE', 'Pomphe', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUSKWARA', 'Muskwara', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIKHULI', 'Chikhuli', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('TINKALO', 'Tinkalo', 5, 'MKA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MULIMA', 'Mulima', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUSAKAMAMBWE', 'Musakamambwe', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHILEKANI', 'Chilekani', 5, 'SIM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MPENYANI', 'Mpenyani', 5, 'MOM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAPILI', 'Kapili', 5, 'KAS');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MACHEKA', 'Macheka', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MWALUKA', 'Mwaluka', 5, 'SIM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KANYENZI', 'Kanyenzi', 5, 'MKA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NDEKELA', 'Ndekela', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MASIYEFARMS', 'Masiyefarms', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NYALULANI', 'Nyalulani', 5, 'MUC');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KALUMBA', 'Kalumba', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KASEZYA', 'Kasezya', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MULILIMA', 'Mulilima', 5, 'CHL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIMYANGA', 'Chimyanga', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KALIMANGOMBE', 'Kalimangombe', 5, 'CLA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KALIKULI', 'Kalikuli', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHISINGA', 'Chisinga', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NYM', 'Nyamphande', 4, 'MPANSHYA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIMUNA', 'Chimuna', 5, 'MLB');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MULENIYA', 'Muleniya', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MPHAMBA', 'Mphamba', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MKOMELA', 'Mkomela', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAPITAO', 'Kapitao', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('ROBERT_A', 'Robert A', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('WILSON', 'Wilson', 5, 'CHN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHINKULI', 'Chinkuli', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('BEMBELE', 'Bembele', 5, 'SIM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LIMBIKANI', 'Limbikani', 5, 'CLA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('THIMULANI', 'Thimulani', 5, 'KTP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KACHANGA_KAUNGO', 'Kachanga Kaungo', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHABALA', 'Chabala', 5, 'LUA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('TUMBWE', 'Tumbwe', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('PELEKA', 'Peleka', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('BALONI', 'Baloni', 5, 'MUC');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHITILIMA', 'Chitilima', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NGULETA', 'Nguleta', 5, 'NGU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MKHANYA', 'Mkhanya', 5, 'KWY');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SHAUTA', 'Shauta', 5, 'MWE');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAKONTHAMA', 'Kakonthama', 5, 'LUI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NSAMBILO', 'Nsambilo', 5, 'NAM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('YINKALO', 'Yinkalo', 5, 'MKA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('JUMA', 'Juma', 5, 'CHI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIMUYANGA', 'Chimuyanga', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KEFAS', 'Kefas', 5, 'LUA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MOLANDE', 'Molande', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SANDWE', 'Sandwe', 3, 'EP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAZANI', 'Kazani', 5, 'LUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KABUMBA', 'Kabumba', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MASIYE', 'Masiye', 5, 'SIM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KANDAMUTHWALE', 'Kandamuthwale', 5, 'MUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SAN', 'San', 5, 'KAS');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MGANNDA', 'Mgannda', 5, 'LUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KANJUZU', 'Kanjuzu', 5, 'KTP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHILUME', 'Chilume', 5, 'KTP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAMUZYU', 'Kamuzyu', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LEVY', 'Levy', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAJUMBE', 'Kajumbe', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KWA_ISAAC', 'Kwa Isaac', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHUTIKA', 'Chutika', 5, 'MLB');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MTULWA', 'Mtulwa', 5, 'LUA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('ZIMBA_PLOT', 'Zimba Plot', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIKULENI', 'Chikuleni', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIMSAU', 'Chimsau', 5, 'KAU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAWECHE', 'Kaweche', 5, 'CHG');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHITUMBE', 'Chitumbe', 5, 'CHI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NYALWIMBA', 'Nyalwimba', 5, 'MOM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LUP', 'Lupande', 4, 'MSORO');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHAMATWA', 'Chamatwa', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KANYETA', 'Kanyeta', 5, 'MWE');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CRB', 'Crb', 5, 'CHP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUKOMELA', 'Mukomela', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LIYONDA', 'Liyonda', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHAWALA', 'Chawala', 5, 'LUA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MULILIKA', 'Mulilika', 5, 'CHL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHODYALA', 'Chodyala', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SIMULEMA', 'Simulema', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIKOMANGARA', 'Chikomangara', 5, 'KTP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KATAMPA', 'Katampa', 5, 'LUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIPIKA', 'Chipika', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MKWESA', 'Mkwesa', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MALIKIYA', 'Malikiya', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NGU', 'Nguleta ', 4, 'NSEFU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAVIEN', 'Kavien', 5, 'CHN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('TUMBWE', 'Tumbwe', 5, 'CHW');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAS', 'Kasamanda', 4, 'MSORO');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NDEKE', 'Ndeke', 5, 'CHN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAMULAZA', 'Kamulaza', 5, 'LUI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('GAGA', 'Gaga', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUSAPENDA', 'Musapenda', 5, 'SIM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHISANZA', 'Chisanza', 5, 'KTP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KUM', 'Kumbukile', 4, 'MKHANYA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MCHENGA', 'Mchenga', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('OTHER', 'Other', 5, 'CHL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIWAYU', 'Chiwayu', 5, 'KAS');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NSANGU', 'Nsangu', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('DCHENGAPLOT', 'Dchengaplot', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NANOKOLA', 'Nanokola', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAKWIYA', 'Kakwiya', 5, 'MAT');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUZABWE', 'Muzabwe', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIWALE', 'Chiwale', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MWALANGA', 'Mwalanga', 5, 'MOM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SAFALAO', 'Safalao', 5, 'MUC');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIFUKA', 'Chifuka', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MULAMBA', 'Mulamba', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('DALIJALA', 'Dalijala', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHINKHOLA', 'Chinkhola', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('ZALAPANGU', 'Zalapangu', 5, 'CLA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAMANGU', 'Kamangu', 5, 'MAT');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUNYETA', 'Munyeta', 5, 'SHI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAPENDA_B', 'Kapenda B', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('BAULEN', 'Baulen', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KALUMBA', 'Kalumba', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MAMBWE', 'Mambwe', 5, 'CHW');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('FAINDAN', 'Faindan', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHAZWENGA', 'Chazwenga', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('JUMBE', 'Jumbe', 3, 'EP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MWACHOMBELA', 'Mwachombela', 5, 'MWE');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIPEPO', 'Chipepo', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MWEPVU', 'Mwepvu', 5, 'CHG');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('JELEMIAH', 'Jelemiah', 5, 'KAS');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('BALUWA', 'Baluwa', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KANGO', 'Kango', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHEMBE', 'Chembe', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MATANDA', 'Matanda', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUNYANGA', 'Munyanga', 5, 'MUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAJUMBA', 'Kajumba', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KASALA', 'Kasala', 5, 'CHG');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAMOTO', 'Kamoto', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('YALULENI', 'Yaluleni', 5, 'MUC');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('ZUNGWALA', 'Zungwala', 5, 'KTP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KALOZYA', 'Kalozya', 5, 'LUI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MPHIKISHA', 'Mphikisha', 5, 'CHI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NYANJHASI', 'Nyanjhasi', 5, 'LUA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KABEME', 'Kabeme', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHINZETE', 'Chinzete', 5, 'NYM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAFULIRA', 'Kafulira', 5, 'KTP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KANKOMA', 'Kankoma', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NJELEWE', 'Njelewe', 5, 'LUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('VUTIKAUPEZE', 'Vutikaupeze', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIZABALA', 'Chizabala', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('OTHER', 'Other', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('BENNY_SHIKAKOTA', 'Benny Shikakota', 5, 'CHL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KADWELELE', 'Kadwelele', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('JANEIRO', 'Janeiro', 5, 'KAU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('YAMBA-YAMBA', 'Yamba-Yamba', 5, 'CHI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('YOBE', 'Yobe', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAWACHO', 'Kawacho', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHAMBAKALE', 'Chambakale', 5, 'LUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NCHEKA', 'Ncheka', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUSA', 'Musa', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAMPHASA', 'Kamphasa', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIPAZU', 'Chipazu', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHS', 'Chasera', 4, 'MWANYA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MASANINGA', 'Masaninga', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('ZALAPANGO', 'Zalapango', 5, 'CLA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MULIZA', 'Muliza', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MPUKA', 'Mpuka', 3, 'EP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAMPALA', 'Kampala', 5, 'KAS');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KACHULUNGANGA', 'Kachulunganga', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LUBEBA', 'Lubeba', 5, 'KAT');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('YAMBA_YAMBA', 'Yamba Yamba', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MAKANDU', 'Makandu', 5, 'LUB');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAMANGA', 'Kamanga', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LISONGWE', 'Lisongwe', 5, 'SHI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SHIMUNGUWO', 'Shimunguwo', 5, 'SHI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('TIYANJANE', 'Tiyanjane', 5, 'CLA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('ZIMBABWE', 'Zimbabwe', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAWALIKA', 'Kawalika', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KWARICHARD', 'Kwarichard', 5, 'LUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NYANKHASI', 'Nyankhasi', 5, 'LUA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHAMBAKALE', 'Chambakale', 5, 'LUI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('PULOWA', 'Pulowa', 5, 'MOM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MCHENGE', 'Mchenge', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHAKULOTA', 'Chakulota', 5, 'MUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MWANKOMESHA', 'Mwankomesha', 5, 'NDU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHM', 'Chimunsanya', 4, 'MPANSHYA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MANGILILA', 'Mangilila', 5, 'LUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SIVILI', 'Sivili', 5, 'MOM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('TAFIKA', 'Tafika', 5, 'LUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('FOUNDAN', 'Foundan', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MAWANZYA', 'Mawanzya', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KUNGWA_SECTION', 'Kungwa Section', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KTP', 'Katopeka', 4, 'CHITUNGULU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LUMPHANGULA', 'Lumphangula', 5, 'CHG');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('TIYESEKO', 'Tiyeseko', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SICHILAMBO', 'Sichilambo', 5, 'MOM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SIMATANGA', 'Simatanga', 5, 'MOM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SAUKANI', 'Saukani', 5, 'MUC');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MAKOPA', 'Makopa', 5, 'LUA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('UKWIMI_FARMS', 'Ukwimi Farms', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KANCHULE', 'Kanchule', 5, 'KTP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KHANKHOMBE', 'Khankhombe', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAMUTU', 'Kamutu', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LULITALA', 'Lulitala', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NAMANONGO', 'Namanongo', 5, 'NAM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KASHINDI', 'Kashindi', 5, 'LUB');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('TULANI', 'Tulani', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUSEBA', 'Museba', 5, 'LUI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NYELA', 'Nyela', 5, 'CHI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUSAOPE', 'Musaope', 5, 'SIM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MMEMBEFARMS', 'Mmembefarms', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SHAKASUNI', 'Shakasuni', 5, 'MWA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NDOMU', 'Ndomu', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAULU', 'Kaulu', 5, 'CHG');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MASAKO', 'Masako', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUYEMBE', 'Muyembe', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MANOKOLA', 'Manokola', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MKHANYA', 'Mkhanya', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SHI', 'Shimunguwo', 4, 'SHIKABETA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUK', 'Mukwera', 4, 'MWANYA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAPAMPHA', 'Kapampha', 5, 'CHG');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MBILISAU', 'Mbilisau', 5, 'KAU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MPHONDAMALI', 'Mphondamali', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHINUNDA', 'Chinunda', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('WOLOKANI', 'Wolokani', 5, 'SIM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NAMANONGO', 'Namanongo', 5, 'NDU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('ABULAMU', 'Abulamu', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KACHEPA', 'Kachepa', 5, 'MWA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MALAMA', 'Malama', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NYONGO', 'Nyongo', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('OTHER', 'Other', 5, 'MWE');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MULILA', 'Mulila', 5, 'CHP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHINKHULI', 'Chinkhuli', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MULAMBABANDA', 'Mulambabanda', 5, 'CHG');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('TAIMON', 'Taimon', 5, 'MWA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUBALA', 'Mubala', 5, 'CLA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHISULO', 'Chisulo', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NCH', 'Ncheka', 4, 'MKHANYA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NYAMPINGA', 'Nyampinga', 5, 'MUC');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIMYANGU', 'Chimyangu', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAU', 'Kafuwela', 4, 'MPUKA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NYALINIMBA', 'Nyalinimba', 5, 'MOM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIOMBELA', 'Chiombela', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NYAKAUYA', 'Nyakauya', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MZALAPANGO', 'Mzalapango', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KATONDA_COT', 'Katonda Cot', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('ZIWANDE', 'Ziwande', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHAKHUNGA', 'Chakhunga', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('TISATILENI', 'Tisatileni', 5, 'MUC');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIKAPA', 'Chikapa', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NYANTHAPO', 'Nyanthapo', 5, 'MUC');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIBINGENI', 'Chibingeni', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHAZULA', 'Chazula', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('BUKUSHA', 'Bukusha', 5, 'MUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SAFALO', 'Safalo', 5, 'MUC');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KANGWA_SECTION', 'Kangwa Section', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LUEMBE', 'Luembe', 3, 'EP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NJELUMA', 'Njeluma', 5, 'MOM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('JEKE', 'Jeke', 5, 'CHN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('BONZO', 'Bonzo', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('ANDISEN', 'Andisen', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHILUKISHA', 'Chilukisha', 5, 'CLA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SHIKABETA', 'Shikabeta', 5, 'MWA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NAM', 'Namanongo', 4, 'BUNDABUNDA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MULALANJALA', 'Mulalanjala', 5, 'MLB');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHW', 'Chiwatala', 4, 'MKHANYA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('THOMAS', 'Thomas', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SAILI', 'Saili', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('FILLIMON', 'Fillimon', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MAULIDI', 'Maulidi', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MPANSHYA', 'Mpanshya', 3, 'EP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SAIDY', 'Saidy', 5, 'MUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUTENEKA', 'Muteneka', 5, 'NYM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHONGO', 'Chongo', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('YOLAMU', 'Yolamu', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHILONGOLOKA', 'Chilongoloka', 5, 'CHG');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KALUMBA', 'Kalumba', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('TIFWENGE', 'Tifwenge', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAPOLE', 'Kapole', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NSANGU', 'Nsangu', 5, 'CHI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('ISAAC', 'Isaac', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SHANTUKA', 'Shantuka', 5, 'NAM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHUNGULUKA', 'Chunguluka', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIPAZE', 'Chipaze', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('ZUBU', 'Zubu', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MWANAMONGA', 'Mwanamonga', 5, 'MWA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KASHEMBA', 'Kashemba', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LUMBUKA_.C._SCHOOL', 'Lumbuka .C. School', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHITULAMIKA', 'Chitulamika', 5, 'LUN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAPONGWE', 'Kapongwe', 5, 'CHN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('ANGULISA', 'Angulisa', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MANASE', 'Manase', 5, 'CHI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KWAELISHA', 'Kwaelisha', 5, 'KAS');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIKULA', 'Chikula', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LUCHELE', 'Luchele', 5, 'MWA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KABAZA', 'Kabaza', 5, 'MOM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHN', 'Chinambi', 4, 'LUEMBE');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('YAMPINGA', 'Yampinga', 5, 'MUC');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAVALAMANJA', 'Kavalamanja', 5, 'MKA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUPELEKA', 'Mupeleka', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('ALI', 'Ali', 5, 'MUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MWENGWE', 'Mwengwe', 5, 'LUN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('HAMOLO', 'Hamolo', 5, 'MKA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHITATA', 'Chitata', 5, 'LUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KUMBA', 'Kumba', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHITEMBO', 'Chitembo', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHUZUNZENI', 'Chuzunzeni', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHAKUMBA', 'Chakumba', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHINKULUNGA', 'Chinkulunga', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHASANZA', 'Chasanza', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('OTHER', 'Other', 5, 'CHG');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MWANYA', 'Mwanya', 3, 'EP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIMULA', 'Chimula', 5, 'SIM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NIPEZA', 'Nipeza', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SIMPETA', 'Simpeta', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHU', 'Chisulo', 4, 'JUMBE');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KWELA', 'Kwela', 5, 'CHG');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SHACHOLI', 'Shacholi', 5, 'MWA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KATAPAONGA', 'Katapaonga', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MTINTHIMIKA', 'Mtinthimika', 5, 'LUA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SELEWU', 'Selewu', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHILAMBA', 'Chilamba', 5, 'MUC');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('PALACE', 'Palace', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('ALICK_FARMS', 'Alick Farms', 5, 'KAS');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUNGWWNGWA', 'Mungwwngwa', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MPOTA', 'Mpota', 5, 'MLB');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MKHANYA', 'Mkhanya', 3, 'EP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NYOMBWEKA', 'Nyombweka', 5, 'CHN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('PAULO', 'Paulo', 5, 'CHN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MKOTWE', 'Mkotwe', 5, 'CHG');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIPISANI', 'Chipisani', 5, 'MUC');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHINKOLWE', 'Chinkolwe', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KANTYANTYA', 'Kantyantya', 5, 'NDU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHOFYA', 'Chofya', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NGOMBE', 'Ngombe', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SQUARE', 'Square', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHATULUKA', 'Chatuluka', 5, 'LUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIWATALA', 'Chiwatala', 5, 'CHW');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIMUNA', 'Chimuna', 5, 'MOM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SIYAWAKONZA', 'Siyawakonza', 5, 'CLA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KADEWELE', 'Kadewele', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SIWONDE', 'Siwonde', 5, 'LUA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHATA', 'Chata', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LUM', 'Lumbuka', 4, 'SANDWE');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHASERA', 'Chasera', 5, 'CHS');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MAYEMBE', 'Mayembe', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAMWENDO', 'Kamwendo', 5, 'KAS');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LEMBANI', 'Lembani', 5, 'SHI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHISHONKHOMOKA', 'Chishonkhomoka', 5, 'MLB');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHENSE', 'Chense', 5, 'LUI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('JEMUSALE', 'Jemusale', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHASANZA', 'Chasanza', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHINJALA', 'Chinjala', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CIGAGA', 'Cigaga', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LISONGWU', 'Lisongwu', 5, 'SHI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KACHENJELA', 'Kachenjela', 5, 'LUN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KALONDA', 'Kalonda', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SIMATUNGA', 'Simatunga', 5, 'MOM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MWANDAMA', 'Mwandama', 5, 'CHG');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUSADABWE', 'Musadabwe', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NJUIKISHA', 'Njuikisha', 5, 'MUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAMUZYA', 'Kamuzya', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUTONKA', 'Mutonka', 5, 'NDU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NACHIWA', 'Nachiwa', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('PALIJARA', 'Palijara', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAMPASA', 'Kampasa', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LAWRENCE_FARM', 'Lawrence Farm', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHENJE', 'Chenje', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUKOMBOLA', 'Mukombola', 5, 'LUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LOLANI', 'Lolani', 5, 'SIM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUZAMBWE', 'Muzambwe', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIKONDANO', 'Chikondano', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('TIYANJANI', 'Tiyanjani', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUZYULANTHENDE', 'Muzyulanthende', 5, 'LUB');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUTENDELE', 'Mutendele', 5, 'CLA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIKOLOMA', 'Chikoloma', 5, 'CHL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SAULO', 'Saulo', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MOTTANDE', 'Mottande', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('PAIZON', 'Paizon', 5, 'LUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KALIMBA', 'Kalimba', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHILEPA', 'Chilepa', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHITETE', 'Chitete', 5, 'CHG');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NTUNIHUMALE', 'Ntunihumale', 5, 'MOM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHAMBANYA', 'Chambanya', 5, 'CHG');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHINZEWE', 'Chinzewe', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('ANANIA_FARM', 'Anania Farm', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MCHENGA', 'Mchenga', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LUA', 'Luangazi', 4, 'LUEMBE');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KWACHANDA', 'Kwachanda', 5, 'MAT');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIMEKETE', 'Chimekete', 5, 'KTP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KWABEN', 'Kwaben', 5, 'CTA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAMILULU', 'Kamilulu', 5, 'NYM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KANUZYU', 'Kanuzyu', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MADALITSO_FARMS', 'Madalitso Farms', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('BULAWAYO', 'Bulawayo', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MPHANGOLA', 'Mphangola', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHANDE', 'Chande', 5, 'MAT');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHADYOLA', 'Chadyola', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MTALIKA', 'Mtalika', 5, 'CHN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MWE', 'Mweshagombe', 4, 'BUNDABUNDA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SAULOSI', 'Saulosi', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHINKHUNI', 'Chinkhuni', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KANYANTU', 'Kanyantu', 5, 'MWE');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUTULWA', 'Mutulwa', 5, 'LUA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHILUBEZI', 'Chilubezi', 5, 'LUI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SUSU', 'Susu', 5, 'LUB');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAFWEMBELA', 'Kafwembela', 5, 'MAT');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHAVULULA', 'Chavulula', 5, 'KAU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KABAYE', 'Kabaye', 5, 'LUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MIYOBE', 'Miyobe', 5, 'MWE');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KANYTANTYA', 'Kanytantya', 5, 'NDU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MWALILANDO', 'Mwalilando', 5, 'CHL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHINYONGOLO', 'Chinyongolo', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MWANZA', 'Mwanza', 5, 'KAS');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('EDWARD', 'Edward', 5, 'MUC');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KANYUNZU', 'Kanyunzu', 5, 'LUI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIHOLEKA', 'Chiholeka', 5, 'LUB');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NYAMPANDE', 'Nyampande', 5, 'NYM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MATAKA', 'Mataka', 5, 'CHG');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MULANWA', 'Mulanwa', 5, 'CHN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAMORO', 'Kamoro', 5, 'MKA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('PINTO', 'Pinto', 5, 'LUN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('OTHER', 'Other', 5, 'MAT');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NYAMALA', 'Nyamala', 5, 'KAS');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NYUMBWE', 'Nyumbwe', 5, 'MUC');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIBILILU', 'Chibililu', 5, 'SIM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('WEYA', 'Weya', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NEBERT', 'Nebert', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NYAMANGO', 'Nyamango', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHULU', 'Chulu', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('YELESANI', 'Yelesani', 5, 'SIM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KANYENTU', 'Kanyentu', 5, 'MWE');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHINKUNI', 'Chinkuni', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LUMBANI', 'Lumbani', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('BALUKA', 'Baluka', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHALOBANTU', 'Chalobantu', 5, 'LUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MSORO', 'Msoro', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KWA_NKHUPI', 'Kwa Nkhupi', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIHOKELA', 'Chihokela', 5, 'LUB');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NKHANDU', 'Nkhandu', 5, 'CHG');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('JANERO', 'Janero', 5, 'KAU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('OTHER', 'Other', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUTEPUKA', 'Mutepuka', 5, 'LUN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('BATANI', 'Batani', 5, 'LUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MULEZI', 'Mulezi', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NDUBULULA', 'Ndubulula', 5, 'NDU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('PALACE', 'Palace', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MKANDA', 'Mkanda', 5, 'KAS');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('BUKUCHA', 'Bukucha', 5, 'MUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('TANGU', 'Tangu', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIGAGA', 'Chigaga', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHA', 'Chalubilo', 4, 'LUEMBE');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHINZEBE', 'Chinzebe', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MPAMBA', 'Mpamba', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHISASA', 'Chisasa', 5, 'CHG');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NDAMVWA', 'Ndamvwa', 5, 'CHM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MATEYO', 'Mateyo', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIFUKA', 'Chifuka', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SALUYAVUKA', 'Saluyavuka', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIKALAMO', 'Chikalamo', 5, 'LUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LUBULA', 'Lubula', 5, 'LUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NSEFU', 'Nsefu', 3, 'EP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MIKWALA', 'Mikwala', 5, 'MOM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHANJUZICAMP', 'Chanjuzicamp', 5, 'KTP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('TIYESAKO', 'Tiyesako', 5, 'CHN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('ALIMEYI_FARMS', 'Alimeyi Farms', 5, 'KAS');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KASAFYA', 'Kasafya', 5, 'KAS');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NYAWA', 'Nyawa', 5, 'KAS');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SIMALANGA', 'Simalanga', 5, 'MOM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NDAMBWA', 'Ndambwa', 5, 'CHM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('OTHER', 'Other', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('RICHARD', 'Richard', 5, 'CHI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('BUNDABUNDA', 'Bundabunda', 3, 'EP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KWA_LEVY', 'Kwa Levy', 5, 'KAS');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LUEMBE', 'Luembe', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MULEBA', 'Muleba', 5, 'LUI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAMBWI', 'Kambwi', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('VALLEYTRUSTFARM', 'Valleytrustfarm', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MAYENGWE', 'Mayengwe', 5, 'MAT');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIWA', 'Chiwa', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NAVUTIKA', 'Navutika', 5, 'LUA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUNGOMBA', 'Mungomba', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KATOPEKA', 'Katopeka', 5, 'KTP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIWALA', 'Chiwala', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('TASILA', 'Tasila', 5, 'CHI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHISULO', 'Chisulo', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KASAKAZYA', 'Kasakazya', 5, 'MLB');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('TEDDY', 'Teddy', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MALUDZI', 'Maludzi', 5, 'KAS');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('JOSEPH_KAPOLE', 'Joseph Kapole', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MWANYABANTHU', 'Mwanyabanthu', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHANA', 'Chana', 5, 'CHI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('WILISI', 'Wilisi', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAPANTHA', 'Kapantha', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHASANZA', 'Chasanza', 5, 'MAT');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIBAMBO', 'Chibambo', 5, 'CHG');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MSAPENDA', 'Msapenda', 5, 'SIM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUZABWELA', 'Muzabwela', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MIKAYELE', 'Mikayele', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIZULE', 'Chizule', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MULEKANO', 'Mulekano', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MULEFU', 'Mulefu', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KALANSHA', 'Kalansha', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MATEMBA', 'Matemba', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KABUUBA', 'Kabuuba', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIMATENBO', 'Chimatenbo', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIKALAWA', 'Chikalawa', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('BULAYA', 'Bulaya', 5, 'LUN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('ELIYA', 'Eliya', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAPHILU', 'Kaphilu', 5, 'KTP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('JUMA', 'Juma', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NYUMBA', 'Nyumba', 5, 'MUC');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUKANDAPALANGA', 'Mukandapalanga', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('RUZAVO', 'Ruzavo', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MPONDE', 'Mponde', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KALAMBWA', 'Kalambwa', 5, 'KAS');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MWANYANGA', 'Mwanyanga', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MOSES', 'Moses', 5, 'MWA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LUBONOPLOT', 'Lubonoplot', 5, 'KWY');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LOTTIE', 'Lottie', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MZEJA', 'Mzeja', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('AMORO', 'Amoro', 5, 'MKA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUPIPA', 'Mupipa', 5, 'LUI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHAMUTWA', 'Chamutwa', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAZEMBE', 'Kazembe', 5, 'SHI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHILUME', 'Chilume', 5, 'LUI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SATIMWAMBO', 'Satimwambo', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SAMU', 'Samu', 5, 'MWA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHONDE', 'Chonde', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHITUMBI', 'Chitumbi', 5, 'CHI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIPUNGU', 'Chipungu', 5, 'NDU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('JAMESPLOT', 'Jamesplot', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MBURUMA', 'Mburuma', 3, 'EP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHABANYA', 'Chabanya', 5, 'CHG');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHILUNA', 'Chiluna', 5, 'MOM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHI', 'Hisanga', 4, 'LUEMBE');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MWACHENDE', 'Mwachende', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LAZARO', 'Lazaro', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIPANDA', 'Chipanda', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHOKANI', 'Chokani', 5, 'LUI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('PETERSPLOT', 'Petersplot', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHL', 'Chilimba', 4, 'BUNDABUNDA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MOM', 'Mombe', 4, 'NYALUGWE');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('TIPILILE', 'Tipilile', 5, 'SIM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIKHUNGU', 'Chikhungu', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHASUNZA', 'Chasunza', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MWA', 'Mwateshi', 4, 'SHIKABETA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHILWENYA', 'Chilwenya', 5, 'CTA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KWANDAMAWE', 'Kwandamawe', 5, 'KTP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIKUNGU', 'Chikungu', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MADALITSO', 'Madalitso', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MWALILANDA', 'Mwalilanda', 5, 'CHL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NYAMINSI', 'Nyaminsi', 5, 'MLB');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KANKAMBA', 'Kankamba', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SAULOSIVI', 'Saulosivi', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIPAZU', 'Chipazu', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LUI', 'Lumimba', 4, 'CHITUNGULU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KABINDALA', 'Kabindala', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NYAMANGA', 'Nyamanga', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIMUCEMJA', 'Chimucemja', 5, 'CHN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NYANLHASI', 'Nyanlhasi', 5, 'LUA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('APATSAMOSIYANA', 'Apatsamosiyana', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MWENECHISONGU', 'Mwenechisongu', 5, 'MOM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHALIMA_FARMS', 'Chalima Farms', 5, 'KAS');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHAKWENGA', 'Chakwenga', 5, 'NYM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KASAMIKA', 'Kasamika', 5, 'MUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAZYULA', 'Kazyula', 5, 'LUI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('TAIMONI', 'Taimoni', 5, 'MWA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('DICKSON', 'Dickson', 5, 'CHN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SHIKABETA', 'Shikabeta', 5, 'LUB');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUNYENYA', 'Munyenya', 5, 'LUN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('OTHER', 'Other', 5, 'NAM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SIMPEYA', 'Simpeya', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LUMWENGA', 'Lumwenga', 5, 'NDU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SHIKABETA', 'Shikabeta', 3, 'EP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MBURUMA', 'Mburuma', 5, 'MLB');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHANGACHANGA', 'Changachanga', 5, 'MUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NDEMA', 'Ndema', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHITUTU', 'Chitutu', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SIM', 'Simalama', 4, 'LUEMBE');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('BATU', 'Batu', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('ISAAC', 'Isaac', 5, 'CHN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHITIKA', 'Chitika', 5, 'MLB');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAMUKAZI', 'Kamukazi', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIMBUZI', 'Chimbuzi', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHAWAMA', 'Chawama', 5, 'CLA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MULULU', 'Mululu', 5, 'LUN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('ZUWALINYENGA', 'Zuwalinyenga', 5, 'MUC');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NTHIMBA', 'Nthimba', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHAKALAMBA', 'Chakalamba', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('YOHANE', 'Yohane', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MATIYA', 'Matiya', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LUPINDA', 'Lupinda', 5, 'CHN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NYALUGWE', 'Nyalugwe', 3, 'EP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('BAULENI', 'Bauleni', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KANYONDU', 'Kanyondu', 5, 'MWE');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NKANGWA_SECTION', 'Nkangwa Section', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('PHILEMON', 'Philemon', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MANGILILA', 'Mangilila', 5, 'KTP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MPANDILIA', 'Mpandilia', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NDU', 'Ndubulula', 4, 'BUNDABUNDA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KANTHUNKAKO', 'Kanthunkako', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('PALACE', 'Palace', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LUMANEMWANI_FARM', 'Lumanemwani Farm', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHG', 'Chitungulu', 4, 'CHITUNGULU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('UNDI', 'Undi', 5, 'MLB');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHP', 'Chipako', 4, 'MKHANYA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MPHONDE', 'Mphonde', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('BUDULA', 'Budula', 5, 'KAS');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUTABIKO', 'Mutabiko', 5, 'CHL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHAUPEKA', 'Chaupeka', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SIMUTANGA', 'Simutanga', 5, 'MOM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LINA', 'Lina', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('TAFIMBWALABILO', 'Tafimbwalabilo', 5, 'LUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NGONA_MKUZINGA', 'Ngona Mkuzinga', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('PITALA', 'Pitala', 5, 'KAS');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAPENDA', 'Kapenda', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KABUSWE', 'Kabuswe', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHINKUSE', 'Chinkuse', 5, 'MWE');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('BOWSPLOT', 'Bowsplot', 5, 'MAT');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MELENIYA', 'Meleniya', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MULULI', 'Mululi', 5, 'LUN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SUUTA', 'Suuta', 5, 'MWE');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KWZ', 'Kawaza', 4, 'NSEFU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MANYENDESHA', 'Manyendesha', 5, 'MUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAWAZA', 'Kawaza', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MALISENI', 'Maliseni', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MALANDA', 'Malanda', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHITEMBE', 'Chitembe', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MALANDA', 'Malanda', 5, 'LUN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KWARISCHARD', 'Kwarischard', 5, 'LUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIFUCHANAMA', 'Chifuchanama', 5, 'LUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('ACKSON', 'Ackson', 5, 'CHI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('GUDU', 'Gudu', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MEKE', 'Meke', 5, 'CHI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAFUNSA', 'Kafunsa', 5, 'NYM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MASIYE', 'Masiye', 5, 'CHI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHINGANGE', 'Chingange', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHILONGA', 'Chilonga', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('ISAAC', 'Isaac', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUCHUKWACHA', 'Muchukwacha', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHAUMA', 'Chauma', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAMPHOSA', 'Kamphosa', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('JANUARY', 'January', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KABULIKA', 'Kabulika', 5, 'CHP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MASANGA', 'Masanga', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MILUNGWE', 'Milungwe', 5, 'CHG');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHINGANZE', 'Chinganze', 5, 'MAT');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUTULUKA', 'Mutuluka', 5, 'KAT');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('PELEMBE', 'Pelembe', 5, 'CHG');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('ZANDONDA', 'Zandonda', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIMUGWELELE', 'Chimugwelele', 5, 'LUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIKONTI', 'Chikonti', 5, 'CHW');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIPAZU', 'Chipazu', 5, 'CHP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KALIKWE', 'Kalikwe', 5, 'CHG');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MPHONELEPO', 'Mphonelepo', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KACHENGELA', 'Kachengela', 5, 'LUN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MULILA', 'Mulila', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KALIMUKWENDA', 'Kalimukwenda', 5, 'MUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHAPONTA', 'Chaponta', 5, 'MAT');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KWL', 'Kawala', 4, 'MSORO');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIKANGU', 'Chikangu', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SUBSTATION', 'Substation', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAMPHUKUTA', 'Kamphukuta', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIMASULA', 'Chimasula', 5, 'KAS');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NKUNKHA', 'Nkunkha', 5, 'MWA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('TIYOPANJALA', 'Tiyopanjala', 5, 'CLA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MASANZA', 'Masanza', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('OLDPALACE', 'Oldpalace', 5, 'MUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('JEKAPU', 'Jekapu', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NENGO', 'Nengo', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('ZIMVEKEFARM', 'Zimvekefarm', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LUBILO', 'Lubilo', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LUSAUYIRO', 'Lusauyiro', 5, 'MOM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CYAONA', 'Cyaona', 5, 'KTP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUNGWANGWA', 'Mungwangwa', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MWANDO', 'Mwando', 5, 'KAT');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHENJA', 'Chenja', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('THANGA', 'Thanga', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHINZAZEN', 'Chinzazen', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KABANDI', 'Kabandi', 5, 'MWE');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MWESHANOMBE', 'Mweshanombe', 5, 'MWE');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('ANDERSON', 'Anderson', 5, 'CHN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUC', 'Muchimadzi', 4, 'NYALUGWE');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('PENDWE_COTTAGE', 'Pendwe Cottage', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUZUMBWE', 'Muzumbwe', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHUUNDU', 'Chuundu', 5, 'NAM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('ALSTONE', 'Alstone', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KABAMBA', 'Kabamba', 5, 'SIM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('ZOMBE', 'Zombe', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('DIVELIUS', 'Divelius', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CLA', 'Chaulima', 4, 'MBURUMA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAPITU', 'Kapitu', 5, 'SIM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MPH', 'Mphata', 4, 'JUMBE');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MALILAKUFWA', 'Malilakufwa', 5, 'MWE');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUGUNDA', 'Mugunda', 5, 'KTP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHITAUKA', 'Chitauka', 5, 'MAT');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KABALIKA', 'Kabalika', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KACHEPA', 'Kachepa', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MALIMA', 'Malima', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LUKANTHIKA', 'Lukanthika', 5, 'CHG');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KASAMANDA', 'Kasamanda', 5, 'KAS');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SIWEMBO', 'Siwembo', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('TONGA', 'Tonga', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NTUTHUMALE', 'Ntuthumale', 5, 'MOM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('ZAKALIYA', 'Zakaliya', 5, 'CHW');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LUPIYA', 'Lupiya', 5, 'LUN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHINGANYAMA', 'Chinganyama', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('YALULANI', 'Yalulani', 5, 'MUC');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NYAMAKAO', 'Nyamakao', 5, 'CHL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHITUNGULU', 'Chitungulu', 3, 'EP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('ZULUPLOT', 'Zuluplot', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MWANDOMBOLA', 'Mwandombola', 5, 'MWE');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHITILILA', 'Chitilila', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHAPUSA', 'Chapusa', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MULAMBA', 'Mulamba', 5, 'KTP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('BATA', 'Bata', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MKA', 'Mkaliva', 4, 'MPUKA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LUB', 'Lubalashi', 4, 'SHIKABETA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('TIYESEKO_FARMS', 'Tiyeseko Farms', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MWACHANDA', 'Mwachanda', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIMUNYANGA', 'Chimunyanga', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHINGANDA', 'Chinganda', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NDUNDA', 'Ndunda', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NKANYENTU', 'Nkanyentu', 5, 'MWE');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('YUDA', 'Yuda', 5, 'CHN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KALUKUSHA', 'Kalukusha', 5, 'CHI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHONI', 'Choni', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MALIMBILA', 'Malimbila', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MANASSEH', 'Manasseh', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIMUWA', 'Chimuwa', 5, 'MOM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('TALIMBANANAZO', 'Talimbananazo', 5, 'CHN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SICHITAMBO', 'Sichitambo', 5, 'MOM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIPAKO', 'Chipako', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUZYULANTENDE', 'Muzyulantende', 5, 'LUB');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIMANO', 'Chimano', 5, 'MUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MSORO', 'Msoro', 3, 'EP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SCHETAMBO', 'Schetambo', 5, 'MOM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHUNDAMA', 'Chundama', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUSANSHIKA', 'Musanshika', 5, 'NAM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MULABIKO', 'Mulabiko', 5, 'CHL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('THEDI', 'Thedi', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('PAUL', 'Paul', 5, 'CHN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIFUKA', 'Chifuka', 5, 'MAT');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MBUZI', 'Mbuzi', 5, 'LUB');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MZANJE', 'Mzanje', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MWESHANGOMBE', 'Mweshangombe', 5, 'MWE');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KATULULA', 'Katulula', 5, 'MWA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MULILWA', 'Mulilwa', 5, 'CHL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHILUKUSHI', 'Chilukushi', 5, 'CLA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MSHARILA', 'Msharila', 5, 'CHI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('OTHER', 'Other', 5, 'CTA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MIIMBA', 'Miimba', 5, 'MAT');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('YELESANI_B', 'Yelesani B', 5, 'SIM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MBIRISAU', 'Mbirisau', 5, 'KAU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHILEMBWA', 'Chilembwa', 5, 'KAT');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SIMKANDA', 'Simkanda', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('ROBERT_B', 'Robert B', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MWESHANGIMBE', 'Mweshangimbe', 5, 'MWE');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SACHOLI', 'Sacholi', 5, 'MWA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('YORAM', 'Yoram', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KASUMBA', 'Kasumba', 5, 'CHL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIDYOLA', 'Chidyola', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MASIYEFARM', 'Masiyefarm', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CTA', 'Chitila', 4, 'MKHANYA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('UNIKEDI', 'Unikedi', 5, 'MUC');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MEJA', 'Meja', 5, 'KAS');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('YOSEFE', 'Yosefe', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAPALILA', 'Kapalila', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NYAMUTUNDA', 'Nyamutunda', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('TEMBA', 'Temba', 5, 'LUI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MBELUKA', 'Mbeluka', 5, 'KTP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NTAINGA', 'Ntainga', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LUMWENGO', 'Lumwengo', 5, 'NDU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LUN', 'Lunsemfwa', 4, 'SHIKABETA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KANPWALE', 'Kanpwale', 5, 'CHG');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NYAMAKAWO', 'Nyamakawo', 5, 'CHL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MAKOBA', 'Makoba', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MPHANGULA', 'Mphangula', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIFOFO', 'Chifofo', 5, 'MUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('PHELEPE', 'Phelepe', 5, 'MUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('DAKA_PLOT', 'Daka Plot', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SALIMU', 'Salimu', 5, 'MUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('SICHILINDA', 'Sichilinda', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('YUBA', 'Yuba', 5, 'CHN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KONKHA', 'Konkha', 5, 'SIM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MWEWA', 'Mwewa', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KABACHA', 'Kabacha', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KALUMNADA', 'Kalumnada', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHINGAWO', 'Chingawo', 5, 'KAS');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MPOTU', 'Mpotu', 5, 'MLB');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MKWELA', 'Mkwela', 5, 'MUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MALIRA', 'Malira', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHAKUMBI', 'Chakumbi', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MLB', 'Malubila', 4, 'MBURUMA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MALATA', 'Malata', 5, 'CHN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KANDA', 'Kanda', 5, 'LUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KANYETU', 'Kanyetu', 5, 'MWE');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KASONTO', 'Kasonto', 5, 'LUI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KALIMAKWENDA', 'Kalimakwenda', 5, 'MUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIKONDI', 'Chikondi', 5, 'CLA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('JAMES', 'James', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NGOCHENI', 'Ngocheni', 5, 'KWY');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KANYETU', 'Kanyetu', 5, 'NAM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAT', 'Katemo', 4, 'MKHANYA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('POMPHE', 'Pomphe', 5, 'CHN');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MUTISIYEKO_FARM', 'Mutisiyeko Farm', 5, 'LUP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('BRAIMO', 'Braimo', 5, 'CHG');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LUK', 'Lukusuzi', 4, 'MWANYA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHINKHALI', 'Chinkhali', 5, 'CHA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LUGOMO', 'Lugomo', 5, 'CHU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('JACKPOT', 'Jackpot', 5, 'NCH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KANEMELA', 'Kanemela', 5, 'MLB');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('DCHENGA', 'Dchenga', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAKOBA', 'Kakoba', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MWENDA_FARM', 'Mwenda Farm', 5, 'MPH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHAMANYONGA', 'Chamanyonga', 5, 'SHI');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHUNDAMA', 'Chundama', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHIKOBENI', 'Chikobeni', 5, 'CHL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CHALUNDA', 'Chalunda', 5, 'NYM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAMUKUZI', 'Kamukuzi', 5, 'KWZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('ISAAAC', 'Isaaac', 5, 'KUM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('UNDI', 'Undi', 5, 'LUK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KABALIKA', 'Kabalika', 5, 'CHP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MPINGO', 'Mpingo', 5, 'MLB');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MSORO', 'Msoro', 5, 'KWL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NA', 'All-Chiefdom', 3, 'NWP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('JV', 'Jivundu', 4, 'NA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KZ', 'Kazozo', 4, 'NA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CK', 'Chikalakala', 4, 'NA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KBA', 'Kaluba', 4, 'NA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('WH', 'Wamafwaha', 4, 'NA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CP', 'Chipawa', 4, 'NA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('NT', 'Ntambu Central', 4, 'NA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('LM', 'Lamisamba', 4, 'NA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MP', 'Mumpulumba', 4, 'NA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('CS', 'Chisengisengi', 4, 'NA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('WT', 'Wamitoto', 4, 'NA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KH', 'Kahombo', 4, 'NA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KLE', 'Kalende', 4, 'NA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KAL', 'Kalende', 4, 'NA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KIM', 'Kimikango', 4, 'NA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MYA', 'Muyange', 4, 'NA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KUA', 'Kahundula', 4, 'NA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KYA', 'Kayonge', 4, 'NA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KEU', 'Keundu', 4, 'NA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KIA', 'Kaisumpa', 4, 'NA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('MKY', 'Makuya', 4, 'NA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KEA', 'Kema', 4, 'NA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('KYM', 'Kanyamisovu', 4, 'NA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('Other', 'Other', 5, 'JV');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('Other', 'Other', 5, 'KZ');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('Other', 'Other', 5, 'CK');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('Other', 'Other', 5, 'KBA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('Other', 'Other', 5, 'WH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('Other', 'Other', 5, 'CP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('Other', 'Other', 5, 'NT');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('Other', 'Other', 5, 'LM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('Other', 'Other', 5, 'MP');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('Other', 'Other', 5, 'CS');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('Other', 'Other', 5, 'WT');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('Other', 'Other', 5, 'KH');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('Other', 'Other', 5, 'KLE');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('Other', 'Other', 5, 'KAL');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('Other', 'Other', 5, 'KIM');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('Other', 'Other', 5, 'MYA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('Other', 'Other', 5, 'KUA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('Other', 'Other', 5, 'KYA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('Other', 'Other', 5, 'KEU');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('Other', 'Other', 5, 'KIA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('Other', 'Other', 5, 'MKY');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('Other', 'Other', 5, 'KEA');
INSERT INTO public.region (id, name, level, parent_id) VALUES ('Other', 'Other', 5, 'KYM');
