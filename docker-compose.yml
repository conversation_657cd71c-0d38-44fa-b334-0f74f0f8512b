services:
  #  honey-producers:
  #    container_name: honey-producers
  #    build:
  #      context: .
  #      dockerfile: ./apps/honey-producers/Dockerfile
  #      args:
  #        NEXT_PUBLIC_BACKEND_URL: ${NEXT_PUBLIC_BACKEND_URL}
  #        NEXT_PUBLIC_AUTH_URL: ${NEXT_PUBLIC_AUTH_URL}
  #    env_file:
  #      - ./.env.dockercompose
  #    restart: on-failure
  #    ports:
  #      - "8080:3000"
  #    networks:
  #      - app_network

  api:
    container_name: server-api
    build:
      context: .
      dockerfile: apps/api/Dockerfile
    env_file:
      - ./.env.dockercompose
    restart: on-failure
    ports:
      - "3001:3000"
    depends_on:
      db:
        condition: service_healthy
    volumes:
      - ./serviceAccount.json:/serviceAccount.json:ro
    environment:
      - FIREBASE_ADMIN_KEY_PATH=/serviceAccount.json
    networks:
      - app_network

  winter-protocol:
    container_name: winter-protocol
    build:
      context: .
      dockerfile: apps/api/Dockerfile.WinterProtocol
    env_file:
      - ./.env.dockercompose
    restart: on-failure
    ports:
      - "3002:8080"
    depends_on:
      db:
        condition: service_healthy
    networks:
      - app_network

  db:
    image: postgres:17.4
    restart: on-failure
    #    command: ["postgres", "-c", "log_statement=all"]
    environment:
      POSTGRES_PASSWORD: mysecretpassword
      POSTGRES_DB: palmira_pro_db
    healthcheck:
      test:
        ["CMD", "pg_isready", "-q", "-U", "postgres", "-d", "palmira_pro_db"]
      interval: 2s
      timeout: 5s
      retries: 10
    volumes:
      - pgdata:/var/lib/postgresql/data
      - ./packages/db/schema:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - app_network

  adminer:
    image: adminer
    restart: on-failure
    ports:
      - "9090:8080"
    networks:
      - app_network

# Define a network, which allows containers to communicate
# with each other, by using their container name as a hostname
networks:
  app_network:
#    external: true

volumes:
  pgdata:
