# Monorepo management.

## Steps to create the repo

Next the list of steps used to create the current monorepo. 

```shell

## Create the turbo project
npm install turbo --global # If not installed before.

## Outside the porject
npx create-turbo@latest palmyra-pro-next14-react18-tailwind3-shadcn \
    --skip-install \
    -m npm \
    --example kitchen-sink


cd palmyra-pro-next14-react18-tailwind3-shadcn/apps

## Create nextjs: https://nextjs.org/docs/app/api-reference/cli/create-next-app
## Default options and yes to alias.
npx create-next-app@14 honey-producers \
    --app \
    --src-dir "src/" \
    --import-alias "@/*" \
    --typescript \
    --eslint \
    --skip-install \
    --use-npm \
    --tailwind

cd honey-producers

## Integrate Shadcn
npx shadcn@latest init -y

## Add component as example into `src/components/ui/button.tsx`
## So we will need our own components in <ROOT>/src/components/ui/.....
npx shadcn@latest add button

## Add a button for testing into `src/app/page.tsx`:
## import { Button } from "@/components/ui/button"
## ....
## <Button>Click me</Button>


# I changed the default ports to fix it to 8080, 8081 and 8082
# - honey-producers at  http://localhost:8080/
# - web at  http://localhost:8081/
# - api at  http://localhost:8082/

cd ../..
npm install
npm run dev

```


## Refs:
- Add new packages: https://turbo.build/repo/docs/crafting-your-repository/creating-an-internal-package
- 