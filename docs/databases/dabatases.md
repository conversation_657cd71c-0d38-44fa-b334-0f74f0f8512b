# Database instances

## Access to db instances
To be able to connect to Google Cloud SQL instances using the public IP:
- Via Google Cloud SQL Proxy. Download it. DBeaver, for example, is downloading it transparently.
- Via Certificates.

## Cloud SQL Auth Proxy

This is the preferred method if you have a GCP user account.

When the Cloud SQL Auth Proxy is running, you can connect to a GCP SQL instance through `localhost`. Many SQL IDEs (like DBeaver)
and drivers already integrate it seamlessly, so in most cases, you won't even need to run it manually.

Check out the full docs here: https://cloud.google.com/sql/docs/postgres/sql-proxy

## Connect via Certificates

This method is used when there's no other option but to expose the instance to the internet. For example,
when you don't know the set of IPs to add to the firewall.

In this setup, you'll have a separate certificate for each instance.

### DBeaver + Cert + User/Password
> Reminder: Use your own user and password

![Config main settings](./dbeaver_settings_main.png)
![Config ssl settings](./dbeaver_settings_ssl.png)


### Intellij IDEA + Cert + User/Password
> Reminder: Use your own user and password

![Config main settings](./dbeaver_settings_main.png)
![Config ssl settings](./dbeaver_settings_ssl.png)



## Allow public access

### Instance config
1. Allow all ips adding `0.0.0.0/0` into the whitelist.
2. From `Connections -> Security` enable *Require trusted client certificates*

## Creating the certificates

> IMPORTANT: It's not necessary to create one per user. We can share it, so ask for the current one!

[To create it](https://cloud.google.com/sql/docs/postgres/configure-ssl-instance#client-certs):
```shell
# Create the certificate.
gcloud sql ssl client-certs create analytics nn-db-analytics-client-key.pem --instance=naturesnectar-db-analytics

# Output
# Created [https://sqladmin.googleapis.com/sql/v1beta4/projects/natures-nectar-dev/instances/naturesnectar-db-analytics/sslCerts/58fb8e346341d417671a16c7931533d934a1ba4b].
# NAME       SHA1_FINGERPRINT                          EXPIRATION
# analytics  58fb8e346341d417671a16c7931533d934a1ba4b  2035-04-08T14:26:40.546Z

# Get the public key to share and the server certificate
gcloud sql ssl client-certs describe analytics --instance=naturesnectar-db-analytics --format="value(cert)" > nn-db-analytics-client-cert.pem
gcloud sql instances describe naturesnectar-db-analytics --format="value(serverCaCert.cert)" > nn-db-analytics-server-ca.pem
```

In general, changing the environment variable as you wish:
```shell
export INSTANCE_DB=naturesnectar-db-analytics
export CERT_NAME=nn-db-analytics

# Create the certificate.
gcloud sql ssl client-certs create analytics $CERT_NAME-client-key.pem --instance=$INSTANCE_DB

# Output
# Created [https://sqladmin.googleapis.com/sql/v1beta4/projects/natures-nectar-dev/instances/naturesnectar-db-analytics/sslCerts/58fb8e346341d417671a16c7931533d934a1ba4b].
# NAME       SHA1_FINGERPRINT                          EXPIRATION
# analytics  58fb8e346341d417671a16c7931533d934a1ba4b  2035-04-08T14:26:40.546Z

# Get the public key to share and the server certificate
gcloud sql ssl client-certs describe $CERT_NAME --instance=$INSTANCE_DB --format="value(cert)" > $CERT_NAME-client-cert.pem
gcloud sql instances describe $INSTANCE_DB --format="value(serverCaCert.cert)" > $CERT_NAME-server-ca.pem
```



