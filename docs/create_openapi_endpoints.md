- [openapi-generator-cli](https://github.com/OpenAPITools/openapi-generator-cli) It's a wrapper around the official
  generator, so we need to have Java installed.
- [openapi-typescript-codegen](https://github.com/ferdikoomen/openapi-typescript-codegen is deprecated and the author
  recommend move to @hey-api/openapi-ts
- [OpenAPI TypeScript](https://github.com/hey-api/openapi-ts) All clients are in beta!!!
  > This package is in initial development. The interface might change before it becomes stable.
- [OpenAPI TS](https://openapi-ts.dev) Only generate client side. No server side stubs. Also need redocly for Auth and other features.
- [openapistack](https://openapistack.co/docs/api-first/)

```shell
## Create api-specs
cd <ROOT>
turbo gen workspace --name api-specs --type package
cd packages/api-specs
#npm install @hey-api/openapi-ts @redocly/cli
npm i -D openapi-typescript

# Add scripts into package.json to generate code.
# "generate:client": "openapi-ts generate --input openapi.yaml --output ../api-sdk/src",
# "generate:server": "openapi-ts generate --input openapi.yaml --output ../apps/api/src/generated --server express"

## Create api-sdk
cd <ROOT>
turbo gen workspace --name api-sdk --type package

```