# GLOBALS
ENV_SHORT_NAME: local

# DB
DB_NAME: "palmira_pro_db"
DB_HOST: "db"
DB_PORT: 5432
DB_USER: "postgres"
DB_PASSWORD: "mysecretpassword"
DB_SSL_MODE: "disable" # In prod: "require" https://www.postgresql.org/docs/current/libpq-ssl.html#LIBPQ-SSL-SSLMODE-STATEMENTS

# API
METABASE_SITE_URL: "https://zengate-global.metabaseapp.com"
METABASE_SECRET_KEY: "bdc8d3f820b8649e7fc2f8d662f515f4e4ae8fa2013d37c008e67a1d7a109802"
AUTH_JWT_KEYS_URL: "http://auth:3000/api/auth/jwks"
ALLOWED_ORIGIN: "http://localhost:8080"

# Auth
BETTER_AUTH_SECRET: 1weVHPNqZatYKRBpeXSeAjRzYHP0zMiy
<PERSON>TER_AUTH_URL: "http://localhost:3000"
FE_URL: "http://localhost:8080"

# Front end
NEXT_PUBLIC_BACKEND_URL: "http://localhost:3001/"
NEXT_PUBLIC_AUTH_URL: "http://localhost:3000/"

# Winter protocol
WINTER_BACKEND_URL: "https://staging-new-wallet-winter-cardano-api-463523546.asia-southeast1.run.app"
HONEY_HARVEST_SUMMARY_INTERVAL: "0 0 * * *"
CARDANO_TENANT_IDENTIFIER: "NATNECT"

# Sendgrid
SENDGRID_API_KEY: "*********************************************************************"

