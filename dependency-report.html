<!DOCTYPE html>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <style>
      * {
        font-family: Arial, Helvetica, sans-serif;
      }
      h1 {
        text-align: center;
      }
      .group-header th {
        font-size: 200%;
      }
      .sub-header th {
        font-size: 150%;
      }
      table, th, td {
        border: 1px solid black;
        border-collapse: collapse;
        white-space: nowrap;
        padding: .3em;
      }
      table {
        margin: 0 auto;
      }
      .severity {
        text-align: center;
        font-weight: bold;
        color: #fafafa;
      }
      .severity-LOW .severity { background-color: #5fbb31; }
      .severity-MEDIUM .severity { background-color: #e9c600; }
      .severity-HIGH .severity { background-color: #ff8800; }
      .severity-CRITICAL .severity { background-color: #e40000; }
      .severity-UNKNOWN .severity { background-color: #747474; }
      .severity-LOW { background-color: #5fbb3160; }
      .severity-MEDIUM { background-color: #e9c60060; }
      .severity-HIGH { background-color: #ff880060; }
      .severity-CRITICAL { background-color: #e4000060; }
      .severity-UNKNOWN { background-color: #74747460; }
      table tr td:first-of-type {
        font-weight: bold;
      }
      .links a,
      .links[data-more-links=on] a {
        display: block;
      }
      .links[data-more-links=off] a:nth-of-type(1n+5) {
        display: none;
      }
      a.toggle-more-links { cursor: pointer; }
    </style>
    <title>apps/api/go.mod - Trivy Report - 2025-07-10 10:23:51.810277027 +0530 IST m=************ </title>
    <script>
      window.onload = function() {
        document.querySelectorAll('td.links').forEach(function(linkCell) {
          var links = [].concat.apply([], linkCell.querySelectorAll('a'));
          [].sort.apply(links, function(a, b) {
            return a.href > b.href ? 1 : -1;
          });
          links.forEach(function(link, idx) {
            if (links.length > 3 && 3 === idx) {
              var toggleLink = document.createElement('a');
              toggleLink.innerText = "Toggle more links";
              toggleLink.href = "#toggleMore";
              toggleLink.setAttribute("class", "toggle-more-links");
              linkCell.appendChild(toggleLink);
            }
            linkCell.appendChild(link);
          });
        });
        document.querySelectorAll('a.toggle-more-links').forEach(function(toggleLink) {
          toggleLink.onclick = function() {
            var expanded = toggleLink.parentElement.getAttribute("data-more-links");
            toggleLink.parentElement.setAttribute("data-more-links", "on" === expanded ? "off" : "on");
            return false;
          };
        });
      };
    </script>
  </head>
  <body>
    <h1>apps/api/go.mod - Trivy Report - 2025-07-10 10:23:51.810324987 +0530 IST m=************</h1>
    <table>
      <tr class="group-header"><th colspan="6">gomod</th></tr>
      <tr class="sub-header">
        <th>Package</th>
        <th>Vulnerability ID</th>
        <th>Severity</th>
        <th>Installed Version</th>
        <th>Fixed Version</th>
        <th>Links</th>
      </tr>
      <tr class="severity-HIGH">
        <td class="pkg-name">github.com/getkin/kin-openapi</td>
        <td>CVE-2025-30153</td>
        <td class="severity">HIGH</td>
        <td class="pkg-version">v0.128.0</td>
        <td>0.131.0</td>
        <td class="links" data-more-links="off">
          <a href="https://access.redhat.com/security/cve/CVE-2025-30153">https://access.redhat.com/security/cve/CVE-2025-30153</a>
          <a href="https://github.com/getkin/kin-openapi">https://github.com/getkin/kin-openapi</a>
          <a href="https://github.com/getkin/kin-openapi/blob/6da871e0e170b7637eb568c265c08bc2b5d6e7a3/openapi3filter/req_resp_decoder.go#L1275">https://github.com/getkin/kin-openapi/blob/6da871e0e170b7637eb568c265c08bc2b5d6e7a3/openapi3filter/req_resp_decoder.go#L1275</a>
          <a href="https://github.com/getkin/kin-openapi/blob/6da871e0e170b7637eb568c265c08bc2b5d6e7a3/openapi3filter/req_resp_decoder.go#L1523">https://github.com/getkin/kin-openapi/blob/6da871e0e170b7637eb568c265c08bc2b5d6e7a3/openapi3filter/req_resp_decoder.go#L1523</a>
          <a href="https://github.com/getkin/kin-openapi/commit/67f0b233ffc01332f7d993f79490fbea5f4455f1">https://github.com/getkin/kin-openapi/commit/67f0b233ffc01332f7d993f79490fbea5f4455f1</a>
          <a href="https://github.com/getkin/kin-openapi/pull/1059">https://github.com/getkin/kin-openapi/pull/1059</a>
          <a href="https://github.com/getkin/kin-openapi/security/advisories/GHSA-wq9g-9vfc-cfq9">https://github.com/getkin/kin-openapi/security/advisories/GHSA-wq9g-9vfc-cfq9</a>
          <a href="https://github.com/getkin/kin-openapi?tab=readme-ov-file#custom-content-type-for-body-of-http-requestresponse">https://github.com/getkin/kin-openapi?tab=readme-ov-file#custom-content-type-for-body-of-http-requestresponse</a>
          <a href="https://nvd.nist.gov/vuln/detail/CVE-2025-30153">https://nvd.nist.gov/vuln/detail/CVE-2025-30153</a>
          <a href="https://pkg.go.dev/vuln/GO-2025-3533">https://pkg.go.dev/vuln/GO-2025-3533</a>
          <a href="https://www.cve.org/CVERecord?id=CVE-2025-30153">https://www.cve.org/CVERecord?id=CVE-2025-30153</a>
        </td>
      </tr>
      <tr class="severity-HIGH">
        <td class="pkg-name">github.com/golang-jwt/jwt/v5</td>
        <td>CVE-2025-30204</td>
        <td class="severity">HIGH</td>
        <td class="pkg-version">v5.2.1</td>
        <td>5.2.2</td>
        <td class="links" data-more-links="off">
          <a href="https://access.redhat.com/errata/RHSA-2025:7425">https://access.redhat.com/errata/RHSA-2025:7425</a>
          <a href="https://access.redhat.com/security/cve/CVE-2025-30204">https://access.redhat.com/security/cve/CVE-2025-30204</a>
          <a href="https://bugzilla.redhat.com/2354195">https://bugzilla.redhat.com/2354195</a>
          <a href="https://errata.almalinux.org/9/ALSA-2025-7425.html">https://errata.almalinux.org/9/ALSA-2025-7425.html</a>
          <a href="https://github.com/golang-jwt/jwt">https://github.com/golang-jwt/jwt</a>
          <a href="https://github.com/golang-jwt/jwt/commit/0951d184286dece21f73c85673fd308786ffe9c3">https://github.com/golang-jwt/jwt/commit/0951d184286dece21f73c85673fd308786ffe9c3</a>
          <a href="https://github.com/golang-jwt/jwt/commit/bf316c48137a1212f8d0af9288cc9ce8e59f1afb">https://github.com/golang-jwt/jwt/commit/bf316c48137a1212f8d0af9288cc9ce8e59f1afb</a>
          <a href="https://github.com/golang-jwt/jwt/security/advisories/GHSA-mh63-6h87-95cp">https://github.com/golang-jwt/jwt/security/advisories/GHSA-mh63-6h87-95cp</a>
          <a href="https://linux.oracle.com/cve/CVE-2025-30204.html">https://linux.oracle.com/cve/CVE-2025-30204.html</a>
          <a href="https://linux.oracle.com/errata/ELSA-2025-7967.html">https://linux.oracle.com/errata/ELSA-2025-7967.html</a>
          <a href="https://nvd.nist.gov/vuln/detail/CVE-2025-30204">https://nvd.nist.gov/vuln/detail/CVE-2025-30204</a>
          <a href="https://pkg.go.dev/vuln/GO-2025-3553">https://pkg.go.dev/vuln/GO-2025-3553</a>
          <a href="https://security.netapp.com/advisory/ntap-20250404-0002">https://security.netapp.com/advisory/ntap-20250404-0002</a>
          <a href="https://security.netapp.com/advisory/ntap-20250404-0002/">https://security.netapp.com/advisory/ntap-20250404-0002/</a>
          <a href="https://www.cve.org/CVERecord?id=CVE-2025-30204">https://www.cve.org/CVERecord?id=CVE-2025-30204</a>
        </td>
      </tr>
      <tr class="severity-HIGH">
        <td class="pkg-name">golang.org/x/crypto</td>
        <td>CVE-2025-22869</td>
        <td class="severity">HIGH</td>
        <td class="pkg-version">v0.32.0</td>
        <td>0.35.0</td>
        <td class="links" data-more-links="off">
          <a href="https://access.redhat.com/errata/RHSA-2025:3833">https://access.redhat.com/errata/RHSA-2025:3833</a>
          <a href="https://access.redhat.com/security/cve/CVE-2025-22869">https://access.redhat.com/security/cve/CVE-2025-22869</a>
          <a href="https://bugzilla.redhat.com/2348367">https://bugzilla.redhat.com/2348367</a>
          <a href="https://errata.almalinux.org/9/ALSA-2025-3833.html">https://errata.almalinux.org/9/ALSA-2025-3833.html</a>
          <a href="https://github.com/golang/crypto">https://github.com/golang/crypto</a>
          <a href="https://github.com/golang/crypto/commit/7292932d45d55c7199324ab0027cc86e8198aa22">https://github.com/golang/crypto/commit/7292932d45d55c7199324ab0027cc86e8198aa22</a>
          <a href="https://go-review.googlesource.com/c/crypto/+/652135">https://go-review.googlesource.com/c/crypto/+/652135</a>
          <a href="https://go.dev/cl/652135">https://go.dev/cl/652135</a>
          <a href="https://go.dev/issue/71931">https://go.dev/issue/71931</a>
          <a href="https://linux.oracle.com/cve/CVE-2025-22869.html">https://linux.oracle.com/cve/CVE-2025-22869.html</a>
          <a href="https://linux.oracle.com/errata/ELSA-2025-7484.html">https://linux.oracle.com/errata/ELSA-2025-7484.html</a>
          <a href="https://nvd.nist.gov/vuln/detail/CVE-2025-22869">https://nvd.nist.gov/vuln/detail/CVE-2025-22869</a>
          <a href="https://pkg.go.dev/vuln/GO-2025-3487">https://pkg.go.dev/vuln/GO-2025-3487</a>
          <a href="https://security.netapp.com/advisory/ntap-20250411-0010">https://security.netapp.com/advisory/ntap-20250411-0010</a>
          <a href="https://security.netapp.com/advisory/ntap-20250411-0010/">https://security.netapp.com/advisory/ntap-20250411-0010/</a>
          <a href="https://www.cve.org/CVERecord?id=CVE-2025-22869">https://www.cve.org/CVERecord?id=CVE-2025-22869</a>
        </td>
      </tr>
      <tr><th colspan="6">No Misconfigurations found</th></tr>
      <tr class="group-header"><th colspan="6">npm</th></tr>
      <tr class="sub-header">
        <th>Package</th>
        <th>Vulnerability ID</th>
        <th>Severity</th>
        <th>Installed Version</th>
        <th>Fixed Version</th>
        <th>Links</th>
      </tr>
      <tr class="severity-LOW">
        <td class="pkg-name">better-auth</td>
        <td>CVE-2025-53535</td>
        <td class="severity">LOW</td>
        <td class="pkg-version">1.2.4</td>
        <td>1.2.10</td>
        <td class="links" data-more-links="off">
          <a href="https://github.com/better-auth/better-auth">https://github.com/better-auth/better-auth</a>
          <a href="https://github.com/better-auth/better-auth/commit/9801d1be53d9da04686b94c6286c53ec97496740">https://github.com/better-auth/better-auth/commit/9801d1be53d9da04686b94c6286c53ec97496740</a>
          <a href="https://github.com/better-auth/better-auth/security/advisories/GHSA-36rg-gfq2-3h56">https://github.com/better-auth/better-auth/security/advisories/GHSA-36rg-gfq2-3h56</a>
          <a href="https://nvd.nist.gov/vuln/detail/CVE-2025-53535">https://nvd.nist.gov/vuln/detail/CVE-2025-53535</a>
        </td>
      </tr>
      <tr class="severity-LOW">
        <td class="pkg-name">brace-expansion</td>
        <td>CVE-2025-5889</td>
        <td class="severity">LOW</td>
        <td class="pkg-version">1.1.11</td>
        <td>2.0.2, 1.1.12, 3.0.1, 4.0.1</td>
        <td class="links" data-more-links="off">
          <a href="https://access.redhat.com/security/cve/CVE-2025-5889">https://access.redhat.com/security/cve/CVE-2025-5889</a>
          <a href="https://gist.github.com/mmmsssttt404/37a40ce7d6e5ca604858fe30814d9466">https://gist.github.com/mmmsssttt404/37a40ce7d6e5ca604858fe30814d9466</a>
          <a href="https://github.com/juliangruber/brace-expansion">https://github.com/juliangruber/brace-expansion</a>
          <a href="https://github.com/juliangruber/brace-expansion/commit/0b6a9781e18e9d2769bb2931f4856d1360243ed2">https://github.com/juliangruber/brace-expansion/commit/0b6a9781e18e9d2769bb2931f4856d1360243ed2</a>
          <a href="https://github.com/juliangruber/brace-expansion/commit/15f9b3c75ebf5988198241fecaebdc45eff28a9f">https://github.com/juliangruber/brace-expansion/commit/15f9b3c75ebf5988198241fecaebdc45eff28a9f</a>
          <a href="https://github.com/juliangruber/brace-expansion/commit/36603d5f3599a37af9e85eda30acd7d28599c36e">https://github.com/juliangruber/brace-expansion/commit/36603d5f3599a37af9e85eda30acd7d28599c36e</a>
          <a href="https://github.com/juliangruber/brace-expansion/commit/c3c73c8b088defc70851843be88ccc3af08e7217">https://github.com/juliangruber/brace-expansion/commit/c3c73c8b088defc70851843be88ccc3af08e7217</a>
          <a href="https://github.com/juliangruber/brace-expansion/pull/65/commits/a5b98a4f30d7813266b221435e1eaaf25a1b0ac5">https://github.com/juliangruber/brace-expansion/pull/65/commits/a5b98a4f30d7813266b221435e1eaaf25a1b0ac5</a>
          <a href="https://github.com/juliangruber/brace-expansion/releases/tag/v4.0.1">https://github.com/juliangruber/brace-expansion/releases/tag/v4.0.1</a>
          <a href="https://nvd.nist.gov/vuln/detail/CVE-2025-5889">https://nvd.nist.gov/vuln/detail/CVE-2025-5889</a>
          <a href="https://vuldb.com/?ctiid.311660">https://vuldb.com/?ctiid.311660</a>
          <a href="https://vuldb.com/?id.311660">https://vuldb.com/?id.311660</a>
          <a href="https://vuldb.com/?submit.585717">https://vuldb.com/?submit.585717</a>
          <a href="https://www.cve.org/CVERecord?id=CVE-2025-5889">https://www.cve.org/CVERecord?id=CVE-2025-5889</a>
        </td>
      </tr>
      <tr class="severity-LOW">
        <td class="pkg-name">brace-expansion</td>
        <td>CVE-2025-5889</td>
        <td class="severity">LOW</td>
        <td class="pkg-version">2.0.1</td>
        <td>2.0.2, 1.1.12, 3.0.1, 4.0.1</td>
        <td class="links" data-more-links="off">
          <a href="https://access.redhat.com/security/cve/CVE-2025-5889">https://access.redhat.com/security/cve/CVE-2025-5889</a>
          <a href="https://gist.github.com/mmmsssttt404/37a40ce7d6e5ca604858fe30814d9466">https://gist.github.com/mmmsssttt404/37a40ce7d6e5ca604858fe30814d9466</a>
          <a href="https://github.com/juliangruber/brace-expansion">https://github.com/juliangruber/brace-expansion</a>
          <a href="https://github.com/juliangruber/brace-expansion/commit/0b6a9781e18e9d2769bb2931f4856d1360243ed2">https://github.com/juliangruber/brace-expansion/commit/0b6a9781e18e9d2769bb2931f4856d1360243ed2</a>
          <a href="https://github.com/juliangruber/brace-expansion/commit/15f9b3c75ebf5988198241fecaebdc45eff28a9f">https://github.com/juliangruber/brace-expansion/commit/15f9b3c75ebf5988198241fecaebdc45eff28a9f</a>
          <a href="https://github.com/juliangruber/brace-expansion/commit/36603d5f3599a37af9e85eda30acd7d28599c36e">https://github.com/juliangruber/brace-expansion/commit/36603d5f3599a37af9e85eda30acd7d28599c36e</a>
          <a href="https://github.com/juliangruber/brace-expansion/commit/c3c73c8b088defc70851843be88ccc3af08e7217">https://github.com/juliangruber/brace-expansion/commit/c3c73c8b088defc70851843be88ccc3af08e7217</a>
          <a href="https://github.com/juliangruber/brace-expansion/pull/65/commits/a5b98a4f30d7813266b221435e1eaaf25a1b0ac5">https://github.com/juliangruber/brace-expansion/pull/65/commits/a5b98a4f30d7813266b221435e1eaaf25a1b0ac5</a>
          <a href="https://github.com/juliangruber/brace-expansion/releases/tag/v4.0.1">https://github.com/juliangruber/brace-expansion/releases/tag/v4.0.1</a>
          <a href="https://nvd.nist.gov/vuln/detail/CVE-2025-5889">https://nvd.nist.gov/vuln/detail/CVE-2025-5889</a>
          <a href="https://vuldb.com/?ctiid.311660">https://vuldb.com/?ctiid.311660</a>
          <a href="https://vuldb.com/?id.311660">https://vuldb.com/?id.311660</a>
          <a href="https://vuldb.com/?submit.585717">https://vuldb.com/?submit.585717</a>
          <a href="https://www.cve.org/CVERecord?id=CVE-2025-5889">https://www.cve.org/CVERecord?id=CVE-2025-5889</a>
        </td>
      </tr>
      <tr class="severity-LOW">
        <td class="pkg-name">next</td>
        <td>CVE-2025-30218</td>
        <td class="severity">LOW</td>
        <td class="pkg-version">14.2.25</td>
        <td>12.3.6, 13.5.10, 14.2.26, 15.2.4</td>
        <td class="links" data-more-links="off">
          <a href="https://access.redhat.com/security/cve/CVE-2025-30218">https://access.redhat.com/security/cve/CVE-2025-30218</a>
          <a href="https://github.com/vercel/next.js">https://github.com/vercel/next.js</a>
          <a href="https://github.com/vercel/next.js/security/advisories/GHSA-223j-4rm8-mrmf">https://github.com/vercel/next.js/security/advisories/GHSA-223j-4rm8-mrmf</a>
          <a href="https://nvd.nist.gov/vuln/detail/CVE-2025-30218">https://nvd.nist.gov/vuln/detail/CVE-2025-30218</a>
          <a href="https://vercel.com/changelog/cve-2025-30218-5DREmEH765PoeAsrNNQj3O">https://vercel.com/changelog/cve-2025-30218-5DREmEH765PoeAsrNNQj3O</a>
          <a href="https://www.cve.org/CVERecord?id=CVE-2025-30218">https://www.cve.org/CVERecord?id=CVE-2025-30218</a>
        </td>
      </tr>
      <tr class="severity-LOW">
        <td class="pkg-name">next</td>
        <td>CVE-2025-48068</td>
        <td class="severity">LOW</td>
        <td class="pkg-version">14.2.25</td>
        <td>15.2.2, 14.2.30</td>
        <td class="links" data-more-links="off">
          <a href="https://access.redhat.com/security/cve/CVE-2025-48068">https://access.redhat.com/security/cve/CVE-2025-48068</a>
          <a href="https://github.com/vercel/next.js">https://github.com/vercel/next.js</a>
          <a href="https://github.com/vercel/next.js/security/advisories/GHSA-3h52-269p-cp9r">https://github.com/vercel/next.js/security/advisories/GHSA-3h52-269p-cp9r</a>
          <a href="https://nvd.nist.gov/vuln/detail/CVE-2025-48068">https://nvd.nist.gov/vuln/detail/CVE-2025-48068</a>
          <a href="https://vercel.com/changelog/cve-2025-48068">https://vercel.com/changelog/cve-2025-48068</a>
          <a href="https://www.cve.org/CVERecord?id=CVE-2025-48068">https://www.cve.org/CVERecord?id=CVE-2025-48068</a>
        </td>
      </tr>
      <tr><th colspan="6">No Misconfigurations found</th></tr>
      <tr class="group-header"><th colspan="6"></th></tr>
      <tr><th colspan="6">No Vulnerabilities found</th></tr>
      <tr><th colspan="6">No Misconfigurations found</th></tr>
    </table>
  </body>
</html>
