# GLOBALS
ENV_SHORT_NAME: local

# DB
DB_NAME: "palmira_pro_db"
DB_HOST: "localhost"
DB_PORT: 5432
DB_USER: "postgres"
DB_PASSWORD: "mysecretpassword"
DB_SSL_MODE: "disable" # In prod: "require" https://www.postgresql.org/docs/current/libpq-ssl.html#LIBPQ-SSL-SSLMODE-STATEMENTS

# API
METABASE_SITE_URL: "https://zengate-global.metabaseapp.com"
METABASE_SECRET_KEY: "bdc8xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx02"
AUTH_JWT_KEYS_URL: "http://auth:3000/api/auth/jwks"
ALLOWED_ORIGIN: "http://localhost:8080"

# Auth
FIREBASE_AUTH_SECRET: 1wexxxxxxxxxxxxxxxxxxxxxMiy
FIREBASE_AUTH_URL: "http://localhost:3000"
FIREBASE_ADMIN_KEY_PATH=./serviceAccount.json

# Front end
NEXT_PUBLIC_BACKEND_URL: "http://localhost:3001/"
NEXT_PUBLIC_AUTH_URL: "http://localhost:3000/"

# Winter protocol
WINTER_BACKEND_URL: "https://ldgaetano-test-9-no-ogmios-winter-cardano-api-*********.asia-southeast1.run.app"
HONEY_HARVEST_SUMMARY_INTERVAL: "0 0 * * *"
CARDANO_TENANT_IDENTIFIER: "NATNECT"
