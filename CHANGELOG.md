# Changelog

## Next

## 1.2.10

- Removed farmer name from IPFS
- Fixed issue where duplicate entries are showing up in honey harvesting

## 1.2.9

- Fixed data aggregation in honey harvesting

## 1.2.8

- Enabled user to search farmer offline
- Improved ui/ux reset password

## 1.2.7

- Fix password reset functionality

## v1.2.6

- Change staging URL and fixed some bugs for Palmyra Pro CLI

## v1.2.5

- Fix contaminationStatus type should not be empty string
- Add contamination_status column to bucket table
- Fixing for historical data

## v1.2.4

- Action staging debug
- Automatically deletes the PR after it's merged

## v1.2.3

- Improved UX on batch management now it open a new tab cardano page
- Fixed Badge Bucket ID UI in Honey Processing
- Improved ui button to display buisness view on batch management detail page. Change button for a toggle button
- Prevent the user from selecting a future date — only past dates should be allowed
- Fixed Beehive Select on honey harvesting: now refresh the data when the farmer change
- Improved the UX on honey harvesting location selection : When you select a farmer the location is pre-fill but if it's wrong you can modify it
- Enable user to edit farmer offline
- Added Contamination Status front/back side (Off Quality Buckets)
- Enable user to add/edit/view beehive offline
- Farmer created offline can be use for honey harvesting offline

## v1.2.2

- Fixed mobile ui
- Fixed survey farmer select step
- Added Contamination status into the weight step of Honey Processing

## v1.2.1

- Increase cloud run timeout and scheduler deadline

## v1.2.0

- Change Form Compliance to Surveys to avoid confusion
- Decouple transaction region from farmer
- Assign batch code during processing, not harvesting
- Remove batch code from buckets
- Improve honey harvesting list load speed
- Allow specifying tenant id in environment variable

## v1.1.0

- Add Winter protocol integration for honey harvesting
- Updated the "Nationality" field on the farmer profile page to display the full country name instead of the country code
- Decimal input support has been added to input elements.
- The drum page in batch management has been redesigned.
- Decimal input support has been added to input elements.
- The drum page in batch management has been redesigned.
- Add Offline mode

## v1.0.0

- First MVP
