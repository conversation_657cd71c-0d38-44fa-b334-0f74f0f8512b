name: Create or Update an environment
run-name: >
  Create or Update ${{ inputs.shortName != '' && inputs.shortName || format('pr-{0}-{1}', github.event.pull_request.number, github.event.pull_request.user.login) }}
on:
  pull_request:
    types: [opened, reopened, edited]
  workflow_dispatch:
    inputs:
      environment:
        type: environment
        description: "Environment"
        required: true
        default: "development"
      shortName:
        type: string
        description: "staging, prod or any other short name used as prefix for resources, labels, etc."
        required: true
        default: "PR"
      seedDev:
        type: boolean
        description: "Seed database with dev data"
        required: true
        default: true

jobs:
  deploy:
    name: Create a new environment
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment || 'development' }}
    env:
      DB_INSTANCE_CONNECTION_NAME: "${{ vars.GCP_PROJECT_ID }}:${{ vars.GCP_REGION }}:${{ vars.DB_INSTANCE }}"
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Use Node.js from .nvmrc
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: "npm"

      - name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}

      - name: Configure Docker
        run: gcloud auth configure-docker "${{ vars.GCP_REGION }}-docker.pkg.dev"

      - name: Set dynamic ENV
        run: |
          # If inputs.shortName is not present, it means its a PR.
          INPUTS_SHORT_NAME="${{ inputs.shortName || 'PR' }}"
          
          if [[ "${INPUTS_SHORT_NAME}" == "prod" ]]; then
            SHORT_NAME="${INPUTS_SHORT_NAME}"
            CHANNEL_ID="live"
            TAG_NAME="latest"
            FE_URL="https://naturesnectar.palmyra.pro"
            SEED_DEV=${{ inputs.seedDev }}
          
            echo "FE_URL=${TMP_PREVIEW_URL}" >> $GITHUB_ENV
            
            # To avoid mistakes, let's don't allow prod deployment into development.
            if [[ "${{ inputs.environment || 'development' }}" != "production" ]]; then
              exit 1
            fi
          elif [[ "${INPUTS_SHORT_NAME}" == "staging" ]]; then
            SHORT_NAME="${INPUTS_SHORT_NAME}"
            CHANNEL_ID="live"
            TAG_NAME="staging-latest"
            FE_URL="https://natures-nectar-dev.web.app"
            SEED_DEV=${{ inputs.seedDev }}
          elif [[ "${INPUTS_SHORT_NAME}" == "PR" ]]; then
            RAW_NAME="pr-${{ github.event.pull_request.number }}-${{ github.event.pull_request.user.login }}"
            SHORT_NAME=$(echo "${RAW_NAME}" | tr '[:upper:]' '[:lower:]' | tr -c 'a-z0-9-' '-' | cut -c1-40)
            CHANNEL_ID="${SHORT_NAME}"
            TAG_NAME="${SHORT_NAME}"
            FE_URL=""
            SEED_DEV="true"
          else
            SHORT_NAME="${INPUTS_SHORT_NAME}"
            CHANNEL_ID="${INPUTS_SHORT_NAME}"
            TAG_NAME="${INPUTS_SHORT_NAME}"
            FE_URL=""
            SEED_DEV=${{ inputs.seedDev }}
          fi
          echo "SHORT_NAME=$SHORT_NAME" >> $GITHUB_ENV
          echo "TAG_NAME=$TAG_NAME" >> $GITHUB_ENV
          echo "CHANNEL_ID=$CHANNEL_ID" >> $GITHUB_ENV
          echo "SEED_DEV=$SEED_DEV" >> $GITHUB_ENV
          if [[ "$SHORT_NAME" == "staging" ]]; then
            MIN_INSTANCES=1
          else
            MIN_INSTANCES=0
          fi
          echo "MIN_INSTANCES=$MIN_INSTANCES" >> $GITHUB_ENV
          echo "GCP_PROJECT_ID=${{ vars.GCP_PROJECT_ID }}" >> $GITHUB_ENV
          GCP_PROJECT_NUMBER=$(gcloud projects describe ${{ vars.GCP_PROJECT_ID }} --format='value(projectNumber)')
          echo "GCP_PROJECT_NUMBER=${GCP_PROJECT_NUMBER}" >> $GITHUB_ENV
          echo "GCP_DB_PASSWORD_SECRET_NAME=DB_PASSWORD_$(echo "${{ vars.DB_INSTANCE }}" | tr '[:lower:]' '[:upper:]' | tr '-' '_')" >> $GITHUB_ENV
          echo "DB_NAME=palmyrapro-db-$SHORT_NAME" >> $GITHUB_ENV
          # echo "DB_HOST=$(gcloud sql instances describe ${{ vars.DB_INSTANCE }} --format=json | jq -r '.ipAddresses[] | select(.type == "PRIMARY") | .ipAddress')" >> $GITHUB_ENV
          echo "DB_HOST=/cloudsql/${DB_INSTANCE_CONNECTION_NAME}" >> $GITHUB_ENV
          echo "DB_PASSWORD=${{ secrets.DB_PASSWORD }}" >>  $GITHUB_ENV
          echo "DB_USER=${{ vars.DB_USER }}" >> $GITHUB_ENV
          echo "DB_PORT=${{ vars.DB_PORT }}" >> $GITHUB_ENV
          
          echo "FE_URL=${FE_URL}" >> $GITHUB_ENV
          echo "API_URL=https://$SHORT_NAME-nn-api-${GCP_PROJECT_NUMBER}.${{ vars.GCP_REGION }}.run.app" >> $GITHUB_ENV
          echo "AUTH_URL=https://$SHORT_NAME-nn-auth-${GCP_PROJECT_NUMBER}.${{ vars.GCP_REGION }}.run.app" >> $GITHUB_ENV
      - name: Install Cloud SQL Auth Proxy
        run: |
          curl -o cloud-sql-proxy https://storage.googleapis.com/cloud-sql-connectors/cloud-sql-proxy/v2.15.2/cloud-sql-proxy.linux.amd64
          chmod +x cloud-sql-proxy
          sudo mv cloud-sql-proxy /usr/local/bin/
      - name: Start Cloud SQL Proxy
        run: |
          cloud-sql-proxy $DB_INSTANCE_CONNECTION_NAME --credentials-file=$GOOGLE_APPLICATION_CREDENTIALS --port 5432 &
          # Wait and check if port is open
          for i in {1..10}; do
            echo "Checking if proxy is up (attempt $i)..."
            nc -z 127.0.0.1 5432 && break
            sleep 2
          done
          # Final check
          if ! nc -z 127.0.0.1 5432; then
            echo "❌ Cloud SQL Proxy failed to start."
            exit 1
          fi
          echo "✅ Cloud SQL Proxy is up and running."
      - name: Check if database exists
        id: check-db
        env:
          PGPASSWORD: ${{ env.DB_PASSWORD }}
        run: |
          DB_EXISTS=$(psql -h 127.0.0.1 -U $DB_USER -d postgres \
            -tAc "SELECT 1 FROM pg_database WHERE datname = '$DB_NAME'" | grep -q 1 && echo true || echo false)
          echo "exists=$DB_EXISTS" >> $GITHUB_OUTPUT
      - name: Create Cloud SQL schema
        if: steps.check-db.outputs.exists == 'false'
        run: |
          gcloud sql databases create $DB_NAME --instance=${{ vars.DB_INSTANCE }}
      - name: Run DB migrations / init scripts
        if: steps.check-db.outputs.exists == 'false'
        env:
          PGPASSWORD: ${{ secrets.DB_PASSWORD }}
        run: |
          psql "host=127.0.0.1 port=5432 user=$DB_USER dbname=$DB_NAME" -f "packages/db/schema/01.01-ddl.sql"
          psql "host=127.0.0.1 port=5432 user=$DB_USER dbname=$DB_NAME" -f "packages/db/schema/01.02.lookups.sql"
          psql "host=127.0.0.1 port=5432 user=$DB_USER dbname=$DB_NAME" -f "packages/db/schema/01.03-honey_harvesting.sql"
          psql "host=127.0.0.1 port=5432 user=$DB_USER dbname=$DB_NAME" -f "packages/db/schema/01.04-honey_processing.sql"
          psql "host=127.0.0.1 port=5432 user=$DB_USER dbname=$DB_NAME" -f "packages/db/schema/02-seeding.sql"
      - name: Seed with dev data
        if: steps.check-db.outputs.exists == 'false' && env.SEED_DEV == true
        env:
          PGPASSWORD: ${{ secrets.DB_PASSWORD }}
        run: |
          psql "host=127.0.0.1 port=5432 user=$DB_USER dbname=$DB_NAME" -f "packages/db/schema/04.00-seeding_regions_dev.sql"
          psql "host=127.0.0.1 port=5432 user=$DB_USER dbname=$DB_NAME" -f "packages/db/schema/04.01-seeding_dev.sql"
          psql "host=127.0.0.1 port=5432 user=$DB_USER dbname=$DB_NAME" -f "packages/db/schema/04.02-honey_harvesting-seeding_dev.sql"
          psql "host=127.0.0.1 port=5432 user=$DB_USER dbname=$DB_NAME" -f "packages/db/schema/04.03-honey_processing-seeding_dev.sql"
      # TODO: From here, should be in a external Deploy Frontend workflow and reuse it.
      - name: FE. Build
        env:
#          FIXME: We need these two!!!!
          NEXT_PUBLIC_BACKEND_URL: "${{ env.API_URL }}"
          NEXT_PUBLIC_AUTH_URL: "${{ env.AUTH_URL }}"
        run: npm ci && npm run generate --workspace=packages/api-specs && npm run build --workspace=apps/honey-producers && du -sh apps/honey-producers/dist/.

      - name: Deploy to Firebase Hosting
        id: deploy
        working-directory: apps/honey-producers
        env:
          NODE_OPTIONS: "--trace-deprecation"
        run: |
          npm install -g firebase-tools
          # Deploy
          TMP_OUTPUT=$(mktemp)
          
          EXIT_CODE=0
          if [ "$CHANNEL_ID" == "live" ]; then
            firebase deploy \
            --only hosting \
            --project="${{ vars.GCP_PROJECT_ID }}" \
            --json >"$TMP_OUTPUT" 2>&1 || EXIT_CODE=$?
          
            if [ $EXIT_CODE -ne 0 ]; then
              echo "❌ Firebase deploy failed:"
              cat "$TMP_OUTPUT"
              rm "$TMP_OUTPUT"
              exit $EXIT_CODE
            fi
          
            # Extract and comment the PREVIEW URL
            TMP_PREVIEW_URL="https://${{ vars.GCP_PROJECT_ID }}.web.app/"
          
            echo "✅ Preview URL: $TMP_PREVIEW_URL"
            echo "PREVIEW_URL=$TMP_PREVIEW_URL" >> "$GITHUB_OUTPUT"
          
          else
            firebase hosting:channel:deploy "$CHANNEL_ID" \
              --project="${{ vars.GCP_PROJECT_ID }}" \
              --json >"$TMP_OUTPUT" 2>&1 || EXIT_CODE=$?
          
            if [ $EXIT_CODE -ne 0 ]; then
              echo "❌ Firebase deploy failed:"
              cat "$TMP_OUTPUT"
              rm "$TMP_OUTPUT"
              exit $EXIT_CODE
            fi
          
            # Extract and comment the PREVIEW URL
            TMP_PREVIEW_URL=$(jq -r '.result[].url' ${TMP_OUTPUT})
            echo "✅ Preview URL: $TMP_PREVIEW_URL"
            echo "PREVIEW_URL=$TMP_PREVIEW_URL" >> "$GITHUB_OUTPUT"
          
            echo "FE_URL=${TMP_PREVIEW_URL}" >> $GITHUB_ENV
          fi
      - name: Comment on PR with preview URL
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          message: |
            ✅ Preview deployed to: ${{ steps.deploy.outputs.PREVIEW_URL }}
      - name: Auth. Build and Push Docker Image
        run: |
          docker build -t ${{ vars.GCP_REGION }}-docker.pkg.dev/${{ vars.GCP_PROJECT_ID }}/palmyra-pro-images/nn-auth:$TAG_NAME -f apps/auth/Dockerfile .
          docker push ${{ vars.GCP_REGION }}-docker.pkg.dev/${{ vars.GCP_PROJECT_ID }}/palmyra-pro-images/nn-auth:$TAG_NAME
      - name: Auth. Deploy to Cloud Run
        run: |
          # TODO: Do we really need this url before creating the service? For what is used BETTER_AUTH_URL here?
          # echo "BETTER_AUTH_URL=https://$SHORT_NAME-nn-auth-${GCP_PROJECT_NUMBER}.${{ vars.GCP_REGION }}.run.app" >> $GITHUB_ENV
          gcloud run deploy $SHORT_NAME-nn-auth \
            --image ${{ vars.GCP_REGION }}-docker.pkg.dev/${{ vars.GCP_PROJECT_ID }}/palmyra-pro-images/nn-auth:$TAG_NAME \
            --region ${{ vars.GCP_REGION }} \
            --project ${{ vars.GCP_PROJECT_ID }} \
            --execution-environment gen2 \
            --min-instances ${{ env.MIN_INSTANCES }} \
            --max-instances 2 \
            --concurrency 1000 \
            --port 3000 \
            --add-cloudsql-instances $DB_INSTANCE_CONNECTION_NAME \
            --set-env-vars "DB_NAME=${{ env.DB_NAME }}" \
            --set-env-vars "DB_HOST=${{ env.DB_HOST }}" \
            --set-env-vars "DB_PORT=${{ vars.DB_PORT }}" \
            --set-env-vars "DB_USER=${{ vars.DB_USER }}" \
            --set-secrets  "DB_PASSWORD=${{ env.GCP_DB_PASSWORD_SECRET_NAME }}:latest" \
            --set-env-vars "FE_URL=${{ env.FE_URL }}" \
            --set-env-vars "BETTER_AUTH_URL=${{ env.AUTH_URL }}" \
            --set-env-vars "ENV_SHORT_NAME=${{ env.SHORT_NAME }}" \
            --set-env-vars "SENDGRID_API_KEY=${{ vars.SENDGRID_API_KEY }}" \
            --set-env-vars "METABASE_SITE_URL=${{ vars.METABASE_SITE_URL }}" \
            --set-secrets  "BETTER_AUTH_SECRET=BETTER_AUTH_SECRET:latest" \
            --labels "env_short_name=${{ env.SHORT_NAME }}"
          # TODO: Better if Firebase is routing this service into a static context path
          gcloud run services add-iam-policy-binding $SHORT_NAME-nn-auth \
            --region ${{ vars.GCP_REGION }} \
            --project ${{ vars.GCP_PROJECT_ID }} \
            --member="allUsers" \
            --role="roles/run.invoker"
          echo "AUTH_URL=${AUTH_URL}" >> $GITHUB_ENV
          echo "AUTH_JWT_KEYS_URL=${AUTH_URL}/api/auth/jwks" >> $GITHUB_ENV
      - name: Auth. Comment on PR with endpoint URL
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          message: |
            ✅ NN Auth deployed to: "${{ env.AUTH_URL }}"
      # TODO: From here, should be in a external Deploy API workflow and reuse it.
      - name: Winter Protocol. Build and Push Docker Image
        run: |
          docker build -t ${{ vars.GCP_REGION }}-docker.pkg.dev/${{ vars.GCP_PROJECT_ID }}/palmyra-pro-images/nn-wp:$TAG_NAME -f ./apps/api/Dockerfile.WinterProtocol .
          docker push ${{ vars.GCP_REGION }}-docker.pkg.dev/${{ vars.GCP_PROJECT_ID }}/palmyra-pro-images/nn-wp:$TAG_NAME
      - name: Winter Protocol. Deploy to Cloud Run
        run: |
          gcloud run deploy $SHORT_NAME-nn-wp \
            --image ${{ vars.GCP_REGION }}-docker.pkg.dev/${{ vars.GCP_PROJECT_ID }}/palmyra-pro-images/nn-wp:$TAG_NAME \
            --region ${{ vars.GCP_REGION }} \
            --project ${{ vars.GCP_PROJECT_ID }} \
            --execution-environment gen2 \
            --min-instances ${{ env.MIN_INSTANCES }} \
            --max-instances 2 \
            --concurrency 1000 \
            --port 3000 \
            --add-cloudsql-instances $DB_INSTANCE_CONNECTION_NAME \
            --set-env-vars "DB_NAME=${{ env.DB_NAME }}" \
            --set-env-vars "DB_HOST=${{ env.DB_HOST }}" \
            --set-env-vars "DB_PORT=${{ vars.DB_PORT }}" \
            --set-env-vars "DB_USER=${{ vars.DB_USER }}" \
            --set-env-vars "DB_SSL_MODE=${{ vars.DB_SSL_MODE }}" \
            --set-secrets  "DB_PASSWORD=${{ env.GCP_DB_PASSWORD_SECRET_NAME }}:latest" \
            --set-env-vars "WINTER_BACKEND_URL=${{ vars.WINTER_BACKEND_URL }}" \
            --set-env-vars "CARDANO_TENANT_IDENTIFIER"="${{ vars.CARDANO_TENANT_IDENTIFIER }}" \
            --set-env-vars "ENV_SHORT_NAME=${{ env.SHORT_NAME }}" \
            --timeout=30m \
            --labels "env_short_name=${{ env.SHORT_NAME }}"
          # TODO: Better if Firebase is routing this service into a static context path
          gcloud run services add-iam-policy-binding $SHORT_NAME-nn-wp \
            --region ${{ vars.GCP_REGION }} \
            --project ${{ vars.GCP_PROJECT_ID }} \
            --member="serviceAccount:${{ vars.SERVICE_ACCOUNT }}" \
            --role="roles/run.invoker"
      # Create a scheduler job
      - name: Winter Protocol. Create Scheduler Job
        run: |
          JOB_NAME="${{ env.SHORT_NAME }}-nn-wp-scheduler"
          if gcloud scheduler jobs describe "$JOB_NAME" --location="${{ vars.GCP_REGION }}" --project="${{ vars.GCP_PROJECT_ID }}" > /dev/null 2>&1; then
            echo "Job $JOB_NAME already exists. Deleting it..."
            gcloud scheduler jobs delete "$JOB_NAME" --location="${{ vars.GCP_REGION }}" --project="${{ vars.GCP_PROJECT_ID }}" --quiet
          fi
          
          
          gcloud scheduler jobs create http "$JOB_NAME" \
            --schedule="${{ vars.HONEY_HARVEST_SUMMARY_INTERVAL }}" \
            --http-method=GET \
            --uri="https://${{ env.SHORT_NAME }}-nn-wp-${{ env.GCP_PROJECT_NUMBER }}.${{ vars.GCP_REGION }}.run.app/run-harvest-summary" \
            --oidc-service-account-email="${{ vars.SERVICE_ACCOUNT }}" \
            --time-zone="Asia/Tokyo" \
            --location="${{ vars.GCP_REGION }}" \
            --project="${{ vars.GCP_PROJECT_ID }}" \
            --attempt-deadline=1800s
          
          gcloud scheduler jobs pause "$JOB_NAME" --project="${{ vars.GCP_PROJECT_ID }}" --location="${{ vars.GCP_REGION }}"
      - name: API. Build and Push Docker Image
        run: |
          docker build -t ${{ vars.GCP_REGION }}-docker.pkg.dev/${{ vars.GCP_PROJECT_ID }}/palmyra-pro-images/nn-api:$TAG_NAME -f ./apps/api/Dockerfile .
          docker push ${{ vars.GCP_REGION }}-docker.pkg.dev/${{ vars.GCP_PROJECT_ID }}/palmyra-pro-images/nn-api:$TAG_NAME
      - name: API. Deploy to Cloud Run
        run: |
          gcloud run deploy $SHORT_NAME-nn-api \
            --image ${{ vars.GCP_REGION }}-docker.pkg.dev/${{ vars.GCP_PROJECT_ID }}/palmyra-pro-images/nn-api:$TAG_NAME \
            --region ${{ vars.GCP_REGION }} \
            --project ${{ vars.GCP_PROJECT_ID }} \
            --execution-environment gen2 \
            --min-instances ${{ env.MIN_INSTANCES }} \
            --max-instances 2 \
            --concurrency 1000 \
            --allow-unauthenticated \
            --port 3000 \
            --add-cloudsql-instances $DB_INSTANCE_CONNECTION_NAME \
            --set-env-vars "DB_NAME=${{ env.DB_NAME }}" \
            --set-env-vars "DB_HOST=${{ env.DB_HOST }}" \
            --set-env-vars "DB_PORT=${{ vars.DB_PORT }}" \
            --set-env-vars "DB_USER=${{ vars.DB_USER }}" \
            --set-env-vars "DB_SSL_MODE=${{ vars.DB_SSL_MODE }}" \
            --set-secrets  "DB_PASSWORD=${{ env.GCP_DB_PASSWORD_SECRET_NAME }}:latest" \
            --set-env-vars "^@^ALLOWED_ORIGIN=${{ vars.ALLOWED_ORIGIN }}" \
            --set-env-vars "AUTH_JWT_KEYS_URL=${{ env.AUTH_JWT_KEYS_URL }}" \
            --set-env-vars "METABASE_SITE_URL=${{ vars.METABASE_SITE_URL }}" \
            --set-env-vars "ENV_SHORT_NAME=${{ env.SHORT_NAME }}" \
            --set-secrets "METABASE_SECRET_KEY=METABASE_SECRET_KEY:latest" \
            --labels "env_short_name=${{ env.SHORT_NAME }}"
          # TODO: Better if Firebase is routing this service into a static context path
          gcloud run services add-iam-policy-binding $SHORT_NAME-nn-api \
            --region ${{ vars.GCP_REGION }} \
            --project ${{ vars.GCP_PROJECT_ID }} \
            --member="allUsers" \
            --role="roles/run.invoker"
      - name: API. Comment on PR with endpoint URL
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          message: |
            ✅ NN API deployed to: "$API_URL"