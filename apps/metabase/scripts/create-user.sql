-- Create the user with no permissions.
CREATE USER analytics_read WITH PASSWORD 'xxxxxxxx';

-- Grant connection to the DB (already done by \c, but for completeness):
GRANT CONNECT ON <PERSON><PERSON><PERSON><PERSON><PERSON> "naturesnectar-db" TO analytics_read;

-- Grant usage on the schema
GRANT USAGE ON SCHEMA public TO analytics_read;

-- Grant SELECT on all existing tables
GRANT SELECT ON ALL TABLES IN SCHEMA public TO analytics_read;

-- Optional: grant SELECT on all sequences if needed (for serial columns, etc.)
GRANT SELECT ON ALL SEQUENCES IN SCHEMA public TO analytics_read;

-- Ensure future tables are also covered
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO analytics_read;

-- (Optional) Also for future sequences
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON SEQUENCES TO analytics_read;

