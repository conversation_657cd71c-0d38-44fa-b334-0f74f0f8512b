-- assetStatus_staging_2
-- asset_staging_2
-- farmer_2
-- processingItem_staging_2
-- regions_zambia_staging_v2
-- role
-- country
-- business
-- assetType

DROP TABLE if exists "regions_zambia_staging";
DROP TABLE if exists "assetStatus";
DROP TABLE if exists "staging_farmer_eastern_data";
DROP TABLE if exists "beehive_locations_eastern_data";
DROP TABLE if exists "beehive_locations_eastern_data_2";
DROP TABLE if exists "staging_farmer_eastern_data_v2";
DROP TABLE if exists "honey_purchase_eastern_data";

drop table if exists farmer_backup;
drop table if exists verification;
drop table if exists session;
drop table if exists jwks;
drop table if exists message;
drop table if exists account;
drop table if exists certificate;
drop table if exists role_reports;
drop table if exists form;
drop table if exists "administrativeArea";

drop table if exists event;
drop table if exists "eventTemplate";
drop table if exists commodity;

drop table if exists honey_process_type;
drop table if exists honey_processing_log;
drop table if exists honey_processing_log_buckets;
drop table if exists bucket;
drop table if exists transaction_asset;
drop table if exists transaction;

drop table if exists "processingItemAsset";
drop table if exists "processingItem";
drop table if exists "post_processing_inventory";

drop table if exists region;

drop table if exists asset_staging;
drop table if exists asset;

drop table if exists "processingItem_staging";
drop table if exists "assetstatus_staging";
drop table if exists farmer;
drop table if exists "user";

drop table if exists "businessFarm";
drop table if exists "harvestLocation";
drop table if exists farm;
drop table if exists factory;

drop table if exists currency;


UPDATE farmer_2 SET metadata = metadata - 'nrc_number';

UPDATE farmer_2
SET "lastName" = LEFT("lastName", 1) || '.'
WHERE "lastName" IS NOT NULL;


ALTER TABLE "assetstatus_staging_2" RENAME TO "asset_status";
ALTER TABLE "asset_staging_2" RENAME TO "asset";
ALTER TABLE "farmer_2" RENAME TO "farmer";
ALTER TABLE "processingItem_staging_2" RENAME TO "processing_item";
ALTER TABLE "regions_zambia_staging_v2" RENAME TO "regions";
alter table "assetType" rename to "asset_type"

