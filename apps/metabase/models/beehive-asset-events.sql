SELECT
    "source"."event_id" AS "event_id",
    "source"."asset_id" AS "asset_id",
    "source"."latitude" AS "latitude",
    "source"."longitude" AS "longitude",
    "source"."repair" AS "repair",
    "source"."occupancy" AS "occupancy",
    "source"."beehive_observation" AS "beehive_observation",
    "source"."farmer_id" AS "farmer_id",
    "source"."event_creation_date" AS "event_creation_date",
    "source"."event_updated_date" AS "event_updated_date",
    "source"."event_created_by" AS "event_created_by",
    "source"."Recent_flag" AS "Recent_flag",
    "source"."assetTypeId" AS "assetTypeId",
    "source"."activeStatus" AS "activeStatus",
    "source"."businessId" AS "businessId",
    "source"."asset_creation_date" AS "asset_creation_date",
    "source"."asset_updated_date" AS "asset_updated_date",
    "source"."farmer_contract_start_year" AS "farmer_contract_start_year",
    "source"."farmer_first_name" AS "farmer_first_name",
    "source"."farmer_last_name" AS "farmer_last_name",
    "source"."region" AS "region",
    "source"."chiefdom" AS "chiefdom",
    "source"."zone_code" AS "zone_code",
    "source"."zones" AS "zones",
    "source"."village_name" AS "village_name"
FROM
    (
        SELECT
            a.id as event_id,
            a."assetId" as asset_id,
            a.latitude :: NUMERIC(10, 6) AS latitude,
            a.longitude :: NUMERIC(10, 6) AS longitude,
            a.metadata ->> 'repair' as repair,
            a.metadata ->> 'occupancy' as occupancy,
            a.metadata ->> 'beehive_observation' as beehive_observation,
            a."farmerId" as farmer_id,
            a."createdAt" as event_creation_date,
            a."updatedAt" as event_updated_date,
            a."createdBy" as event_created_by,
            CASE
                WHEN b."updatedAt" IS NULL THEN 1 -- Flag as most recent (1) if no newer entry exists
                ELSE 0 -- Otherwise, not the most recent (0)
                END AS "Recent_flag" -- Static Asset Data
                ,
            c."assetTypeId",
            c."activeStatus",
            c."businessId",
            c."createdAt" as asset_creation_date,
            c."updatedAt" as asset_updated_date -- Farmer Data
                ,
            EXTRACT(
                    YEAR
                    FROM
                    TO_TIMESTAMP(
                            f.metadata ->> 'contract_date',
                            'YYYY-MM-DD"T"HH24:MI:SS'
                    )
            ) AS farmer_contract_start_year,
            f."firstName" as farmer_first_name,
            f."lastName" as farmer_last_name -- Region Data
                ,
            re.region as region,
            re.chiefdom as chiefdom,
            re.zone_code,
            re.zones,
            re.village_name
        FROM
            asset_status a

                LEFT JOIN asset_status b ON a."assetId" = b."assetId"

                AND a."updatedAt" < b."updatedAt" -- Searches for the newest entry
                LEFT JOIN asset as c ON a."assetId" = c.id
                LEFT JOIN farmer as f on f.id = a."farmerId"
                LEFT JOIN regions as re on re.id :: text = f."regionId" -- made some changes to accomodate the join
    ) AS "source"
LIMIT
    1048575