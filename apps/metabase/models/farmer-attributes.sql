SELECT
    "source"."farmer_id" AS "farmer_id",
    "source"."firstName" AS "firstName",
    "source"."lastName" AS "lastName",
    "source"."gender" AS "gender",
    "source"."maritalStatus" AS "maritalStatus",
    "source"."dob" AS "dob",
    "source"."sourceOfIncome" AS "sourceOfIncome",
    "source"."farmer_longitude" AS "farmer_longitude",
    "source"."farmer_latitude" AS "farmer_latitude",
    "source"."contract_date" AS "contract_date",
    "source"."role_name" AS "role_name",
    "source"."region" AS "region",
    "source"."chiefdom" AS "chiefdom",
    "source"."zone_code" AS "zone_code",
    "source"."zones" AS "zones",
    "source"."village_name" AS "village_name",
    "source"."beehive_count" AS "beehive_count",
    "source"."total_honeycomb_weight_sold" AS "total_honeycomb_weight_sold",
    "source"."total_honeycomb_amount_sold" AS "total_honeycomb_amount_sold",
    "source"."total_honeycomb_unique_transactions" AS "total_honeycomb_unique_transactions",
    "source"."total_honeycomb_buckets_sold" AS "total_honeycomb_buckets_sold"
FROM
    (
        select
            f.id as farmer_id,
            f."firstName",
            f."lastName",
            f.gender,
            f."maritalStatus",
            f.dob --, CAST(f."householdSize"  AS INTEGER) AS "householdSize"
                ,
            "sourceOfIncome",
            (f.metadata ->> 'longitude') :: NUMERIC(10, 6) AS farmer_longitude,
            (f.metadata ->> 'latitude') :: NUMERIC(10, 6) AS farmer_latitude,
            f.metadata ->> 'contract_year' as contract_date -- Addin Columns From Other Tables
                ,
            r.name as role_name,
            re.region as region,
            re.chiefdom as chiefdom,
            re.zone_code,
            re.zones,
            re.village_name -- Aggregated Beehive data
                ,
            b.beehive_count -- Aggregate Transaction data
                ,
            pis.total_honeycomb_weight_sold,
            pis.total_honeycomb_amount_sold,
            pis.total_honeycomb_unique_transactions,
            pis.total_honeycomb_buckets_sold
        from
            public.farmer f
                left join public.role r ON f."roleId" = r.id
                left join public.regions re on f."regionId" = re.id :: TEXT -- may need to get fixed later
                left join (
                select
                    distinct "farmerId" as farmer_id,
                             count (distinct "assetId") as beehive_count
                from
                    public."asset_status" at2
                group by
                    1
            ) as b on f.id = b.farmer_id
                left join (
                select
                    distinct "purchasedFrom" as farmer_id,
                             sum(quantity) as total_honeycomb_weight_sold,
                             sum("amountPaid") as total_honeycomb_amount_sold,
                             count (distinct "batchId") as total_honeycomb_unique_transactions,
                             count (distinct id) as total_honeycomb_buckets_sold
                from
                    "processing_item" pis
                where
                    quantity is not null
                group by
                    1
            ) as pis ON pis.farmer_id = f.id
        where
            f.id NOT IN ('4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14')
    ) AS "source"
LIMIT
    1048575