SELECT
    "source"."transaction_id" AS "transaction_id",
    "source"."farmer_id" AS "farmer_id",
    "source"."event_recorded_by" AS "event_recorded_by",
    "source"."event_created_at" AS "event_created_at",
    "source"."event_updated_at" AS "event_updated_at",
    "source"."count_of_buckets" AS "count_of_buckets",
    "source"."quantity" AS "quantity",
    "source"."amountPaid" AS "amountPaid",
    "source"."commission_per_kg" AS "commission_per_kg",
    "source"."commission_calculated" AS "commission_calculated",
    "source"."honey_price_per_kg" AS "honey_price_per_kg",
    "source"."harvest_season" AS "harvest_season",
    "source"."farmer_contract_start_year" AS "farmer_contract_start_year",
    "source"."farmer_first_name" AS "farmer_first_name",
    "source"."farmer_last_name" AS "farmer_last_name",
    "source"."farmer_longitude" AS "farmer_longitude",
    "source"."farmer_latitude" AS "farmer_latitude",
    "source"."gender" AS "gender",
    "source"."region" AS "region",
    "source"."chiefdom" AS "chiefdom",
    "source"."zone_code" AS "zone_code",
    "source"."zones" AS "zones",
    "source"."village_name" AS "village_name"
FROM
    (
        select
            axis.*,
            CASE
                WHEN EXTRACT(
                        MONTH
                        FROM
                        event_created_at
                     ) BETWEEN 11
                    AND 12 THEN EXTRACT(
                                        YEAR
                                        FROM
                                        event_created_at
                                ) || '/' || (
                    EXTRACT(
                            YEAR
                            FROM
                            event_created_at
                    ) + 1
                    ) || '-Summer Harvest'
                WHEN EXTRACT(
                        MONTH
                        FROM
                        event_created_at
                     ) BETWEEN 1 AND 5 THEN (
                                                EXTRACT(
                                                        YEAR
                                                        FROM
                                                        event_created_at
                                                ) - 1
                                                ) || '/' || EXTRACT(
                        YEAR
                        FROM
                        event_created_at
                                                              ) || '-Summer Harvest'
                WHEN EXTRACT(
                        MONTH
                        FROM
                        event_created_at
                     ) BETWEEN 6 AND 10 THEN EXTRACT(
                                                     YEAR
                                                     FROM
                                                     event_created_at
                                             ) || '-Winter Harvest'
                ELSE 'Unknown Season'
                END AS harvest_season -- Farmer Data
                ,
            EXTRACT(
                    YEAR
                    FROM
                    TO_TIMESTAMP(
                            f.metadata ->> 'contract_date',
                            'YYYY-MM-DD"T"HH24:MI:SS'
                    )
            ) AS farmer_contract_start_year,
            f."firstName" as farmer_first_name,
            f."lastName" as farmer_last_name,
            (f.metadata ->> 'longitude') :: NUMERIC(10, 6) AS farmer_longitude,
            (f.metadata ->> 'latitude') :: NUMERIC(10, 6) AS farmer_latitude,
            f.gender -- Region Data
                ,
            re.region as region,
            re.chiefdom as chiefdom,
            re.zone_code,
            re.zones,
            re.village_name -- Grouping it up at the txn level (not the bucket level)
        from
            (
                select
                    pis."transactionId" as transaction_id,
                    pis."purchasedFrom" as farmer_id,
                    pis."recordedBy" as event_recorded_by,
                    MIN(pis."createdAt") as event_created_at,
                    MAX(pis."updatedAt") as event_updated_at,
                    COUNT(distinct pis.id) as count_of_buckets,
                    SUM(pis.quantity) as quantity,
                    SUM(pis."amountPaid") as "amountPaid",
                    MAX(
                            CAST(pis.metadata ->> 'commission_per_kg' as NUMERIC)
                    ) as commission_per_kg,
                    SUM(
                            CAST(pis.metadata ->> 'commission_calculated' as NUMERIC)
                    ) as commission_calculated,
                    MAX(
                            CAST(pis.metadata ->> 'honey_price_per_kg' as NUMERIC)
                    ) as honey_price_per_kg
                from
                    "processing_item" pis
                group by
                    1,
                    2,
                    3
                order by
                    1,
                    2,
                    3
            ) as axis

                LEFT JOIN farmer as f on f.id = axis.farmer_id
                LEFT JOIN regions as re on re.id :: TEXT = f."regionId"
    ) AS "source"
LIMIT
    1048575