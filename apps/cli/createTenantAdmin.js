import admin from "firebase-admin"

admin.initializeApp({
  credential: admin.credential.cert("./serviceAccount.json"),
})

const auth = admin.auth()

/**
 * @param {string} tenantId
 * @param {string} email
 * @param {string} password
 */
async function createTenantAdmin(tenantId, email, password) {
  try {
    const tenantAuth = auth.tenantManager().authForTenant(tenantId)

    const userRecord = await tenantAuth.createUser({
      email,
      password,
      emailVerified: false,
    })

    await tenantAuth.setCustomUserClaims(userRecord.uid, { admin: true })

    console.log(`Admin user ${email} created in tenant ${tenantId}`)
    return userRecord
  } catch (error) {
    console.error("Error creating admin user:", error)
    throw error
  }
}

const [tenantId, email, password] = process.argv.slice(2)
if (!tenantId || !email || !password) {
  console.error(
    "Usage: node createTenantAdmin.js <tenantId> <email> <password>"
  )
  process.exit(1)
}

createTenantAdmin(tenantId, email, password)
  .then(() => process.exit(0))
  .catch(() => process.exit(1))
