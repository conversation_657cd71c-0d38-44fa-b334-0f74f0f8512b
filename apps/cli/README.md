# 📦 Palmyra Pro CLI

## You can log in, create users as an admin, view session info, and generate password reset links.

---

## 🚀 Installation

### 1. Install dependencies

```bash
npm install
```

### 2. Build the CLI

```bash
##  cd apps/cli (If you're going to use it outside of the Turborepo, please make sure you're inside the cli folder)
npm run build
```

### 3. Link globally

```bash
npm link
```

This will make `palmyra-pro` available as a global command.

---

## 🌐 Environment Selection (`--env`)

Instead of using `.env` files, Palmyra CLI uses a **persistent config system** via the `--env` flag.

### Set Environment (saves to `~/.palmyra-pro/config.json`)

```bash
palmyra-pro --env prod
palmyra-pro --env staging
palmyra-pro --env local
palmyra-pro --env your-environment-name
```

This sets the base URLs used internally by the CLI.

### View Current Environment

```bash
palmyra-pro --env
```

---

## ✅ Supported Environments

| Environment               | AUTH_URL                                                      | PUBLIC_URL                                                  |
| ------------------------- | ------------------------------------------------------------- | ----------------------------------------------------------- |
| `production`              | https://prod-nn-auth-430483576123.europe-west1.run.app        | https://naturesnectar.palmyra.pro                           |
| `local`                   | http://localhost:3000                                         | http://localhost:8080                                       |
| custom (e.g. `davis-347`) | `https://davis-347-nn-auth-463523546.asia-southeast1.run.app` | `https://davis-347-nn-fe-463523546.asia-southeast1.run.app` |

---

## 🧩 CLI Commands

### 🔐 Login

```bash
palmyra-pro login -e <EMAIL> -p yourpassword
```

Stores the token in `~/.palmyra-pro/config.json`.

---

### 👤 Who Am I

```bash
palmyra-pro whoami
```

---

### ➕ Sign Up (Admin Only)

```bash
palmyra-pro sign-up -n "Ali Bektash" -e "<EMAIL>"
```

Creates a new user with a system-generated password.  
Only available to users with `Owner` or `Manager` roles.

> **Note:** After creating a user, you must generate a password reset link for them using the `reset-password` command.

---

### 🔁 Reset Password (Admin Only)

```bash
palmyra-pro reset-password -e <EMAIL>
```

Generates a magic link to reset the password of the specified user.  
Prints the link to the terminal.

---

### 🚪 Logout

```bash
palmyra-pro logout
```

Deletes the saved token from your system.

---

## 🧪 Example Usage Flow

```bash
palmyra-pro --env staging
palmyra-pro login -e <EMAIL> -p adminpass
palmyra-pro sign-up -n "New User" -e <EMAIL>
palmyra-pro reset-password -e <EMAIL>
palmyra-pro whoami
palmyra-pro logout
```

---

## 🛠️ Developer Notes

- Config is stored in: `~/.palmyra-pro/config.json`
- Use `--env` once to persist environment
- Use quotes around multi-word values: `--name "Ali Bektash"`
