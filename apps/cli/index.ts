#!/usr/bin/env node

import { Command } from "commander"
import fetch from "node-fetch"
import { jwtDecode } from "jwt-decode"
import fs from "fs"
import os from "os"
import path from "path"
import chalk from "chalk"
import crypto from "crypto"
import { createAuthClient } from "better-auth/client"

export enum Role {
  Owner = "1",
  Manager = "2",
}

export type RoleType = (typeof Role)[keyof typeof Role]

const program = new Command()
const CONFIG_PATH = path.join(os.homedir(), ".palmyra-pro/config.json")

interface LoginResponse {
  token?: string
  [key: string]: unknown
}

const getToken = (): string | null => {
  if (process.env.PALMYRA_TOKEN) return process.env.PALMYRA_TOKEN
  if (!fs.existsSync(CONFIG_PATH)) return null

  try {
    const raw = fs.readFileSync(CONFIG_PATH, "utf8")
    const { token } = JSON.parse(raw)
    if (!token) return null

    if (token.split(".").length === 3) {
      const decoded = jwtDecode(token) as { exp?: number }
      const now = Math.floor(Date.now() / 1000)
      if (decoded.exp && decoded.exp < now) return null
    }

    return token
  } catch {
    return null
  }
}

const getEnv = (): string => {
  try {
    const raw = fs.readFileSync(CONFIG_PATH, "utf8")
    const config = JSON.parse(raw)
    return config.env || "local"
  } catch {
    return "local"
  }
}

const setEnv = (env: string) => {
  const config = { env }
  fs.mkdirSync(path.dirname(CONFIG_PATH), { recursive: true })
  fs.writeFileSync(CONFIG_PATH, JSON.stringify(config), "utf8")
  console.log(`✅ Environment set to: ${env}`)
  console.log(`Previous login session cleared. Please login again.`)
  process.exit(0)
}

const handleEnvFlag = () => {
  const index = process.argv.indexOf("--env")
  if (index !== -1) {
    const value = process.argv[index + 1]
    if (!value || value.startsWith("--")) {
      console.log(`📦 Current environment: ${getEnv()}`)
    } else {
      setEnv(value)
    }
    process.exit(0)
  }
}

handleEnvFlag()

const ENV = getEnv()

const resolveUrls = (env: string): { AUTH_URL: string; PUBLIC_URL: string } => {
  switch (env) {
    case "prod":
      return {
        AUTH_URL: "https://prod-nn-auth-430483576123.europe-west1.run.app",
        PUBLIC_URL: "https://naturesnectar.palmyra.pro",
      }
    case "local":
      return {
        AUTH_URL: "http://localhost:3000",
        PUBLIC_URL: "http://localhost:8080",
      }
    case "staging":
      return {
        AUTH_URL: "https://staging-nn-auth-463523546.asia-southeast1.run.app",
        PUBLIC_URL: "https://natures-nectar-dev.web.app",
      }
    default:
      return {
        AUTH_URL: `https://${env}-nn-auth-463523546.asia-southeast1.run.app`,
        PUBLIC_URL: `https://${env}-nn-fe-463523546.asia-southeast1.run.app`,
      }
  }
}

const { AUTH_URL, PUBLIC_URL } = resolveUrls(ENV)

export const authClient = createAuthClient({
  baseURL: AUTH_URL,
})

const generatePassword = (length = 9) => {
  return crypto.randomBytes(length).toString("base64").slice(0, length)
}

const getSession = async (): Promise<any> => {
  const token = getToken()
  if (!token) {
    console.error(
      "❌ You are not logged in. Please run `palmyra-pro login` first."
    )
    process.exit(1)
  }

  const response = await fetch(`${AUTH_URL}/api/auth/get-session`, {
    headers: { Authorization: `Bearer ${token}` },
  })

  if (!response.ok) {
    console.error(
      `❌ Server responded with ${response.status} - ${response.statusText}`
    )
    const text = await response.text()
    console.error(text)
    process.exit(1)
  }

  const text = await response.text()
  if (!text) {
    console.error("❌ Empty response from server.")
    process.exit(1)
  }

  try {
    return JSON.parse(text)
  } catch (err) {
    console.error("❌ Failed to parse server response as JSON.")
    console.error(text)
    process.exit(1)
  }
}

const saveToken = (token: string) => {
  const existing = fs.existsSync(CONFIG_PATH)
    ? JSON.parse(fs.readFileSync(CONFIG_PATH, "utf8"))
    : {}
  const updated = { ...existing, token }
  fs.mkdirSync(path.dirname(CONFIG_PATH), { recursive: true })
  fs.writeFileSync(CONFIG_PATH, JSON.stringify(updated), "utf8")
}

program.name("palmyra-pro").description("Palmyra Pro CLI tool").version("0.0.3")

program
  .command("login")
  .description("Login to your Palmyra Pro account")
  .requiredOption("-e, --email <string>", "Your email")
  .requiredOption("-p, --password <string>", "Your password")
  .action(async (options) => {
    const { email, password } = options

    try {
      const response = await fetch(`${AUTH_URL}/api/auth/sign-in/email`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, password }),
      })

      if (!response.ok) {
        const errText = await response.text()
        console.error(`❌ Login failed: ${errText}`)
        process.exit(1)
      }

      const data = (await response.json()) as LoginResponse
      const token = data.token
      if (!token) {
        console.error("❌ Login failed: No token received.")
        process.exit(1)
      }

      saveToken(token)
      console.log("✅ Login successful! Token saved.")
    } catch (error) {
      console.error("❌ Unexpected login error:", error)
      process.exit(1)
    }
  })

program
  .command("whoami")
  .description("Display current login info")
  .action(async () => {
    const user = await getSession()
    console.log("👤 Logged in as:")
    console.log(JSON.stringify(user, null, 2))
  })

program
  .command("sign-up")
  .description("Create a new user (admin-only)")
  .requiredOption("-n, --name <string>", "Name of the new user")
  .requiredOption("-e, --email <string>", "Email of the new user")
  .action(async (options) => {
    const token = getToken()
    if (!token) {
      console.error(
        "❌ You are not logged in. Please run `palmyra-pro login` first."
      )
      process.exit(1)
    }

    const session = await getSession()
    const roleId = session?.user?.roleId

    if (roleId !== Role.Owner && roleId !== Role.Manager) {
      console.error("❌ Unauthorized: Only admin users can create accounts.")
      process.exit(1)
    }

    const password = generatePassword(12)

    try {
      const response = await fetch(`${AUTH_URL}/api/auth/sign-up-with-cli`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          name: options.name,
          email: options.email,
          password,
          token,
        }),
      })

      if (!response.ok) {
        const errText = await response.text()
        console.error(
          `❌ Sign-up failed. Something went wrong on the server side: ${errText}`
        )
        process.exit(1)
      }

      console.log("✅ User successfully created.")
      console.log(`📩 Email address: ${options.email}`)
    } catch (err) {
      console.error("❌ Sign-up error:", err)
      process.exit(1)
    }
  })

program
  .command("logout")
  .description("Logout from your account")
  .action(() => {
    if (fs.existsSync(CONFIG_PATH)) fs.unlinkSync(CONFIG_PATH)
    console.log("✅ Logged out. Config file removed.")
  })

program
  .command("reset-password")
  .description("Send a magic link to reset password")
  .requiredOption("-e, --email <string>", "Your email")
  .action(async (options) => {
    const session = await getSession()
    const roleId = session?.user?.roleId

    if (roleId !== Role.Owner && roleId !== Role.Manager) {
      console.error(
        "❌ Unauthorized to reset password. You should be admin user"
      )
      process.exit(1)
    }

    const { email } = options

    const { data, error } = await authClient.forgetPassword({
      email,
    })

    if (data?.status) {
      console.log("\n✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨")
      console.log(
        chalk.bgYellowBright.bold(`🔮 Magic link has been sent to ${email}!`)
      )
      console.log("✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨")
    } else {
      console.log(
        `❌❌ We encountered an error in the magic link function (${error?.message}) ❌❌\n`,
        error
      )
    }
  })

if (!process.argv.slice(2).length) {
  program.outputHelp()
  process.exit(0)
}

program
  .parseAsync(process.argv)
  .then(() => process.exit(0))
  .catch((err) => {
    console.error("❌ CLI error:", err)
    process.exit(1)
  })
