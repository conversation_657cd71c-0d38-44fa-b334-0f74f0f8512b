{"name": "cli", "version": "1.0.0", "main": "index.js", "scripts": {"build": "tsc", "dev": "npx ts-node index.ts"}, "bin": {"palmyra-pro": "./dist/index.js"}, "files": ["dist"], "engines": {"node": ">=16.0.0"}, "private": true, "dependencies": {"better-auth": "^1.1.18", "chalk": "^5.3.0", "commander": "^13.1.0", "jwt-decode": "^4.0.0", "node-fetch": "^2.6.7", "yn": "^5.0.0"}, "devDependencies": {"@types/node": "^20.11.25", "@types/node-fetch": "^2.6.12", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}