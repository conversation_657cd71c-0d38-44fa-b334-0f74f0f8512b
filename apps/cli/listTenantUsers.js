import admin from "firebase-admin"
import { readFileSync } from "fs"

const serviceAccount = JSON.parse(readFileSync("./serviceAccount.json", "utf8"))

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
})

const [tenantId] = process.argv.slice(2)
if (!tenantId) {
  console.error("Usage: node listTenantUsers.js <tenantId>")
  process.exit(1)
}

async function listTenantUsers(tenantId) {
  try {
    const tenantAuth = admin.auth().tenantManager().authForTenant(tenantId)
    const users = await tenantAuth.listUsers()

    if (users.users.length === 0) {
      console.log("No users found for this tenant.")
    } else {
      users.users.forEach((user) => {
        console.log(`📧 ${user.email} — 🔑 uid: ${user.uid}`)
      })
    }
  } catch (error) {
    console.error("❌ Error while retrieving users:", error)
  }
}

listTenantUsers(tenantId)
