FROM node:22-alpine AS base

## Install turbo in this layer, so we don't need to build it again.
## Be careful, because it will stick on one version until forcing rebuild.
FROM base AS builder
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk update
RUN apk add --no-cache libc6-compat
WORKDIR /monorepo
RUN npm install turbo --global
COPY . .
RUN turbo prune auth --docker

# Add lockfile and package.json's of isolated subworkspace
FROM base AS installer
RUN apk update
RUN apk add --no-cache libc6-compat
WORKDIR /app

# First install dependencies (as they change less often)
# We will not need to rebuild this layer offen.
COPY --from=builder /monorepo/out/json/ .
RUN npm install

# Build the project and its dependencies
COPY --from=builder /monorepo/out/full/ .

RUN npm run build

# Build the final container.
FROM base AS runner
WORKDIR /app

# Don't run production as root
RUN addgroup --system --gid 1001 expressjs
RUN adduser --system --uid 1001 expressjs
USER expressjs
COPY --from=installer /app .

CMD node apps/auth/dist/index.cjs
