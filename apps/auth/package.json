{"name": "auth", "version": "0.0.0", "type": "module", "private": true, "scripts": {"start": "node dist/index.js", "dev": "tsup --watch --onSuccess \"node dist/index.cjs\"", "build": "tsup src/index.ts", "check-types": "tsc --noEmit", "lint": "eslint src/ --max-warnings 0", "test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "jest": {"preset": "@repo/jest-presets/node"}, "dependencies": {"@sendgrid/mail": "^8.1.5", "better-auth": "^1.1.18", "body-parser": "^1.20.3", "cors": "^2.8.5", "express": "^5.0.1", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "pg": "^8.13.3", "uuid": "^11.1.0", "yaml": "^2.7.0"}, "devDependencies": {"@jest/globals": "^29.7.0", "@repo/eslint-config": "*", "@repo/jest-presets": "*", "@repo/typescript-config": "*", "@types/body-parser": "^1.19.5", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/jsonwebtoken": "^9.0.8", "@types/morgan": "^1.9.9", "@types/node": "^22.12.0", "@types/pg": "^8.11.11", "@types/supertest": "^6.0.2", "eslint": "^9.20.0", "jest": "^29.7.0", "supertest": "^7.0.0", "tsup": "^8.4.0", "typescript": "5.7.3"}, "packageManager": "npm@10.9.2"}