import sgMail from "@sendgrid/mail"

if (!process.env.SENDGRID_API_KEY) {
  throw new Error("SENDGRID_API_KEY is not set in the environment variables")
}

sgMail.setApiKey(process.env.SENDGRID_API_KEY)

interface SendPasswordResetEmailOptions {
  to: string
  userName: string
  resetLink: string
}

export async function sendPasswordResetEmail({
  to,
  userName,
  resetLink,
}: SendPasswordResetEmailOptions): Promise<void> {
  const msg = {
    to,
    from: "Palmyra Pro <<EMAIL>>",
    templateId: "d-7046b6d263364fd9885139e2d60ffbdb",
    dynamic_template_data: {
      user_name: userName,
      reset_link: resetLink,
    },
  }

  try {
    await sgMail.send(msg)
    console.log(`Password reset email sent to ${to}`)
  } catch (error) {
    console.error("SendGrid error:", error)
    throw new Error("Failed to send password reset email")
  }
}
