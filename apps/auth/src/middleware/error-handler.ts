import { Request, Response, NextFunction } from "express"
import { components } from "@repo/api-specs"

type ErrorResponse = components["schemas"]["Error"]

interface OpenAPIValidationError extends Error {
  status?: number
  code?: string
  errors?: any[]
}

export const errorHandler = (
  err: OpenAPIValidationError,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  console.error(err)

  // Handle OpenAPI validation errors
  if (err.status === 400 && err.errors) {
    res.status(400).json({
      code: "VALIDATION_ERROR",
      message: "Request validation failed",
      errors: err.errors,
    } as ErrorResponse)
  }

  // Default error response
  const statusCode = err.status || 500
  const errorCode = err.code || "INTERNAL_ERROR"
  const message = err.message || "Internal Server Error"

  res.status(statusCode).json({
    code: errorCode,
    message: message,
  } as ErrorResponse)
}
