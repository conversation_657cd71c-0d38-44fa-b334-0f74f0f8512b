import { Response, NextFunction, Request } from "express"
import { betterAuth } from "better-auth"
import { bearer, jwt, openAPI } from "better-auth/plugins"
import { fromNodeHeaders, toNodeHandler } from "better-auth/node"
import pg from "pg"
import { randomUUID, scryptSync, timingSafeEqual } from "crypto"
import { sendPasswordResetEmail } from "../email"

export interface CustomRequest extends Request {
  session?: any
  user?: any
}

export enum Role {
  Owner = "1",
  Manager = "2",
}
export type RoleType = (typeof Role)[keyof typeof Role]

export const auth = betterAuth({
  baseURL: process.env.BETTER_AUTH_URL,
  secret: process.env.BETTER_AUTH_SECRET,
  trustedOrigins: ["*"],
  advanced: {
    defaultCookieAttributes: {
      sameSite: "none",
      secure: true,
    },
  },
  user: {
    additionalFields: {
      firstName: { type: "string" },
      lastName: { type: "string" },
      phone: { type: "string" },
      dob: { type: "string" },
      jobTitle: { type: "string" },
      isOnboarded: { type: "boolean" },
      location: { type: "string" },
      latitude: { type: "string" },
      longitude: { type: "string" },
      roleId: { type: "string" },
      employerId: { type: "string" },
    },
  },
  session: {
    cookieCache: {
      enabled: true,
      maxAge: 60,
    },
    expiresIn: 60 * 60 * 24 * 7, // 1 week
    updateAge: 60 * 60 * 24, // every 1 day the session expiration is updated
  },
  database: new pg.Pool({
    database: process.env.DB_NAME,
    host: process.env.DB_HOST,
    port: Number(process.env.DB_PORT) || 5432,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
  }),
  plugins: [
    jwt({
      jwt: {
        expirationTime: "5m",
      },
      jwks: {
        keyPairConfig: { alg: "EdDSA", crv: "Ed25519" },
      },
    }),
    bearer(),
    openAPI(),
  ],
  emailAndPassword: {
    enabled: true,
    disableSignUp: true,
    autoSignIn: true,
    minPasswordLength: 8,
    resetPasswordTokenExpiresIn: 60 * 60 * 24 * 4, // 4 days
    sendResetPassword: async ({ user, token }) => {
      await sendPasswordResetEmail({
        to: user.email,
        userName: user.name,
        resetLink: `${process.env.FE_URL}/reset-password/?token=${token}`,
      })
    },
  },
})

const handler = toNodeHandler(auth)

export const handleAuth = async (req: CustomRequest, res: Response) => {
  if (req.method === "POST" && req.url === "/api/auth/sign-up-with-cli") {
    try {
      const body = await new Promise<string>((resolve, reject) => {
        let data = ""
        req.on("data", (chunk) => (data += chunk))
        req.on("end", () => resolve(data))
        req.on("error", reject)
      })

      const { name, email, password, token: adminRoleToken } = JSON.parse(body)

      const adminSession = await auth.api.getSession({
        headers: fromNodeHeaders({
          ...req.headers,
          authorization: `Bearer ${adminRoleToken}`,
        }),
      })

      if (
        adminSession?.user.roleId !== Role.Owner &&
        adminSession?.user.roleId !== Role.Manager
      ) {
        res.writeHead(403, { "Content-Type": "application/json" })
        res.end(
          JSON.stringify({ error: "Unauthorized action: user is not an admin" })
        )
        return
      }

      if (!email || !password) {
        return res
          .status(400)
          .json({ error: "Name, Email and password are required" })
      }

      const ctx = await auth.$context
      const hash = await ctx.password.hash(password)
      const user = await ctx.internalAdapter.createUser({
        name,
        email,
        emailVerified: true,
      })

      await ctx.internalAdapter.createAccount({
        userId: user.id,
        providerId: "credential",
        accountId: randomUUID(),
        password: hash,
      })

      return res.status(200).json({ success: true })
    } catch (err) {
      console.error("❌ Sign up with CLI error:", err)
      return res.status(500).json({ error: "Internal server error" })
    }
  }

  return handler(req, res)
}

/**
 * Middleware to enrich the request object with session and user information.
 *
 * This middleware retrieves the session and user information from better-auth session if it exists
 * and attaches them to the request object. It is intended to be used in an Express.js application.
 *
 * @param req - The Express request object, extended with custom properties for session and user.
 * @param res - The Express response object.
 * @param next - The next middleware function in the stack.
 *
 * @returns A Promise that resolves when the session and user information have been attached to the request object.
 *
 * @example
 * // Usage in an Express.js route
 * app.use(enrichRequest);
 *
 * // Accessing the session and user information in a route handler
 * app.get('/some-route', (req, res) => {
 *   const session = req.session;
 *   const user = req.user;
 *   res.json({ session, user });
 * });
 */
export const enrichRequest = async (
  req: CustomRequest,
  res: Response,
  next: NextFunction
) => {
  const session = await auth.api.getSession({
    headers: fromNodeHeaders(req.headers),
  })
  req.session = session?.session
  req.user = session?.user
  next()
}

/**
 * Retrieves the current session and user information.
 *
 * @param req - The Express request object, extended with custom properties for session and user.
 * @param res - The Express response object.
 */
export const getSession = async (req: CustomRequest, res: Response) => {
  const session = req.session
  const user = req.session

  if (!user) {
    res.status(401).json({ error: "session not found. Is the user logged?" })
    return
  }

  res.status(200).json({ session, user })
}
