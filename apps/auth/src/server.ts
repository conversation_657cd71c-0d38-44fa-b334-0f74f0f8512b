import { json, urlencoded } from "body-parser"
import express, { type Express } from "express"
import morgan from "morgan"
import cors from "cors"
import { enrichRequest, getSession, handleAuth } from "./middleware/auth.ts"
import { errorHandler } from "./middleware/error-handler"

export const createServer = (): Express => {
  const app = express()

  app.use(morgan("combined")) // Apache compatible format
  app.use(
    cors({
      origin: (origin, callback) => {
        callback(null, true) // FIXME: Allows all origins
      },
      allowedHeaders: ["Content-Type", "Authorization"],
      methods: ["POST", "GET", "OPTIONS"],
      exposedHeaders: ["Content-Length"],
      maxAge: 600,
      credentials: true,
    })
  )

  // It must be registered before the json handler.
  // https://better-auth.vercel.app/docs/installation#mount-handler
  app.all("/api/auth/*splat", handleAuth)

  // Standard handlers and middlewares
  app
    .disable("x-powered-by")
    .use(urlencoded({ extended: true }))
    .use(json())

  // Auth
  app.use(enrichRequest)
  app.get("/session", getSession)

  app.get("/status", (req, res) => {
    res.status(200).json({ ok: true })
  })

  // Default 404
  app.use((_, res) => {
    res.status(404).json({ error: "Not Found" })
  })

  // Error handling (must be last)
  app.use(errorHandler)

  return app
}
