# api

## DEV

Before to start the service:
- Start the database as explained in [the database package documentation](../../packages/database/README.md)
- Set up the .env in the root of the project, as explain [the documentation](../../README.md)
- `npm run dev`

User in the dev seed:
- <EMAIL> / 1234567890
- <EMAIL> / 1234567890

## BetterAuth

OpenAPI UI: http://localhost:3000/api/auth/reference


How to test if it's working via CLI?

```shell
curl http://localhost:3000/api/auth/sign-in/email \
  --request POST \
  --header 'Content-Type: application/json' \
  --data '
{
  "email": "<EMAIL>",
  "password": "1234567890"
}'
```

And now, the signup is open, so:
```shell
curl http://localhost:3000/api/auth/sign-up/email \
  --request POST \
  --header 'Content-Type: application/json' \
  --data '{
  "name": "<PERSON>",
  "email": "angel<PERSON><PERSON><PERSON>@gmail.com",
  "password": "1234567890"
}'
```
