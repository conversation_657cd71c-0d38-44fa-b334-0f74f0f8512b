package main

import (
	"log"
	"net/http"
	"os"
	"palmyra-pro-api/pkg/api/routes"
	"palmyra-pro-api/pkg/db"
	"palmyra-pro-api/pkg/db/repository"
	"palmyra-pro-api/pkg/utils"
)

func main() {

	dbName := utils.GetEnvOrPanic("DB_NAME")
	dbHost := utils.GetEnvOrPanic("DB_HOST")
	dbPort := utils.GetEnvOrPanic("DB_PORT")
	dbUser := utils.GetEnvOrPanic("DB_USER")
	dbPassword := utils.GetEnvOrPanic("DB_PASSWORD")
	dbSSLMode := utils.GetEnvOrPanic("DB_SSL_MODE")

	connection, err := db.NewDB(&db.Config{
		Name:         dbName,
		Host:         dbHost,
		Port:         dbPort,
		User:         dbUser,
		Password:     dbPassword,
		SSLMode:      dbSSLMode,
		Debug:        false,
		MaxOpenConn:  0,
		MaxIdleConns: 0,
	})

	if err != nil {
		panic(err)
	}

	// create a type that satisfies the `api.ServerInterface`, which contains an implementation of every operation from the generated code
	server := routes.Server{
		FarmerRepo:                   repository.NewFarmerRepo(connection),
		UserRepo:                     repository.NewUserRepo(connection),
		RoleRepo:                     repository.NewRoleRepo(connection),
		AssetStatusRepo:              repository.NewAssetStatusRepo(connection),
		FormRepo:                     repository.NewFormRepo(connection),
		FormComplianceSubmissionRepo: repository.NewFormComplianceSubmissionRepo(connection),
		TransactionRepo:              repository.NewTransactionRepo(connection),
		HoneyProcessingRepo:          repository.NewHoneyProcessingRepo(connection),
		RegionRepo:                   repository.NewRegionRepository(connection),
		BucketRepo:                   repository.NewBucketRepo(connection),
	}

	http.HandleFunc("/run-harvest-summary", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodGet {
			http.Error(w, "Method not allowed, please use GET", http.StatusMethodNotAllowed)
			return
		}

		server.CreateHarvestSummaryReport(r.Context())
		w.WriteHeader(http.StatusAccepted)
		w.Write([]byte("Harvest summary report job triggered"))
	})

	// Start server
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	log.Println("Listening on port", port)
	log.Fatal(http.ListenAndServe(":"+port, nil))
}
