

## Tooling
- Using new `go tools`in GoLang 1.24: https://www.jvt.me/posts/2025/01/27/go-tools-124/
- OpenAPI
  - Code Generation: https://github.com/oapi-codegen/oapi-codegen
  - https://github.com/getkin/kin-openapi
  - Other alternatives:
    - https://goa.design/ : DSL to define microservices.
- Migration tool: https://github.com/amacneil/dbmate
- Query builder: https://bun.uptrace.dev
- JWT middleware:
  - https://github.com/lestrrat-go/jwx
  - https://github.com/getkin/kin-openapi/blob/master/openapi3filter/validate_request.go
  - AuthenticationFunc:
    - https://pkg.go.dev/github.com/getkin/kin-openapi/openapi3filter#pkg-types
    - https://www.reddit.com/r/golang/comments/1d58jzs/authentication_within_deepmapoapicodegen/
    - https://github.com/oapi-codegen/oapi-codegen/tree/main/examples/authenticated-api
    - https://www.better-auth.com/docs/plugins/jwt#verifying-the-token

## Verify JWT 
Ref: https://www.better-auth.com/docs/plugins/jwt#verifying-the-token

```
