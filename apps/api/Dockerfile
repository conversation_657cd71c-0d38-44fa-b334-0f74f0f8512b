# Use the official Golang image to build the Go application
FROM golang:1.24 AS builder

# Set the Current Working Directory inside the container
WORKDIR /working

# Copy the source from the current directory to the Working Directory inside the container
COPY . .

WORKDIR /working/apps/api

# Download all dependencies. Dependencies will be cached if the go.mod and go.sum files are not changed
RUN go mod download

# Build the Go app
RUN go generate generate.go
RUN go build -o main ./cmd/server

# Start a new stage from scratch
FROM gcr.io/distroless/base-debian12

# Set the Current Working Directory inside the container
WORKDIR /app

# Copy the Pre-built binary file from the previous stage
COPY --from=builder /working/apps/api/main .

# Expose port 3000 to the outside world
EXPOSE 3000

# Command to run the executable
CMD ["./main"]
