package routes

import (
	"context"
	"palmyra-pro-api/pkg/api"
	"palmyra-pro-api/pkg/utils"
	"sort"
	"strings"
	"time"
)

// Search batches
// (GET /batches)
func (s Server) SearchBatches(
	ctx context.Context,
	request api.SearchBatchesRequestObject,
) (api.SearchBatchesResponseObject, error) {
	dailySummaries, err := s.HoneyProcessingRepo.GetDailySummaries(ctx)

	if err != nil {
		return nil, err
	}

	batchListItems := map[string]api.BatchListItem{}
	// Map the daily summaries by batch code and calculate summaries by batch code
	for _, dailySummary := range dailySummaries {
		dailySummaryItem := api.DailySummaryItem{
			Date:                 dailySummary.Day,
			TotalWeight:          dailySummary.TotalWeight,
			NumberOfBuckets:      dailySummary.NumberOfBuckets,
			WaxProduced:          dailySummary.WaxProduced,
			EstimatedHoneyWeight: dailySummary.EstimatedHoneyWeight,
		}

		isProcessed := dailySummary.ProcessEndTS != nil

		if _, exists := batchListItems[dailySummary.BatchCode]; !exists {
			// Process a new batch list item
			batchListItems[dailySummary.BatchCode] = api.BatchListItem{
				BatchCode:      dailySummary.BatchCode,
				IsBlended:      utils.IsBatchCodeBlended(dailySummary.BatchCode),
				IsProcessed:    &isProcessed,
				TotalWeight:    dailySummary.TotalWeight,
				DateGenerated:  dailySummary.Day,
				DailySummaries: []api.DailySummaryItem{dailySummaryItem},
			}
		} else {
			// Update the existing batch list item
			batchItem := batchListItems[dailySummary.BatchCode]
			batchItem.TotalWeight += dailySummary.TotalWeight

			if utils.IsBatchCodeBlended(batchItem.BatchCode) {
				batchItem.IsBlended = true
			}

			if dailySummary.Day.Before(batchItem.DateGenerated) {
				batchItem.DateGenerated = dailySummary.Day
			}

			if !isProcessed {
				batchItem.IsProcessed = &isProcessed
			}

			batchItem.DailySummaries = append(batchItem.DailySummaries, dailySummaryItem)
			batchListItems[dailySummary.BatchCode] = batchItem
		}
	}

	// FIXME: This is a temporary implementation to filter and sort the batches.
	// If done in SQL with the current query, full batch data would not be returned
	// since the filter on daily summaries would be applied.
	// For example, if a batch has daily summaries for 1 Jan, 2 Jan, and 3 Jan,
	// and the date range is 2 Jan to 3 Jan, the batch would be missing 1 Jan
	// from the batch summary.
	// The proper way to handle this would be using a temporary database with
	// batch codes within a date range and using them to filter the daily summaries.

	// Convert the map to an array of BatchListItem
	results := make([]api.BatchListItem, 0, len(batchListItems))
	for _, batchListItem := range batchListItems {
		results = append(results, batchListItem)
	}

	// Sort the results based on the date generated
	sort.Slice(results, func(i, j int) bool {
		isDescending := request.Params.SortBy == nil ||
			api.SearchBatchesParamsSortByNewestFirst == *request.Params.SortBy

		if isDescending {
			return results[i].DateGenerated.After(results[j].DateGenerated)
		}
		return results[i].DateGenerated.Before(results[j].DateGenerated)
	})

	// Search by batch code
	if request.Params.Query != nil && *request.Params.Query != "" {
		originalResults := results // Create a copy of the original results
		results = results[:0]      // Clear the results slice
		for _, r := range originalResults {
			if strings.Contains(r.BatchCode, *request.Params.Query) {
				results = append(results, r)
			}
		}
	}

	// Filter by date range
	if request.Params.CreatedAtFrom != nil && request.Params.CreatedAtTo != nil {
		originalResults := results // Create a copy of the original results
		results = results[:0]      // Clear the results slice

		// Adjust CreatedAtTo to the end of the day
		createdAtToEndOfDay := request.Params.CreatedAtTo.Add(23*time.Hour + 59*time.Minute + 59*time.Second)

		for _, r := range originalResults {
			if r.DateGenerated.After(*request.Params.CreatedAtFrom) &&
				r.DateGenerated.Before(createdAtToEndOfDay) {
				results = append(results, r)
			}
		}
	}

	return api.SearchBatches200JSONResponse{
		Results:    results,
		Total:      nil, // Replace 'nil' with the appropriate value
		Page:       nil, // Replace 'nil' with the appropriate value
		PageSize:   nil, // Replace 'nil' with the appropriate value
		TotalPages: nil, // Replace 'nil' with the appropriate value
	}, nil
}

// Get summary about a batch
// (GET /batch/{id})
func (s Server) GetBatchByCode(
	ctx context.Context,
	request api.GetBatchByCodeRequestObject,
) (api.GetBatchByCodeResponseObject, error) {
	// Get Buckets
	buckets, err := s.BucketRepo.GetBucketsByBatchCode(
		ctx,
		request.BatchCode,
	)

	if err != nil {
		return nil, err
	}

	var apiBuckets []api.Bucket
	uniqueFarmers := make(map[string]api.Farmer)
	uniqueBeehives := make(map[string]api.BatchDetailsBeehives)
	totalBucketWeight := float32(0.0)
	var firstSaleDate *time.Time = nil

	for _, b := range buckets {
		totalBucketWeight += float32(b.Weight)
		if firstSaleDate == nil || b.HarvestDate.Before(*firstSaleDate) {
			firstSaleDate = &b.HarvestDate
		}

		apiBuckets = append(apiBuckets, api.Bucket{
			Id:              b.Id,
			BucketId:        b.BucketId,
			HarvestDate:     b.HarvestDate,
			HoneyType:       b.HoneyType,
			MoistureContent: b.MoistureContent,
			Weight:          b.Weight,
		})

		// Remove duplicates from farmers
		uniqueFarmers[b.Transaction.Farmer.ID] = api.Farmer{
			Id:        b.Transaction.Farmer.ID,
			FirstName: b.Transaction.Farmer.FirstName,
			LastName:  b.Transaction.Farmer.LastName,
		}

		// Remove duplicates from beehives
		for _, ta := range b.Transaction.Assets {
			uniqueBeehives[ta.AssetStatusId] = api.BatchDetailsBeehives{
				Id:        ta.AssetStatus.ID,
				Name:      ta.AssetStatus.Asset.Name,
				Latitude:  *ta.AssetStatus.Latitude,
				Longitude: *ta.AssetStatus.Longitude,
				Metadata:  ta.AssetStatus.Metadata,
			}
		}
	}

	farmers := make([]api.Farmer, 0, len(uniqueFarmers))
	for _, farmer := range uniqueFarmers {
		farmers = append(farmers, farmer)
	}

	beehives := make([]api.BatchDetailsBeehives, 0, len(uniqueBeehives))
	for _, beehive := range uniqueBeehives {
		beehives = append(beehives, beehive)
	}

	// Currently only Zambia is supported
	country := "Zambia"

	parts := strings.Split(request.BatchCode, "-")

	regionId := parts[0]
	zoneId := parts[1]

	region, err := s.RegionRepo.GetById(ctx, regionId)

	if err != nil {
		return nil, err
	}

	zone, err := s.RegionRepo.GetById(ctx, zoneId)

	if err != nil {
		return nil, err
	}

	processingSummary, err := s.HoneyProcessingRepo.GetSummaryByBatchCode(ctx, request.BatchCode)

	if err != nil {
		return nil, err
	}

	dailySummaries, err := s.HoneyProcessingRepo.GetDailySummariesByBatchCode(
		ctx,
		request.BatchCode,
	)

	dailySummaryItems := make([]api.DailySummaryItem, 0, len(dailySummaries))
	for _, dailySummary := range dailySummaries {
		dailySummaryItems = append(dailySummaryItems, api.DailySummaryItem{
			Date:                 dailySummary.Day,
			TotalWeight:          dailySummary.TotalWeight,
			NumberOfBuckets:      dailySummary.NumberOfBuckets,
			WaxProduced:          dailySummary.WaxProduced,
			EstimatedHoneyWeight: dailySummary.EstimatedHoneyWeight,
		})
	}

	if err != nil {
		return nil, err
	}

	return api.GetBatchByCode200JSONResponse{
		IsProcessed: processingSummary.IsProcessed,
		Sources: api.BatchDetailsSources{
			Farmers:  farmers,
			Country:  country,
			Region:   region.Name,
			Zone:     zone.Name,
			Beehives: beehives,
		},
		Buckets: api.BatchDetailsBuckets{
			TotalWeight:   totalBucketWeight,
			FirstSaleDate: *firstSaleDate,
			List:          apiBuckets,
		},
		Processing: api.BatchDetailsProcessing{
			FactoryLocation:    processingSummary.FactoryLocation,
			ProcessType:        processingSummary.ProcessType,
			StartProcessDate:   processingSummary.StartProcessDate,
			EndProcessDate:     processingSummary.EndProcessDate,
			FinalWeightOfHoney: processingSummary.FinalWeightOfHoney,
			FinalWeightOfWax:   processingSummary.FinalWeightOfWax,
			DailySummaries:     dailySummaryItems,
		},
	}, nil
}
