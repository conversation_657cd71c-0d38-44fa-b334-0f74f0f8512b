package routes

import (
	"testing"
	"time"
)

func Test_generateJWT(t *testing.T) {
	creation, err := time.Parse(time.DateTime, "2025-02-01 15:04:00")
	if err != nil {
		t.Fatal(err)
		return
	}

	expiration, err := time.Parse(time.DateTime, "2025-02-01 15:14:00")
	if err != nil {
		t.Fatal(err)
		return
	}

	payload := MetabasePayload{
		Resource: MetabaseResource{
			Dashboard: 10,
		},
		Params: map[string]any{},
		Exp:    expiration,
		Iat:    creation,
	}
	const privateKey = "c4973e9b830f1dabcf7d68e8ed48861963b8d94716a5b34b39e2ee8c1a354f20"
	const expected = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3Mzg0MjI4NDAsImlhdCI6MTczODQyMjI0MCwicGFyYW1zIjp7fSwicmVzb3VyY2UiOnsiZGFzaGJvYXJkIjoxMH19.9n8Ix0rXG5MRI3hM-caR9sZV_F2JEig_xHyTJXpRRr0"
	got, err := generateJWT(payload, privateKey)
	if err != nil {
		t.Errorf("generateJWT() error = %v", err)
		return
	}
	if got != expected {
		t.Errorf("generateJWT() got = %v, want %v", got, expected)
	}
}
