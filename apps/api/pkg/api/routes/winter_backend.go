package routes

import (
	"context"
	"fmt"
	"log"
	client "palmyra-pro-api/pkg/client/winter_backend"
	"palmyra-pro-api/pkg/db/models"
	"palmyra-pro-api/pkg/utils"
	"time"
)

// Upload Honey Harvesting Summary Report to IPFS and tokenize it on Palmyra
// (GET /run-harvest-summary)
func (s Server) CreateHarvestSummaryReport(ctx context.Context) {
	tenantIdentifier := utils.GetEnvOrPanic("CARDANO_TENANT_IDENTIFIER")

	transactions, err := s.TransactionRepo.GetTransactionsNotOnChain(ctx)

	if err != nil {
		log.Println("Failed to submit daily harvest report:", err)
		return
	}

	// If no transactions found skip running the cron job
	if len(transactions) == 0 {
		log.Println("No transactions found to submit")
		return
	}

	log.Println("Found", len(transactions), "transactions to submit, uploading to blockchain")

	winterClient := client.NewWinterBackendClient()
	transactionModels := make([]models.Transaction, len(transactions))
	var transactionIds []string
	for i, transaction := range transactions {
		transactionIds = append(transactionIds, transaction.Id)
		transactionModels[i] = *transaction
	}
	ipfsResponse, err := winterClient.UploadHoneyHarvestingToIPFS(transactionModels)

	if err != nil {
		log.Println("Failed to upload transactions to IPFS:", err)
		return
	}

	palmyraResponse, err := winterClient.PalmyraTokenizeCommodity(ipfsResponse.Hash, fmt.Sprintf("%s_HONEYHARV", tenantIdentifier))

	if err != nil {
		log.Println("Failed to upload transactions to Palmyra:", err)
		return
	}

	var checkResponse *client.CheckResponse
	// Since it takes time for the transaction to be processed, we might need to check the transaction ID multiple times
	attempts := 0
	for attempts < 5 {
		checkResponse, err = winterClient.CheckTransactionById(palmyraResponse.ID)

		if err != nil {
			log.Println("Failed to check transaction by ID:", err)
			return
		}

		if checkResponse == nil || checkResponse.Status != "SUCCESS" {
			attempts++
			time.Sleep(1 * time.Minute)
		} else {
			break
		}
	}

	if checkResponse.Status != "SUCCESS" {
		log.Println("Failed to get transaction ID after 5 attempts")
		return
	}

	if err != nil {
		log.Println("Failed to check transaction by ID:", err)
		return
	}

	err = s.TransactionRepo.UpdateTransactionsWithHashes(ctx, transactionIds, ipfsResponse.Hash, checkResponse.TxID)

	if err != nil {
		log.Println("Failed to update DB", err)
		return
	}

	fmt.Println(len(transactionIds), " honey harvesting transactions uploaded on blockchain")
}
