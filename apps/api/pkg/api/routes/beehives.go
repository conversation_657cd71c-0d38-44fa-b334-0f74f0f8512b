package routes

import (
	"context"
	. "palmyra-pro-api/pkg/api"
	"palmyra-pro-api/pkg/db/models"
)

func dbAssetStatusToBeehive(assetStatus models.AssetStatus) (Beehive, error) {
	isFunctional := false
	if isFunctionalAny, found := assetStatus.Metadata[models.CntBeehiveIsFunctional]; found && isFunctionalAny != nil {
		isFunctional = isFunctionalAny.(bool)
	}

	isOccupied := false
	if isOccupiedAny, found := assetStatus.Metadata[models.CntBeehiveOccupied]; found && isOccupiedAny != nil {
		isOccupied = isOccupiedAny.(bool)
	}

	needsToBeRepaired := false
	if needsToBeRepairedAny, found := assetStatus.Metadata[models.CntBeehiveToBeRepaired]; found && needsToBeRepairedAny != nil {
		needsToBeRepaired = needsToBeRepairedAny.(bool)
	}

	observations := ""
	if observationsAny, found := assetStatus.Metadata[models.CntBeehiveObservations]; found && observationsAny != nil {
		observations = observationsAny.(string)
	}

	return Beehive{
		FarmerId:          assetStatus.FarmerId,
		Id:                assetStatus.ID,
		IsFunctional:      isFunctional,
		IsOccupied:        isOccupied,
		Latitude:          *assetStatus.Latitude,
		Longitude:         *assetStatus.Longitude,
		Name:              assetStatus.Asset.Name,
		NeedsToBeRepaired: needsToBeRepaired,
		Observations:      &observations,
		Status:            assetStatus.Status,
	}, nil
}

// Get beehives
// (GET /beehives)
func (s Server) GetBeehives(ctx context.Context, request GetBeehivesRequestObject) (GetBeehivesResponseObject, error) {
	status := "active"
	assetsStatus, err := s.AssetStatusRepo.Search(
		ctx,
		request.Params.Query,
		request.Params.FarmerId,
		&status,
		map[string]interface{}{"assetTypeId": "1"},
		0, 0,
	)
	if err != nil {
		return nil, err
	}

	results := []Beehive{}
	for _, assetStatus := range assetsStatus {
		beehive, err := dbAssetStatusToBeehive(*assetStatus)
		if err != nil {
			return nil, err
		}
		results = append(results, beehive)
	}

	response := BeehiveList{
		Page:       nil,
		PageSize:   nil,
		Results:    &results,
		Total:      nil,
		TotalPages: nil,
	}

	return GetBeehives200JSONResponse(response), nil
}

// Create a new beehive
// (POST /beehives)
func (s Server) CreateBeehive(ctx context.Context, request CreateBeehiveRequestObject) (CreateBeehiveResponseObject, error) {
	assetTypeId := "1"
	businessId := "1"

	asset := models.Asset{
		Name:        request.Body.Name,
		AssetTypeID: &assetTypeId,
		BusinessID:  &businessId,
		CreatedBy:   nil,
	}

	assetStatus := models.AssetStatus{
		Status:    request.Body.Status,
		Latitude:  &request.Body.Latitude,
		Longitude: &request.Body.Longitude,
		Metadata: map[string]interface{}{
			models.CntBeehiveIsFunctional: request.Body.IsFunctional,
			models.CntBeehiveToBeRepaired: request.Body.NeedsToBeRepaired,
			models.CntBeehiveObservations: request.Body.Observations,
			models.CntBeehiveOccupied:     request.Body.IsOccupied,
		},
		FarmerId:  request.Body.FarmerId,
		CreatedBy: nil,
		Asset:     &asset,
	}

	id, err := s.AssetStatusRepo.CreateAssetStatus(ctx, assetStatus)
	if err != nil {
		return nil, err
	}

	return CreateBeehive201JSONResponse{Id: &id}, nil

}

// Soft delete Beehive by ID
// (DELETE /beehives/{id})
func (s Server) DeleteBeehiveById(ctx context.Context, request DeleteBeehiveByIdRequestObject) (DeleteBeehiveByIdResponseObject, error) {
	currentValue, err := s.AssetStatusRepo.GetAssetStatusByID(ctx, request.Id)
	if err != nil {
		return nil, err
	}
	if currentValue == nil {
		return DeleteBeehiveById404JSONResponse{
			Code:    "NOT_FOUND",
			Message: "Beehive not found",
		}, nil
	}

	err = s.AssetStatusRepo.DeleteAssetStatus(ctx, request.Id)
	if err != nil {
		return nil, err
	}

	return DeleteBeehiveById204Response{}, nil
}

// Get Beehive by ID
// (GET /beehives/{id})
func (s Server) GetBeehiveById(ctx context.Context, request GetBeehiveByIdRequestObject) (GetBeehiveByIdResponseObject, error) {
	assetStatus, err := s.AssetStatusRepo.GetAssetStatusByID(ctx, request.Id)
	if err != nil {
		return nil, err
	}

	if assetStatus == nil {
		return GetBeehiveById404JSONResponse{
			Code:    "NOT_FOUND",
			Message: "Beehive not found",
		}, nil
	}

	response, err := dbAssetStatusToBeehive(*assetStatus)
	if err != nil {
		return nil, err
	}

	return GetBeehiveById200JSONResponse(response), nil
}

// Update a existing Beehive
// (POST /beehives/{id})
func (s Server) UpdateBeehive(ctx context.Context, request UpdateBeehiveRequestObject) (UpdateBeehiveResponseObject, error) {

	currentValue, err := s.AssetStatusRepo.GetAssetStatusByID(ctx, request.Id)
	if err != nil {
		return nil, err
	}
	if currentValue == nil {
		return UpdateBeehive404JSONResponse{
			Code:    "NOT_FOUND",
			Message: "Beehive not found",
		}, nil
	}

	currentValue.Asset.Name = request.Body.Name
	currentValue.Status = request.Body.Status
	currentValue.Metadata[models.CntBeehiveIsFunctional] = request.Body.IsFunctional
	currentValue.Metadata[models.CntBeehiveOccupied] = request.Body.IsOccupied
	currentValue.Metadata[models.CntBeehiveObservations] = request.Body.Observations
	currentValue.Metadata[models.CntBeehiveToBeRepaired] = request.Body.NeedsToBeRepaired
	currentValue.Status = request.Body.Status
	currentValue.Latitude = &request.Body.Latitude
	currentValue.Longitude = &request.Body.Longitude

	err = s.AssetStatusRepo.UpdateAssetStatus(ctx, *currentValue)
	if err != nil {
		return nil, err
	}

	return UpdateBeehive200JSONResponse{}, nil
}
