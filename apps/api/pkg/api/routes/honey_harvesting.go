package routes

import (
	"context"
	"fmt"
	"time"

	api "palmyra-pro-api/pkg/api"
	"palmyra-pro-api/pkg/db/models"

	"github.com/spf13/cast"
)

// CreateHoneyHarvesting creates a new honey harvesting record
// (POST /honey-harvesting)
func (s Server) CreateHoneyHarvesting(
	ctx context.Context,
	request api.CreateHoneyHarvestingRequestObject,
) (api.CreateHoneyHarvestingResponseObject, error) {
	now := time.Now()

	transaction := models.Transaction{
		CreatedAt:  now,
		UpdatedAt:  now,
		FarmerID:   request.Body.FarmerId,
		SaleDate:   request.Body.SaleDate,
		PricePerKg: float64(request.Body.PricePerKg),
		RegionId:   request.Body.RegionId,
		ChiefdomId: request.Body.ChiefdomId,
		ZoneId:     request.Body.ZoneId,
		VillageId:  request.Body.VillageId,
		// TODO: Should get from JWT token
		CreatedBy: request.Body.CreatedBy,
	}

	buckets := make([]*models.Bucket, len(request.Body.Buckets))
	for i, bucketReq := range request.Body.Buckets {
		buckets[i] = &models.Bucket{
			CreatedAt:       now,
			UpdatedAt:       now,
			HarvestDate:     bucketReq.HarvestDate,
			MoistureContent: bucketReq.MoistureContent,
			Weight:          bucketReq.Weight,
			BucketId:        bucketReq.BucketId,
			HoneyType:       string(bucketReq.HoneyType),
		}
	}

	transactionAssets := make([]*models.TransactionAsset, len(request.Body.Beehives))
	for i, beehive := range request.Body.Beehives {
		transactionAssets[i] = &models.TransactionAsset{
			AssetStatusId: beehive.(string),
		}
	}

	transactionId, transactionErr := s.TransactionRepo.Create(
		ctx,
		&transaction,
		buckets,
		transactionAssets,
	)

	if transactionErr != nil {
		return nil, transactionErr
	}

	return api.CreateHoneyHarvesting201JSONResponse{
		Id: &transactionId,
	}, nil
}

func (s Server) GetHoneyHarvestingById(
	ctx context.Context,
	request api.GetHoneyHarvestingByIdRequestObject,
) (api.GetHoneyHarvestingByIdResponseObject, error) {
	transaction, err := s.TransactionRepo.GetById(ctx, request.Id)
	if err != nil {
		return nil, err
	}

	buckets := make([]api.Bucket, len(transaction.Buckets))
	for i, bucket := range transaction.Buckets {
		buckets[i] = api.Bucket{
			BucketId:        bucket.BucketId,
			HarvestDate:     bucket.HarvestDate,
			MoistureContent: float32(bucket.MoistureContent),
			Weight:          float32(bucket.Weight),
			HoneyType:       bucket.HoneyType,
		}
	}

	beehives := make([]string, len(transaction.Assets))
	for i, asset := range transaction.Assets {
		beehives[i] = asset.AssetStatusId
	}

	return api.GetHoneyHarvestingById200JSONResponse{
		RegionId:   transaction.RegionId,
		ChiefdomId: transaction.ChiefdomId,
		ZoneId:     transaction.ZoneId,
		VillageId:  transaction.VillageId,
		Beehives:   beehives,
		Buckets:    buckets,
		CreatedAt:  &transaction.CreatedAt,
		CreatedBy: api.HoneyHarvestingCreatedBy{
			Id:   transaction.User.ID,
			Name: transaction.User.FirstName + " " + transaction.User.LastName,
		},
		Farmer: api.HoneyHarvestingFarmer{
			Id:   transaction.Farmer.ID,
			Name: transaction.Farmer.FirstName + " " + transaction.Farmer.LastName,
		},
		Id:          transaction.Id,
		PricePerKg:  float32(transaction.PricePerKg),
		SaleDate:    transaction.SaleDate,
		IpfsHash:    &transaction.IPFSHash,
		PalmyraHash: &transaction.PalmyraHash,
	}, nil
}

// SearchHoneyHarvestingTransactions searches for honey harvesting transactions based on the provided filters
// (GET /honey-harvesting)
func (s Server) SearchHoneyHarvestingTransactions(
	ctx context.Context,
	request api.SearchHoneyHarvestingTransactionsRequestObject,
) (api.SearchHoneyHarvestingTransactionsResponseObject, error) {
	pageSize := cast.ToInt(request.Params.PageSize)

	isDescending := request.Params.SortBy == nil ||
		api.SearchHoneyHarvestingTransactionsParamsSortByNewestFirst == *request.Params.SortBy

	transactions, err := s.TransactionRepo.QuickSearch(
		ctx,
		cast.ToString(request.Params.Query),
		request.Params.CreatedAtFrom,
		request.Params.CreatedAtTo,
		isDescending,
		cast.ToInt(request.Params.Page)*pageSize,
		pageSize,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to fetch transaction data: %w", err)
	}

	results := []api.HoneyHarvestingSearchResult{}
	for _, transaction := range transactions {
		moistureRange := fmt.Sprintf(
			"%.1f~%.1f%%",
			transaction.MinMoistureContent,
			transaction.MaxMoistureContent,
		)

		result := api.HoneyHarvestingSearchResult{
			AmountPaid:    float32(transaction.PricePerKg * transaction.TotalWeight),
			BucketCount:   transaction.BucketCount,
			CreatedAt:     transaction.CreatedAt,
			HoneyType:     api.HoneyHarvestingSearchResultHoneyType(transaction.HoneyType),
			Id:            transaction.Id,
			MoistureRange: &moistureRange,
			Region:        &transaction.RegionName,
			Chiefdom:      &transaction.ChiefdomName,
			Zone:          &transaction.ZoneName,
			Village:       &transaction.VillageName,
			PricePerKg:    float32(transaction.PricePerKg),
			TotalWeight:   float32(transaction.TotalWeight),
			FarmerName:    transaction.FarmerName + " " + transaction.FarmerLastName,
		}
		results = append(results, result)
	}

	return api.SearchHoneyHarvestingTransactions200JSONResponse{
		Page:       nil,
		PageSize:   nil,
		Results:    &results,
		Total:      nil,
		TotalPages: nil,
	}, nil
}
