package routes

import (
	"context"
	"fmt"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	. "palmyra-pro-api/pkg/api"
	"time"
)

type MetabaseResource struct {
	Dashboard int `json:"dashboard"`
}

type MetabasePayload struct {
	Resource MetabaseResource `json:"resource"`
	Params   map[string]any   `json:"params"`
	Exp      time.Time        `json:"exp"`
	Iat      time.Time        `json:"iat"`
}

func generateJWT(payload MetabasePayload, privateKey string) (string, error) {
	claims := jwt.MapClaims{
		"resource": payload.Resource,
		"params":   payload.Params,
		"exp":      payload.Exp.UnixMilli() / 1000,
		"iat":      payload.Iat.UnixMilli() / 1000,
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	return token.SignedString([]byte(privateKey))
}

// Generate a new dashboard link
// (GET /dashboard-links)
func (s Server) CreateDashboardLink(ctx context.Context, request CreateDashboardLinkRequestObject) (CreateDashboardLinkResponseObject, error) {

	metabaseSecret := s.MetabasePrivateKey
	metabaseURL := s.MetabaseURL

	now := time.Now()
	expirationTime := now.Add(10 * time.Minute)

	payload := MetabasePayload{
		Resource: MetabaseResource{
			Dashboard: request.Id,
		},
		Params: map[string]any{},
		Exp:    expirationTime,
		Iat:    now,
	}

	tokenString, err := generateJWT(payload, metabaseSecret)
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf(
		"%s/embed/dashboard/%s", // #bordered=true&titled=true
		metabaseURL, tokenString,
	)
	dashboard := DashboardLink{
		Id:          uuid.New(),
		CreatedAt:   now,
		ExpiresAt:   expirationTime,
		DashboardId: request.Id,
		Url:         url,
	}

	return CreateDashboardLink201JSONResponse(dashboard), nil
}
