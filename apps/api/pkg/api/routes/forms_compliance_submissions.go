package routes

import (
	"context"
	. "palmyra-pro-api/pkg/api"
	"palmyra-pro-api/pkg/db/models"
	"palmyra-pro-api/pkg/utils"
	"time"

	"github.com/mitchellh/mapstructure"
	"github.com/spf13/cast"
)

// CreateFormsComplianceSubmissions creates a new form
// (POST /forms/compliance-submissions)
func (s Server) CreateFormsComplianceSubmissions(ctx context.Context, request CreateFormsComplianceSubmissionsRequestObject) (CreateFormsComplianceSubmissionsResponseObject, error) {
	metadata, err := utils.StructToMap(request.Body)
	if err != nil {
		return nil, err
	}

	now := time.Now()

	form := models.Form{
		Name:        "compliance-submission",
		Description: nil,
		Metadata:    metadata,
		BusinessID:  "1",                                // TODO: get from auth
		CreatedBy:   "817Hs1WZ9PwCkeRwipPOINTDIeYN15fq", // TODO: get from auth
		CreatedAt:   now,
		UpdatedAt:   now,
	}

	id, err := s.FormRepo.CreateForm(ctx, &form)
	if err != nil {
		return nil, err
	}

	return CreateFormsComplianceSubmissions201JSONResponse{
		Id: &id,
	}, nil
}

// SearchFormsComplianceSubmissions search compliance form submissions
// (GET /forms/compliance-submissions)
func (s Server) SearchFormsComplianceSubmissions(
	ctx context.Context,
	request SearchFormsComplianceSubmissionsRequestObject,
) (SearchFormsComplianceSubmissionsResponseObject, error) {
	pageSize := cast.ToInt(request.Params.PageSize)

	isDescending := request.Params.SortBy == nil ||
		SearchFormsComplianceSubmissionsParamsSortByNewestFirst == *request.Params.SortBy

	forms, err := s.FormComplianceSubmissionRepo.QuickSearchForms(
		ctx,
		cast.ToString(request.Params.Query),
		request.Params.CreatedAtFrom, request.Params.CreatedAtTo,
		isDescending,
		cast.ToInt(request.Params.Page)*pageSize,
		pageSize,
	)

	if err != nil {
		return nil, err
	}

	results := []FormsComplianceSubmissions{}
	for _, form := range forms {

		var formMetadata CreateFormsComplianceSubmissionsJSONBody
		err = mapstructure.Decode(form.Metadata, &formMetadata)
		if err != nil {
			return nil, err
		}

		// TODO: Retrieve information about the user that created the form
		creatorInfo := FormsComplianceSubmissionsCreatedBy{
			Id:   form.CreatedById,
			Name: form.CreatedByName,
		}

		// TODO: Retrieve Farmer Info.
		farmerInfo := FormsComplianceSubmissionsFarmer{
			Id:      formMetadata.FarmerId,
			Name:    form.FarmerName,
			ZoneId:  form.FarmerZoneName,
			Village: form.FarmerVillageName,
			Gender:  form.FarmerGender,
		}

		results = append(results, FormsComplianceSubmissions{
			Beehives:      formMetadata.Beehives,
			ManagerInputs: formMetadata.ManagerInputs,
			CreatedBy:     creatorInfo,
			Farmer:        farmerInfo,
			Id:            form.ID,
			Name:          form.Name,
			CreatedAt:     form.CreatedAt,
			UpdatedAt:     form.UpdatedAt,
		})
	}

	return SearchFormsComplianceSubmissions200JSONResponse{
		Page:       nil,
		PageSize:   nil,
		Results:    &results,
		Total:      nil,
		TotalPages: nil,
	}, nil
}

// GetFormsComplianceSubmissionById gets form by ID
// (GET /forms/compliance-submissions/{id})
func (s Server) GetFormsComplianceSubmissionById(ctx context.Context, request GetFormsComplianceSubmissionByIdRequestObject) (GetFormsComplianceSubmissionByIdResponseObject, error) {
	form, err := s.FormRepo.GetFormByID(ctx, request.Id)
	if err != nil {
		return nil, err
	}

	var formMetadata CreateFormsComplianceSubmissionsJSONBody
	err = mapstructure.Decode(form.Metadata, &formMetadata)
	if err != nil {
		return nil, err
	}

	farmer, err := s.FarmerRepo.GetFarmerByID(ctx, formMetadata.FarmerId)
	if err != nil {
		return nil, err
	}

	farmerInfo := FormsComplianceSubmissionsFarmer{
		Id:     formMetadata.FarmerId,
		Name:   farmer.FirstName + " " + farmer.LastName,
		ZoneId: farmer.ZoneID,
		Gender: farmer.Gender,
	}

	creatorInfo := FormsComplianceSubmissionsCreatedBy{
		Id:   form.CreatedBy,
		Name: form.CreatedByUser.FirstName + " " + form.CreatedByUser.LastName,
	}

	return GetFormsComplianceSubmissionById200JSONResponse{
		Beehives:      formMetadata.Beehives,
		Farmer:        farmerInfo,
		ManagerInputs: formMetadata.ManagerInputs,
		Id:            form.ID,
		Name:          form.Name,
		UpdatedAt:     form.UpdatedAt,
		CreatedBy:     creatorInfo,
		CreatedAt:     form.CreatedAt,
	}, nil
}
