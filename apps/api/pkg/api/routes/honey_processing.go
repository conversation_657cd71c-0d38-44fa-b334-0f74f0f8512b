package routes

import (
	"context"
	"fmt"
	api "palmyra-pro-api/pkg/api"
	"palmyra-pro-api/pkg/db/models"
	"palmyra-pro-api/pkg/db/repository"
	"palmyra-pro-api/pkg/utils"

	"github.com/spf13/cast"
)

// CreateHoneyProcessing creates a new honey harvesting record
// (POST /honey-harvesting)
func (s Server) CreateHoneyProcessingLog(
	ctx context.Context,
	request api.CreateHoneyProcessingLogRequestObject,
) (api.CreateHoneyProcessingLogResponseObject, error) {
	processingLog := models.HoneyProcessingLog{
		FactoryID:          request.Body.FactorId,
		ProcessTypeID:      request.Body.ProcessTypeId,
		FactoryStaffLeadID: request.Body.FactoryStaffLeadId,
		ProcessStartTS:     request.Body.ProcessStartDate,
		ProcessEndTS:       request.Body.ProcessEndDate,
	}

	// Since all buckets should be from the same batch, we can just take the first one
	if len(request.Body.Buckets) == 0 {
		return nil, fmt.Errorf("failed to fetch buckets for honey processing log")
	}
	bucketId := request.Body.Buckets[0]

	bucket, err := s.BucketRepo.GetBucketById(ctx, bucketId)

	if err != nil {
		return nil, err
	}

	processingLog.BatchCode = utils.GenerateBatchCode(
		bucket.HoneyType,
		bucket.HarvestDate.Year(),
		bucket.Transaction.RegionId,
		&bucket.Transaction.ZoneId,
	)

	if request.Body.HoneyKg != nil {
		processingLog.HoneyWeight = *request.Body.HoneyKg
	}

	if request.Body.CentrifugeTemperature != nil {
		processingLog.CentrifugeTemperature = *request.Body.CentrifugeTemperature
	}

	if request.Body.CentrifugeSpeed != nil {
		processingLog.CentrifugeSpeed = *request.Body.CentrifugeSpeed
	}

	if request.Body.HoneyTemperature != nil {
		processingLog.HoneyTemperature = *request.Body.HoneyTemperature
	}

	if request.Body.MeshSize != nil {
		processingLog.MeshSize = *request.Body.MeshSize
	}

	if request.Body.PumpHopper != nil {
		processingLog.PumpHopper = *request.Body.PumpHopper
	}

	if request.Body.WaxKg != nil {
		processingLog.WaxWeight = *request.Body.WaxKg
	}

	bucketIds := []string{}
	for _, bucketId := range request.Body.Buckets {
		bucketIds = append(bucketIds, bucketId)
	}

	id, err := s.HoneyProcessingRepo.Create(ctx, &processingLog, bucketIds)
	if err != nil {
		return nil, err
	}

	return api.CreateHoneyProcessingLog201JSONResponse{
		Id:        id,
		BatchCode: processingLog.BatchCode,
	}, nil

}

func detailsModelToResponse(honeyLog models.HoneyProcessingLog) api.HoneyProcessingLogDetails {
	// Get the first transaction (location should be the same for all buckets)
	transaction := honeyLog.Buckets[0].Bucket.Transaction

	buckets := []api.HoneyHarvestingDetailsBucket{}
	for _, bucket := range honeyLog.Buckets {
		b := bucket.Bucket
		buckets = append(buckets, api.HoneyHarvestingDetailsBucket{
			Id:              &b.Id,
			BucketId:        b.BucketId,
			HarvestDate:     b.HarvestDate,
			HoneyType:       b.HoneyType,
			MoistureContent: b.MoistureContent,
			Weight:          b.Weight,
			VerifiedWeight:  b.VerifiedWeight,
			ContaminationStatus: (*api.HoneyHarvestingDetailsBucketContaminationStatus)(&b.ContaminationStatus),
		})
	}

	return api.HoneyProcessingLogDetails{
		Id:        honeyLog.ID,
		BatchCode: honeyLog.BatchCode,
		Buckets:   buckets,
		Factory: &api.HoneyHarvestingDetailsFactory{
			Id:   &honeyLog.Factory.ID,
			Name: &honeyLog.Factory.Name,
		},
		FactoryStaffLead: &api.HoneyHarvestingDetailsFactoryStaffLead{
			Id:   &honeyLog.FactoryStaffLead.ID,
			Name: &honeyLog.FactoryStaffLead.Name,
		},
		HoneyKg:               honeyLog.HoneyWeight,
		WaxKg:                 honeyLog.WaxWeight,
		CentrifugeTemperature: honeyLog.CentrifugeTemperature,
		CentrifugeSpeed:       honeyLog.CentrifugeSpeed,
		HoneyTemperature:      honeyLog.HoneyTemperature,
		MeshSize:              honeyLog.MeshSize,
		PumpHopper:            honeyLog.PumpHopper,
		ProcessStartDate:      honeyLog.ProcessStartTS,
		ProcessEndDate:        honeyLog.ProcessEndTS,
		ProcessType: &api.HoneyHarvestingDetailsProcessType{
			Id:   &honeyLog.ProcessType.ID,
			Name: &honeyLog.ProcessType.Name,
		},
		RegionId:   transaction.RegionId,
		ChiefdomId: transaction.ChiefdomId,
		ZoneId:     transaction.ZoneId,
		VillageId:  transaction.VillageId,
	}
}

func (s Server) GetHoneyProcessingById(
	ctx context.Context,
	request api.GetHoneyProcessingByIdRequestObject,
) (api.GetHoneyProcessingByIdResponseObject, error) {

	honeyLog, err := s.HoneyProcessingRepo.GetById(ctx, request.Id)
	if err != nil {
		return nil, err
	}

	if honeyLog == nil {
		return api.GetHoneyProcessingById404JSONResponse{
			Code:    "NOT_FOUND",
			Message: fmt.Sprintf("honey processing log [%s] not found", request.Id),
		}, nil
	}

	honeyDetails := detailsModelToResponse(*honeyLog)

	return api.GetHoneyProcessingById200JSONResponse(honeyDetails), nil

}

// Search honey processing logs
// (GET /honey-processing)
func (s Server) SearchHoneyProcessingLog(
	ctx context.Context,
	request api.SearchHoneyProcessingLogRequestObject,
) (api.SearchHoneyProcessingLogResponseObject, error) {
	pageSize := cast.ToInt(request.Params.PageSize)

	isDescending := request.Params.SortBy == nil || api.SearchHoneyProcessingLogParamsSortBy(api.SearchBatchesParamsSortByNewestFirst) == *request.Params.SortBy
	onlyOpen := false
	if request.Params.OnlyOpen != nil {
		onlyOpen = *request.Params.OnlyOpen
	}

	honeyLogs, err := s.HoneyProcessingRepo.QuickSearch(
		ctx,
		cast.ToString(request.Params.Query),
		request.Params.ProcessStartTSFrom, request.Params.ProcessStartTSTo,
		onlyOpen,
		isDescending,
		cast.ToInt(request.Params.Page)*pageSize,
		pageSize,
	)

	if err != nil {
		return nil, err
	}

	results := []api.HoneyProcessingLogDetails{}
	for _, honeyLog := range honeyLogs {
		results = append(results, detailsModelToResponse(*honeyLog))
	}

	return api.SearchHoneyProcessingLog200JSONResponse{
		Page:       nil,
		PageSize:   nil,
		Results:    &results,
		Total:      nil,
		TotalPages: nil,
	}, nil
}

// Update the Create a new honey processing log
// (POST /honey-processing/{id})
func (s Server) UpdateHoneyProcessingLog(ctx context.Context, request api.UpdateHoneyProcessingLogRequestObject) (api.UpdateHoneyProcessingLogResponseObject, error) {

	buckets := map[string]repository.HoneyProcessingBucket{}
	for id, fields := range *request.Body.Buckets {
		var verifiedWeight float32
		if fields.VerifiedWeight != nil {
			verifiedWeight = *fields.VerifiedWeight
		}

		var contaminationStatus string
		if fields.ContaminationStatus != nil {
			contaminationStatus = string(*fields.ContaminationStatus)
		}

		buckets[id] = repository.HoneyProcessingBucket{
			VerifiedWeight:      verifiedWeight,
			ContaminationStatus: contaminationStatus,
		}
	}

	hpLog, err := s.HoneyProcessingRepo.Update(
		ctx,
		request.Id,
		request.Body.ProcessEndDate, request.Body.HoneyKg, request.Body.WaxKg, request.Body.CentrifugeTemperature, request.Body.CentrifugeSpeed, request.Body.HoneyTemperature, request.Body.MeshSize, request.Body.PumpHopper, buckets,
	)

	if err != nil {
		return nil, err
	}

	if hpLog == nil {
		return api.UpdateHoneyProcessingLog404JSONResponse{
			Code:    "NOT_FOUND",
			Message: fmt.Sprintf("honey processing log [%s] not found", request.Id),
		}, nil
	}

	return api.UpdateHoneyProcessingLog200JSONResponse{
		Id:        hpLog.ID,
		BatchCode: hpLog.BatchCode,
	}, nil
}
