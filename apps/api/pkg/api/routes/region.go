package routes

import (
	"context"
	"fmt"
	"palmyra-pro-api/pkg/api"
	"palmyra-pro-api/pkg/db/models"
)

// Helper function to map a region to API response
func regionToAPI(region models.Region) api.Region {
	return api.Region{
		Id:   &region.Id,
		Name: &region.Name,
	}
}

// Helper function to map regions to API response
func mapRegionsToResponse(regions []models.Region) []api.Region {
	response := make([]api.Region, len(regions))
	for i, region := range regions {
		response[i] = regionToAPI(region)
	}
	return response
}

func genChildrenPerRegionKey(level int, id *string) string {
	key := ""
	if id != nil {
		key = *id
	}
	return fmt.Sprintf("%d-%s", level, key)
}

// fillWithChildren recursively fills the children with a region
func fillWithChildren(parent *api.Region, parentLevel int, regionsByKey map[string][]models.Region) {
	key := genChildrenPerRegionKey(parentLevel, parent.Id)
	if regionsForThisParent, ok := regionsByKey[key]; ok {
		parent.Children = &[]api.Region{}
		delete(regionsByKey, key) // Remove this key from the map, so we don't get infinite recursion.
		for _, modelChild := range regionsForThisParent {
			child := regionToAPI(modelChild)
			fillWithChildren(&child, parentLevel+1, regionsByKey)

			children := append(*parent.Children, child)
			parent.Children = &children
		}
	}
}

func generateRegionTree(regions []models.Region) (api.Region, error) {

	// Generate a map of regions by a unique parent key
	regionsByKey := map[string][]models.Region{}
	for _, r := range regions {
		key := genChildrenPerRegionKey(r.Level-1, r.ParentId)
		if regionsForThisParent, ok := regionsByKey[key]; ok {
			regionsByKey[key] = append(regionsForThisParent, r)
		} else {
			regionsByKey[key] = []models.Region{r}
		}

	}

	// Start from Zambia, and generate the tree
	country := regionsByKey[genChildrenPerRegionKey(0, nil)][0] // FIXME: Only works if only Zambia as root level.
	tree := regionToAPI(country)
	fillWithChildren(&tree, 1, regionsByKey)

	return tree, nil
}

// Get the full tree in Zambia
// (GET /regions/tree)
func (s Server) GetAllRegionsTree(ctx context.Context, request api.GetAllRegionsTreeRequestObject) (api.GetAllRegionsTreeResponseObject, error) {
	regions, err := s.RegionRepo.GetAll(ctx)
	if err != nil {
		return nil, err
	}

	tree, err := generateRegionTree(regions)
	if err != nil {
		return nil, err
	}

	return api.GetAllRegionsTree200JSONResponse(tree), nil
}

// Get all regions
// (GET /regions)
func (s Server) GetAllRegions(
	ctx context.Context,
	request api.GetAllRegionsRequestObject,
) (api.GetAllRegionsResponseObject, error) {
	// Currently hardcoded to "ZMB" for Zambia
	regions, err := s.RegionRepo.GetAllChildren(ctx, "ZMB")

	if err != nil {
		return nil, err
	}

	response := mapRegionsToResponse(regions)
	return api.GetAllRegions200JSONResponse(response), nil // Wrap the response
}

// Get all chiefdoms by region ID
// (GET /regions/{id}/chiefdoms)
func (s Server) GetAllChiefdomsByRegionId(
	ctx context.Context,
	request api.GetAllChiefdomsByRegionIdRequestObject,
) (api.GetAllChiefdomsByRegionIdResponseObject, error) {

	chiefdoms, err := s.RegionRepo.GetAllChildren(ctx, request.Id)

	if err != nil {
		return nil, err
	}

	response := mapRegionsToResponse(chiefdoms)
	return api.GetAllChiefdomsByRegionId200JSONResponse(response), nil // Wrap the response
}

// Get all zones by region ID
// (GET /regions/{id}/zones)
func (s Server) GetAllZonesByRegionId(
	ctx context.Context,
	request api.GetAllZonesByRegionIdRequestObject,
) (api.GetAllZonesByRegionIdResponseObject, error) {

	chiefdoms, chiefdomErr := s.RegionRepo.GetAllChildren(ctx, request.Id)

	if chiefdomErr != nil {
		return nil, chiefdomErr
	}

	var chiefdomIds []string
	for _, chiefdom := range chiefdoms {
		chiefdomIds = append(chiefdomIds, chiefdom.Id)
	}

	zones, zoneErr := s.RegionRepo.GetAllChildrenByParentIds(ctx, chiefdomIds)

	if zoneErr != nil {
		return nil, zoneErr
	}

	response := mapRegionsToResponse(zones)
	return api.GetAllZonesByRegionId200JSONResponse(response), nil // Wrap the response
}

// Get all zones by chiefdom ID
// (GET /chiefdoms/{id}/zones)
func (s Server) GetAllZonesByChiefdomId(
	ctx context.Context,
	request api.GetAllZonesByChiefdomIdRequestObject,
) (api.GetAllZonesByChiefdomIdResponseObject, error) {

	zones, err := s.RegionRepo.GetAllChildren(ctx, request.Id)

	if err != nil {
		return nil, err
	}

	response := mapRegionsToResponse(zones)
	return api.GetAllZonesByChiefdomId200JSONResponse(response), nil // Wrap the response
}

// Get all villages by zone ID
// (GET /zones/{id}/villages)
func (s Server) GetAllVillagesByZoneId(
	ctx context.Context,
	request api.GetAllVillagesByZoneIdRequestObject,
) (api.GetAllVillagesByZoneIdResponseObject, error) {

	villages, err := s.RegionRepo.GetAllChildren(ctx, request.Id)

	if err != nil {
		return nil, err
	}

	response := mapRegionsToResponse(villages)
	return api.GetAllVillagesByZoneId200JSONResponse(response), nil // Wrap the response
}
