package routes

import (
	"context"
	"palmyra-pro-api/pkg/api"
)

// GetAllRoles returns all roles available.
// (GET /roles)
func (s Server) GetAllRoles(ctx context.Context, request api.GetAllRolesRequestObject) (api.GetAllRolesResponseObject, error) {
	var allowedRoles []string
	// Allowed only Farmer and Zone Lead Farmer
	if 	request.Params.IsFarmer != nil {
		allowedRoles = append( allowedRoles, "5", "6")
	}

	roles, err := s.RoleRepo.GetAll(
		ctx,
		allowedRoles,
	)

	if err != nil {
		return nil, err
	}

	var results []api.RoleDetails
	for _, role := range roles {
		results = append(results, api.RoleDetails{
			Id:          role.ID,
			Name:        role.Name,
			DisplayName: role.DisplayName,
		})
	}

	response := api.RolesList{
		Page:       nil,
		PageSize:   nil,
		Results:    &results,
		Total:      nil,
		TotalPages: nil,
	}

	return api.GetAllRoles200JSONResponse(response), nil
}