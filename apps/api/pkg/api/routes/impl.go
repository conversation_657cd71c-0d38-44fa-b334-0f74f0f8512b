package routes

import (
	"palmyra-pro-api/pkg/api"
	"palmyra-pro-api/pkg/db/repository"

	"firebase.google.com/go/v4/auth"
)

type Server struct {
	FarmerRepo                   repository.FarmerRepo
	UserRepo                     repository.UserRepo
	AssetStatusRepo              repository.AssetStatusRepo
	FormRepo                     repository.FormRepo
	FormComplianceSubmissionRepo repository.FormComplianceSubmissionRepo
	TransactionRepo              repository.TransactionRepo
	HoneyProcessingRepo          repository.HoneyProcessingRepo
	RegionRepo                   repository.RegionRepo
	BucketRepo                   repository.BucketRepo
	MetabasePrivateKey           string
	MetabaseURL                  string
	FirebaseAuth                 *auth.Client
}

// Double check that Server implements all services expected.
var _ api.StrictServerInterface = (*Server)(nil)
