package routes

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"palmyra-pro-api/pkg/api"
)

// Search users
// (GET /users)
func (s Server) GetUsers(ctx context.Context, request api.GetUsersRequestObject) (api.GetUsersResponseObject, error) {

	pageSize := cast.ToInt(request.Params.PageSize)

	users, err := s.UserRepo.Search(
		ctx,
		request.Params.Query,
		request.Params.Roles,
		cast.ToInt(request.Params.Page)*pageSize,
		pageSize,
	)

	if err != nil {
		return nil, err
	}

	var results []api.User
	for _, u := range users {
		results = append(results, api.User{
			Id:       u.ID,
			Name:     fmt.Sprintf("%s %s", u.FirstName, u.LastName),
			RoleName: u.Role.DisplayName,
		})
	}

	response := api.UserList{
		Page:       nil,
		PageSize:   nil,
		Results:    &results,
		Total:      nil,
		TotalPages: nil,
	}

	return api.GetUsers200JSONResponse(response), nil
}
