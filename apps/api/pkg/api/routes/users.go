package routes

import (
	"context"
	"fmt"
	"palmyra-pro-api/pkg/api"
	"palmyra-pro-api/pkg/db/models"
	"time"

	"firebase.google.com/go/v4/auth"
	"github.com/google/uuid"
	"github.com/spf13/cast"
)

func dbUserToAPI(user models.User) (api.User, error) {

	return api.User{
		Id:             user.ID,
		ExternalUID:    user.ExternalUID,
		FirstName:      user.FirstName,
		LastName:       user.LastName,
		Email:          user.Email,
		Role:        	user.Role,
		Phone:       	user.Phone,
		JobTitle:       &user.JobTitle,
	}, nil
}

// Search users
// (GET /users)
func (s Server) SearchUsers(ctx context.Context, request api.SearchUsersRequestObject) (api.SearchUsersResponseObject, error) {
	pageSize := cast.ToInt(request.Params.PageSize)
	offset := cast.ToInt(request.Params.Page) * pageSize
	query := cast.ToString(request.Params.Query)
	roleFilters := request.Params.Role

	var roles []string
	if roleFilters != nil {
		roles = *roleFilters
	}
	

	users, err := s.UserRepo.SearchUsers(
		ctx,
		query,
		offset,
		pageSize,
		roles,
	)

	if err != nil {
		return nil, err
	}

	var results []api.User
	for _, u := range users {
		results = append(results, api.User{
			Id:        u.ID,
			FirstName: u.FirstName,
			LastName:  u.LastName,
			Email:     u.Email,
			Role:      u.Role,
			Phone:     u.Phone,
		})
	}

	response := api.UserList{
		Page:       nil,
		PageSize:   nil,
		Results:    &results,
		Total:      nil,
		TotalPages: nil,
	}

	return api.SearchUsers200JSONResponse(response), nil
}

// Create users
// (POST /users)
func (s Server) CreateUser(ctx context.Context, request api.CreateUserRequestObject) (api.CreateUserResponseObject, error) {
	const tenantID = "NaturesNectar-82ft3"

	tenantAuth, err := s.FirebaseAuth.TenantManager.AuthForTenant(tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get tenant client: %w", err)
	}
	
	params := (&auth.UserToCreate{}).
		Email(request.Body.Email).
		Password(request.Body.Password)

	u, err := tenantAuth.CreateUser(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("failed to create Firebase user: %w", err)
	}

	id := uuid.New().String()
	now := time.Now()

	user := models.User{
		ID:          id,
		ExternalUID: u.UID,
		FirstName:   request.Body.FirstName,
		LastName:    request.Body.LastName,
		Email:       request.Body.Email,
		Role:        request.Body.Role,
		Phone:       request.Body.Phone,
		JobTitle:    *request.Body.JobTitle,
		CreatedAt:   now,
		UpdatedAt:   now,
	}

	err = tenantAuth.SetCustomUserClaims(ctx, u.UID, map[string]interface{}{
		"role": request.Body.Role,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to set custom claims: %w", err)
	}

	err = s.UserRepo.CreateUser(ctx, &user)
	if err != nil {
		return nil, fmt.Errorf("failed to save user in DB: %w", err)
	}

	return api.CreateUser201JSONResponse{
		Id: &id,
	}, nil
}

// GetUserById gets User by ID
// (GET /users/{id})
func (s Server) GetUserById(ctx context.Context, request api.GetUserByIdRequestObject) (api.GetUserByIdResponseObject, error) {
	user, err := s.UserRepo.GetUserById(ctx, request.Id)
	if err != nil {
		return nil, err
	}

	if user == nil {
		return api.GetUserById404JSONResponse{
			Code:    "NOT_FOUND",
			Message: "User not found",
		}, nil
	}

	response, err := dbUserToAPI(*user)
	if err != nil {
		return nil, err
	}

	return api.GetUserById200JSONResponse(response), nil
}

// Update a existing user
// (POST /users/{id})
func (s Server) UpdateUser(ctx context.Context, request api.UpdateUserRequestObject) (api.UpdateUserResponseObject, error) {
	retrieved, err := s.UserRepo.GetUserById(ctx, request.Id)
	if err != nil {
		return nil, err
	}

	if retrieved == nil {
		return api.UpdateUser404JSONResponse{
			Code:    "NOT_FOUND",
			Message: fmt.Sprintf("User with id [%s] not found", request.Id),
		}, nil
	}

	user := models.User{
		ID:          request.Id,
		FirstName:   request.Body.FirstName,
		LastName:    request.Body.LastName,
		Role:        request.Body.Role,
		Phone:       request.Body.Phone,
		JobTitle:    *request.Body.JobTitle,
		CreatedAt:   retrieved.CreatedAt,
		Email:       retrieved.Email,
		ExternalUID: retrieved.ExternalUID,
	}

	err = s.UserRepo.UpdateUser(
		ctx,
		&user,
	)

	if err != nil {
		return nil, err
	}

	return api.UpdateUser200JSONResponse{
		Id: &request.Id,
	}, nil
}

// Soft delete User by ID
// (DELETE /users/{id})
func (s Server) DeleteUserById(ctx context.Context, request api.DeleteUserByIdRequestObject) (api.DeleteUserByIdResponseObject, error) {
	retrieved, err := s.UserRepo.GetUserById(ctx, request.Id)
	if err != nil {
		return nil, err
	}
	if retrieved == nil {
		return api.DeleteUserById404JSONResponse{
			Code:    "NOT_FOUND",
			Message: fmt.Sprintf("User with id [%s] not found", request.Id),
		}, nil
	}

	if retrieved.ExternalUID != "" {
		err = s.FirebaseAuth.DeleteUser(ctx, retrieved.ExternalUID)
		if err != nil {
			return nil, fmt.Errorf("failed to delete user from Firebase: %w", err)
		}
	}

	err = s.UserRepo.DeleteUser(ctx, request.Id)
	if err != nil {
		return nil, err
	}

	return api.DeleteUserById204Response{}, nil
}