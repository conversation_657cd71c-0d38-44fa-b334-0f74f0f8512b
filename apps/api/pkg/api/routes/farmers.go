package routes

import (
	"context"
	"fmt"
	"palmyra-pro-api/pkg/api"
	"palmyra-pro-api/pkg/db/models"
	"time"

	"github.com/google/uuid"
	openapi_types "github.com/oapi-codegen/runtime/types"
	"github.com/spf13/cast"
)

func parseDOBFromDB(dobString *string) (*openapi_types.Date, error) {
	if dobString == nil {
		return nil, nil
	}

	dob, err := time.Parse("2006-01-02T15:04:05", *dobString)
	if err != nil {
		return nil, err
	}

	dobOpenAPI := openapi_types.Date{dob}
	return &dobOpenAPI, nil
}

func parseDOBFromAPI(dobAPI *openapi_types.Date) *string {
	if dobAPI == nil {
		return nil
	}

	dobString := dobAPI.Format("2006-01-02T15:04:05")
	return &dobString
}

func dbToAPI(farmer models.Farmer) (api.Farmer, error) {
	dob, err := parseDOBFromDB(farmer.Dob)
	if err != nil {
		return api.Farmer{}, err
	}

	return api.Farmer{
		Dob:                   dob,
		EstimatedAnnualIncome: farmer.EstimatedAnnualIncome,
		FirstName:             farmer.FirstName,
		Gender:                farmer.Gender,
		HouseholdSize:         farmer.HouseholdSize,
		Id:                    farmer.ID,
		LastName:              farmer.LastName,
		MaritalStatus:         farmer.MaritalStatus,
		Nrc:                   farmer.NRC,
		Phone:                 farmer.Phone,
		RoleDisplayName:       farmer.Role.DisplayName,
		RoleId:                farmer.RoleID,
		SourceOfIncome:        farmer.SourceOfIncome,

		CountryId:   farmer.CountryID,
		CountryName: farmer.Country.Name,
		RegionId:    farmer.RegionID,
		ChiefdomId:  farmer.ChiefdomID,
		ZoneId:      farmer.ZoneID,
		VillageId:   farmer.VillageID,

		CreatedAt: farmer.CreatedAt,
		UpdatedAt: farmer.UpdatedAt,
	}, nil
}

func quickSearchToAPI(farmer models.FarmerQuickSearch) (api.Farmer, error) {
	dob, err := parseDOBFromDB(farmer.Dob)
	if err != nil {
		return api.Farmer{}, err
	}

	var honeybees []struct {
		Id   string `json:"id"`
		Name string `json:"name"`
	}

	for _, beehive := range farmer.Beehives {
		honeybees = append(honeybees, struct {
			Id   string `json:"id"`
			Name string `json:"name"`
		}{
			Id:   beehive.ID,
			Name: beehive.Asset.Name,
		})
	}

	return api.Farmer{
		Id: farmer.ID,

		Dob:                   dob,
		EstimatedAnnualIncome: farmer.EstimatedAnnualIncome,
		FirstName:             farmer.FirstName,
		Gender:                farmer.Gender,
		HouseholdSize:         farmer.HouseholdSize,
		LastName:              farmer.LastName,
		MaritalStatus:         farmer.MaritalStatus,
		Nrc:                   farmer.NRC,
		Phone:                 farmer.Phone,
		RoleId:                farmer.RoleID,
		RoleDisplayName:       farmer.RoleDisplayName,
		SourceOfIncome:        farmer.SourceOfIncome,

		CreatedAt: farmer.CreatedAt,
		UpdatedAt: farmer.UpdatedAt,

		CountryId:  farmer.CountryID,
		RegionId:   farmer.RegionID,
		ChiefdomId: farmer.ChiefdomID,
		ZoneId:     farmer.ZoneID,
		VillageId:  farmer.VillageID,
		Beehives:   &honeybees,
	}, nil
}

// Download all farmers for offline usage.
// (GET /farmers/offline)
func (s Server) DownloadFarmersOffline(ctx context.Context, _ api.DownloadFarmersOfflineRequestObject) (api.DownloadFarmersOfflineResponseObject, error) {
	farmers, err := s.FarmerRepo.AllFarmersOffline(
		ctx,
	)

	if err != nil {
		return nil, err
	}

	var results []api.Farmer
	for _, f := range farmers {
		farmer, err := quickSearchToAPI(*f)
		if err != nil {
			return nil, err
		}
		results = append(results, farmer)
	}

	response := api.FarmerList{
		Page:       nil,
		PageSize:   nil,
		Results:    &results,
		Total:      nil,
		TotalPages: nil,
	}

	return api.DownloadFarmersOffline200JSONResponse(response), nil
}

// SearchFarmers search farmers
// (GET /farmers/search)
func (s Server) SearchFarmers(ctx context.Context, request api.SearchFarmersRequestObject) (api.SearchFarmersResponseObject, error) {

	pageSize := cast.ToInt(request.Params.PageSize)
	farmers, err := s.FarmerRepo.QuickSearchFarmers(
		ctx,
		cast.ToString(request.Params.Query),
		cast.ToInt(request.Params.Page)*pageSize,
		pageSize,
	)

	if err != nil {
		return nil, err
	}

	var results []api.Farmer
	for _, f := range farmers {
		farmer, err := quickSearchToAPI(*f)
		if err != nil {
			return nil, err
		}
		results = append(results, farmer)
	}

	response := api.FarmerList{
		Page:       nil,
		PageSize:   nil,
		Results:    &results,
		Total:      nil,
		TotalPages: nil,
	}

	return api.SearchFarmers200JSONResponse(response), nil
}

// SearchFarmers search farmers
// (GET /farmers)
func (s Server) GetFarmers(ctx context.Context, request api.GetFarmersRequestObject) (api.GetFarmersResponseObject, error) {

	pageSize := cast.ToInt(request.Params.PageSize)
	farmers, err := s.FarmerRepo.SearchFarmers(
		ctx,
		cast.ToString(request.Params.Query),
		cast.ToInt(request.Params.Page)*pageSize,
		pageSize,
	)

	if err != nil {
		return nil, err
	}

	var results []api.Farmer
	for _, f := range farmers {
		farmer, err := quickSearchToAPI(*f)
		if err != nil {
			return nil, err
		}
		results = append(results, farmer)
	}

	response := api.FarmerList{
		Page:       nil,
		PageSize:   nil,
		Results:    &results,
		Total:      nil,
		TotalPages: nil,
	}

	return api.GetFarmers200JSONResponse(response), nil
}

// GetFarmerById gets Farmer by ID
// (GET /farmers/{id})
func (s Server) GetFarmerById(ctx context.Context, request api.GetFarmerByIdRequestObject) (api.GetFarmerByIdResponseObject, error) {
	farmer, err := s.FarmerRepo.GetFarmerByID(ctx, request.Id)
	if err != nil {
		return nil, err
	}

	if farmer == nil {
		return api.GetFarmerById404JSONResponse{
			Code:    "NOT_FOUND",
			Message: "Farmer not found",
		}, nil
	}

	response, err := dbToAPI(*farmer)
	if err != nil {
		return nil, err
	}

	return api.GetFarmerById200JSONResponse(response), nil
}

// Create a new farmer
// (POST /farmers)
func (s Server) CreateFarmer(ctx context.Context, request api.CreateFarmerRequestObject) (api.CreateFarmerResponseObject, error) {
	id := uuid.New().String()
	now := time.Now()

	farmer := models.Farmer{
		ID:                    id,
		RoleID:                request.Body.RoleId,
		FirstName:             request.Body.FirstName,
		LastName:              request.Body.LastName,
		Gender:                request.Body.Gender,
		Phone:                 request.Body.Phone,
		NRC:                   request.Body.Nrc,
		MaritalStatus:         request.Body.MaritalStatus,
		Dob:                   parseDOBFromAPI(request.Body.Dob),
		HouseholdSize:         request.Body.HouseholdSize,
		EstimatedAnnualIncome: request.Body.EstimatedAnnualIncome,
		SourceOfIncome:        request.Body.SourceOfIncome,

		CountryID:  request.Body.CountryId,
		RegionID:   request.Body.RegionId,
		ChiefdomID: request.Body.ChiefdomId,
		ZoneID:     request.Body.ZoneId,
		VillageID:  request.Body.VillageId,

		Metadata: nil,

		CreatedAt: now,
		UpdatedAt: now,

		// FarmID:                "",
		// UserID:                nil,
	}

	err := s.FarmerRepo.CreateFarmer(
		ctx,
		&farmer,
	)

	if err != nil {
		return nil, err
	}

	return api.CreateFarmer201JSONResponse{
		Id: &id,
	}, nil
}

// Update a existing farmer
// (POST /farmers/{id})
func (s Server) UpdateFarmer(ctx context.Context, request api.UpdateFarmerRequestObject) (api.UpdateFarmerResponseObject, error) {
	retrieved, err := s.FarmerRepo.GetFarmerByID(ctx, request.Id)
	if err != nil {
		return nil, err
	}

	if retrieved == nil {
		return api.UpdateFarmer404JSONResponse{
			Code:    "NOT_FOUND",
			Message: fmt.Sprintf("Farmer with id [%s] not found", request.Id),
		}, nil
	}

	farmer := models.Farmer{
		ID:                    request.Id,
		RoleID:                request.Body.RoleId,
		FirstName:             request.Body.FirstName,
		LastName:              request.Body.LastName,
		Gender:                request.Body.Gender,
		Phone:                 request.Body.Phone,
		NRC:                   request.Body.Nrc,
		MaritalStatus:         request.Body.MaritalStatus,
		Dob:                   parseDOBFromAPI(request.Body.Dob),
		HouseholdSize:         request.Body.HouseholdSize,
		EstimatedAnnualIncome: request.Body.EstimatedAnnualIncome,
		SourceOfIncome:        request.Body.SourceOfIncome,

		CountryID:  request.Body.CountryId,
		RegionID:   request.Body.RegionId,
		ChiefdomID: request.Body.ChiefdomId,
		ZoneID:     request.Body.ZoneId,
		VillageID:  request.Body.VillageId,

		Metadata: retrieved.Metadata,

		CreatedAt: retrieved.CreatedAt,

		// FarmID:                "",
		// UserID:                nil,
	}

	err = s.FarmerRepo.UpdateFarmer(
		ctx,
		&farmer,
	)

	if err != nil {
		return nil, err
	}

	return api.UpdateFarmer200JSONResponse{
		Id: &request.Id,
	}, nil
}

// Soft delete Farmer by ID
// (DELETE /farmers/{id})
func (s Server) DeleteFarmerById(ctx context.Context, request api.DeleteFarmerByIdRequestObject) (api.DeleteFarmerByIdResponseObject, error) {
	retrieved, err := s.FarmerRepo.GetFarmerByID(ctx, request.Id)
	if err != nil {
		return nil, err
	}
	if retrieved == nil {
		return api.DeleteFarmerById404JSONResponse{
			Code:    "NOT_FOUND",
			Message: fmt.Sprintf("Farmer with id [%s] not found", request.Id),
		}, nil
	}

	err = s.FarmerRepo.DeleteFarmer(ctx, request.Id)
	if err != nil {
		return nil, err
	}

	return api.DeleteFarmerById204Response{}, nil

}
