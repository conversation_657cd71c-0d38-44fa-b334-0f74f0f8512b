package routes

import (
	"context"
	"palmyra-pro-api/pkg/api"
)

// Get all buckets
// (GET /buckets)
func (s Server) GetAllBuckets(
	ctx context.Context,
	request api.GetAllBucketsRequestObject,
) (api.GetAllBucketsResponseObject, error) {
	buckets, err := s.BucketRepo.Search(
		ctx,
		*request.Params.Region,
		*request.Params.Chiefdom,
		*request.Params.Zone,
		*request.Params.Village,
		(*string)(request.Params.HoneyType),
		*request.Params.Query,
	)

	if err != nil {
		return nil, err
	}

	results := []api.Bucket{}
	for _, bucket := range buckets {

		results = append(results, api.Bucket{
			Id:              bucket.Id,
			BucketId:        bucket.BucketId,
			HarvestDate:     bucket.HarvestDate,
			MoistureContent: bucket.MoistureContent,
			Weight:          bucket.Weight,
			HoneyType:       bucket.HoneyType,
			ContaminationStatus: (*api.BucketContaminationStatus)(&bucket.ContaminationStatus),
			Farmer: &api.BucketFarmer{
				Id:   bucket.Transaction.Farmer.ID,
				Name: bucket.Transaction.Farmer.FirstName + " " + bucket.Transaction.Farmer.LastName,
			},
		})
	}

	return api.GetAllBuckets200JSONResponse(results), nil
}
