package middlewares

import (
	"context"
	"errors"
	"fmt"
	"log"
	"net/http"
	"strings"

	"palmyra-pro-api/pkg/api"

	"firebase.google.com/go/v4/auth"
	"github.com/getkin/kin-openapi/openapi3filter"
	middleware "github.com/oapi-codegen/nethttp-middleware"
)

type contextKey string

const (
	firebaseUIDKey contextKey = "firebase_uid"
	tenantIDKey    contextKey = "tenant_id"
)

// CreateValidationsMiddleware creates a middleware that will validate the request following openapi specifications.
// - Request params, body, etc... format.
// - Authentication / Authorization via claims in the token.
func CreateValidationsMiddleware(authClient *auth.Client) (func(next http.Handler) http.Handler, error) {
	spec, err := api.GetSwagger()
	if err != nil {
		return nil, fmt.Errorf("loading spec: %w", err)
	}

	// Clear out the servers array in the swagger spec, that skips validating
	// that server names match. We don't know how this thing will be run.
	spec.Servers = nil

	validator := middleware.OapiRequestValidatorWithOptions(spec,
		&middleware.Options{
			Options: openapi3filter.Options{
				ExcludeRequestBody:        true,
				ExcludeResponseBody:       true,
				ExcludeRequestQueryParams: true,
				AuthenticationFunc:        FirebaseAuthenticationFunc(authClient),
			},
		})

	return validator, nil
}

func FirebaseAuthenticationFunc(authClient *auth.Client) openapi3filter.AuthenticationFunc {
	return func(ctx context.Context, input *openapi3filter.AuthenticationInput) error {
		
		authHeader := input.RequestValidationInput.Request.Header.Get("Authorization")
		if authHeader == "" || !strings.HasPrefix(authHeader, "Bearer ") {
			return errors.New("missing or invalid Authorization header")
		}

		idToken := strings.TrimPrefix(authHeader, "Bearer ")

		parsedToken, err := authClient.VerifyIDToken(ctx, idToken)
		if err != nil {
			log.Printf("Failed to parse ID token (initial): %v\n", err)
			return errors.New("invalid token")
		}

		firebaseClaims, ok := parsedToken.Claims["firebase"].(map[string]interface{})
		if !ok {
			return errors.New("missing firebase claim")
		}

		tenantID, ok := firebaseClaims["tenant"].(string)
		if !ok || tenantID == "" {
			return errors.New("missing tenant ID in firebase claim")
		}

		tenantAuthClient, err := authClient.TenantManager.AuthForTenant(tenantID)
		if err != nil {
			log.Printf("Could not get tenant auth client: %v\n", err)
			return errors.New("failed to resolve tenant")
		}

		token, err := tenantAuthClient.VerifyIDToken(ctx, idToken)
		if err != nil {
			log.Printf("Token verification failed (tenant-scoped): %v\n", err)
			return errors.New("invalid or expired tenant-scoped token")
		}

		ctxWithUID := context.WithValue(ctx, firebaseUIDKey, token.UID)
		ctxWithTenant := context.WithValue(ctxWithUID, tenantIDKey, tenantID)

		input.RequestValidationInput.Request = input.RequestValidationInput.Request.WithContext(ctxWithTenant)
		return nil
	}
}