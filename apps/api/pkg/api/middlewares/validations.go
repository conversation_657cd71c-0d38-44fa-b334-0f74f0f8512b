package middlewares

import (
	"fmt"
	"github.com/getkin/kin-openapi/openapi3filter"
	middleware "github.com/oapi-codegen/nethttp-middleware"
	"log"
	"net/http"
	"palmyra-pro-api/pkg/api"
	"palmyra-pro-api/pkg/auth"
)

// CreateValidationsMiddleware creates a middleware that will validate the request following openapi specifications.
// - Request params, body, etc... format.
// - Authentication / Authorization via claims in the token.
func CreateValidationsMiddleware(jwtPub<PERSON><PERSON>ey, kid string) (func(next http.Handler) http.Handler, error) {

	// Create authenticator
	authenticator, err := auth.NewBetterAuthAuthenticator(jwt<PERSON><PERSON><PERSON><PERSON><PERSON>, kid)
	if err != nil {
		log.Fatalln("error creating authenticator:", err)
	}

	spec, err := api.GetSwagger()
	if err != nil {
		return nil, fmt.Errorf("loading spec: %w", err)
	}

	// Clear out the servers array in the swagger spec, that skips validating
	// that server names match. We don't know how this thing will be run.
	spec.Servers = nil

	validator := middleware.OapiRequestValidatorWithOptions(spec,
		&middleware.Options{
			Options: openapi3filter.Options{
				ExcludeRequestBody:        true,
				ExcludeResponseBody:       true,
				ExcludeRequestQueryParams: true,
				AuthenticationFunc:        auth.NewAuthenticator(authenticator),
			},
		})

	return validator, nil
}
