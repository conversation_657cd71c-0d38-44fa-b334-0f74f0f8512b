package utils

import (
	"encoding/json"
	"fmt"
	"log"
	"slices"
)

// https://cloud.google.com/functions/docs/monitoring/logging#functions-log-helloworld-go
// https://cloud.google.com/logging/docs/structured-logging
// https://cloud.google.com/functions/docs/monitoring/logging
// https://cloud.google.com/logging/docs/agent/logging/configuration#special-fields

var defaultEnvironment = ""

func init() {
	// Disable log prefixes such as the default timestamp.
	// Prefix text prevents the message from being parsed as JSON.
	// A timestamp is added when shipping logs to Cloud Logging.
	log.SetFlags(0)
	defaultEnvironment = GetEnvOrDefault("ENV_SHORT_NAME", "undefined")
}

// LogEntry defines a log entry.
type LogEntry struct {
	Message  any    `json:"message"`
	Severity string `json:"severity,omitempty"` // https://cloud.google.com/logging/docs/reference/v2/rest/v2/LogEntry#LogSeverity
	Trace    string `json:"logging.googleapis.com/trace,omitempty"`
	UserUID  string `json:"remoteUserUID,omitempty"`
	EventID  string `json:"eventID,omitempty"`

	// Logs Explorer allows filtering and display of this as `jsonPayload.component`.
	Component    string `json:"component,omitempty"`
	SubComponent string `json:"subComponent,omitempty"`
	Environment  string `json:"environment"`
}

func NewLogEntry(msg string) LogEntry {
	return LogEntry{
		Message:     msg,
		Environment: defaultEnvironment,
	}
}

func NewLogEntryf(format string, a ...any) LogEntry {
	return LogEntry{
		Message:     fmt.Sprintf(format, a...),
		Environment: defaultEnvironment,
	}
}

func NewLogEntryJson(obj any) LogEntry {
	return LogEntry{
		Message:     obj,
		Environment: defaultEnvironment,
	}
}

var severities = []string{"INFO", "WARNING", "ERROR", "DEBUG"}

func (l LogEntry) String() string {
	if !slices.Contains(severities, l.Severity) {
		l.Severity = "INFO"
	}
	out, err := json.Marshal(l)
	if err != nil {
		log.Printf("Error marshalling log [%#v]: %v", l, err)
	}
	return string(out)
}

func (l LogEntry) Log() {
	log.Println(l.String())
}
func (l LogEntry) LogInfo() {
	l.Severity = "INFO"
	log.Println(l.String())
}
func (l LogEntry) LogWarning() {
	l.Severity = "WARNING"
	log.Println(l.String())
}

func (l LogEntry) LogError() {
	l.Severity = "ERROR"
	log.Println(l.String())
}

func (l LogEntry) LogWithError(err error) {
	l.Severity = "ERROR"
	wrappedError := fmt.Errorf("%s Error: %w", l.Message, err)
	l.Message = wrappedError.Error()
	log.Println(l.String())
}

func (l LogEntry) LogDebug() {
	l.Severity = "DEBUG"
	log.Println(l.String())
}

func (l LogEntry) WithComponent(component string) LogEntry {
	l.Component = component
	return l
}

func (l LogEntry) WithSubComponent(subComponent string) LogEntry {
	l.SubComponent = subComponent
	return l
}

func (l LogEntry) WithUserUID(uid string) LogEntry {
	l.UserUID = uid
	return l
}

func (l LogEntry) WithEnv(env string) LogEntry {
	l.Environment = env
	return l
}
