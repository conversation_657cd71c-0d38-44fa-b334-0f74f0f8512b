package utils

import (
	"fmt"
	"strings"
)

func GenerateBatchCode(honeyType string, sessionYear int, regionId string, zoneId *string) string {
	honeyCode := "L"
	if honeyType == "dark" {
		honeyCode = "D"
	}
	yearSuffix := sessionYear % 100

	zone := "Bl"
	if zoneId != nil {
		zone = *zoneId
	}

	return strings.ToUpper(fmt.Sprintf("%s-%s-%s-%02d", regionId, zone, honeyCode, yearSuffix))
}

func IsBatchCodeBlended(batchCode string) bool {
	parts := strings.Split(batchCode, "-")
	if len(parts) < 3 {
		return false
	}
	return strings.ToUpper(parts[1]) == "BL"
}
