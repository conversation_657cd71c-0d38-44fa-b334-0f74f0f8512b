package schemas

import "time"

// Schema represents the structure of the IPFS data.
type HoneyHarvestingV1 struct {
	SchemaVersion string                   `json:"schemaVersion" default:"g:nnz:honeyHarvesting:0.1"`
	LogTime       time.Time                `json:"logTime"`
	Events        []HoneyHarvestingV1Event `json:"events"`
}

// Event represents an individual event in the schema.
type HoneyHarvestingV1Event struct {
	EventType  string                         `json:"eventType"`
	EventTime  time.Time                      `json:"eventTime"`
	Inputs     []HoneyHarvestingV1InputOutput `json:"inputs"`
	Outputs    []HoneyHarvestingV1InputOutput `json:"outputs"`
	Properties HoneyHarvestingV1Properties    `json:"properties"`
}

type HoneyHarvestingV1InputOutputProperties struct {
	Quantity float32 `json:"quantity"`
	Unit     string  `json:"unit"`
}

// Item represents an input or output item in an event.
type HoneyHarvestingV1InputOutput struct {
	Id             string                                   `json:"id"`
	ItemDefinition string                                   `json:"itemDefinition"`
	Properties     []HoneyHarvestingV1InputOutputProperties `json:"properties"`
}

// Properties represents additional metadata for an event.
type HoneyHarvestingV1Properties struct {
	TransactionID string                    `json:"transactionId"`
	Sellers       []HoneyHarvestingV1Person `json:"sellers"`
	Buyers        []HoneyHarvestingV1Person `json:"buyers"`
}

// Person represents a seller or buyer in the event properties.
type HoneyHarvestingV1Person struct {
	Id       string `json:"id,omitempty"`
	Name     string `json:"name"`
	Location string `json:"location,omitempty"`
}
