package auth

import (
	"testing"
)

const KeyID = `zLDzm6YiI9f8nlJZbsDM9HXOCH0lTUeU`
const PublicKey = `qy964kYJs7K9syax-gniWJnJEXRou2UacqzTLSWCyv0`

func TestFakeAuthenticator_ValidateJWS(t *testing.T) {
	tests := []struct {
		name      string
		jwsString string
		want      bool
		wantErr   bool
	}{
		{
			name:      "Valid signed token",
			jwsString: "eyJhbGciOiJFZERTQSIsImtpZCI6InpMRHptNllpSTlmOG5sSlpic0RNOUhYT0NIMGxUVWVVIn0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.YO41dmVbHEJBrR3gkEofo2BR3VSmfKtmLJ0_A6QZgn4CXoJbvix61Bk04xPS5kTILbNrPq63XlPxrsBLVpxUAw",
			want:      true,
			wantErr:   false,
		},
	}

	auth, err := NewBetterAuthAuthenticator(PublicKey, KeyID)
	if err != nil {
		panic(err)
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := auth.ValidateJWS(tt.jwsString)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateJWS() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.Valid != tt.want {
				t.Errorf("ValidateJWS() valid got = %v, want %v", got.Valid, tt.want)
			}
		})
	}
}
