package auth

import (
	"crypto/ed25519"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"github.com/golang-jwt/jwt/v5"
	"github.com/pkg/errors"
	"io"
	"net/http"
)

type BetterAuthAuthenticator struct {
	PublicKey   ed25519.PublicKey
	ExpectedKid string
}

var _ JWSValidator = (*BetterAuthAuthenticator)(nil)

// NewBetterAuthAuthenticator creates an authenticator example which uses a hard coded
// ECDSA key to validate JWT's that it has signed itself.
func NewBetterAuthAuthenticator(rawPublicKey, expectedKid string) (*BetterAuthAuthenticator, error) {
	publicKeyBytes, err := base64.RawURLEncoding.DecodeString(rawPublicKey)
	if err != nil {
		return nil, fmt.Errorf("failed to decode public key: %v", err)
	}

	// Convert to Ed25519 PublicKey
	publicKey := ed25519.PublicKey(publicKeyBytes)

	return &BetterAuthAuthenticator{PublicKey: publicKey, ExpectedKid: expectedKid}, nil
}

// JW<PERSON><PERSON> represents a single key in the "keys" array.
type JWKey struct {
	Crv string `json:"crv"`
	X   string `json:"x"`
	Kty string `json:"kty"`
	Kid string `json:"kid"`
}

// JWKS represents the full response from the endpoint.
type JWKS struct {
	Keys []JWKey `json:"keys"`
}

// FetchJWKS fetches the JWT keys from the given URL.
func FetchJWKS(url string) (*JWKS, error) {
	resp, err := http.Get(url)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch JWKS: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var jwks JWKS
	if err := json.Unmarshal(bodyBytes, &jwks); err != nil {
		return nil, fmt.Errorf("failed to unmarshal JWKS: %w", err)
	}

	return &jwks, nil
}

// ValidateJWS ensures that the critical JWT claims needed to ensure that we
// trust the JWT are present and with the correct values.
func (f *BetterAuthAuthenticator) ValidateJWS(jwsString string) (*jwt.Token, error) {

	var keyfunc jwt.Keyfunc = func(token *jwt.Token) (interface{}, error) {
		return f.PublicKey, nil
	}

	parsed, err := jwt.Parse(jwsString, keyfunc)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to parse JWT.")
	}

	if !parsed.Valid {
		return nil, fmt.Errorf("Token is not valid.")
	}

	if currentKid, found := parsed.Header["kid"]; !found || f.ExpectedKid != currentKid {
		return nil, fmt.Errorf("Kid not found or does not match")
	}

	return parsed, nil

}

// // SignToken takes a JWT and signs it with our private key, returning a JWS.
// func (f *BetterAuthAuthenticator) SignToken(t jwt.Token) ([]byte, error) {
// 	hdr := jws.NewHeaders()
// 	if err := hdr.Set(jws.AlgorithmKey, jwa.ES256); err != nil {
// 		return nil, fmt.Errorf("setting algorithm: %w", err)
// 	}
// 	if err := hdr.Set(jws.TypeKey, "JWT"); err != nil {
// 		return nil, fmt.Errorf("setting type: %w", err)
// 	}
// 	if err := hdr.Set(jws.KeyIDKey, KeyID); err != nil {
// 		return nil, fmt.Errorf("setting Key ID: %w", err)
// 	}
// 	return jwt.Sign(t, jwa.ES256, f.PrivateKey, jwt.WithHeaders(hdr))
// }

// // CreateJWSWithClaims is a helper function to create JWT's with the specified
// // claims.
// func (f *BetterAuthAuthenticator) CreateJWSWithClaims(claims []string) ([]byte, error) {
// 	t := jwt.New()
// 	err := t.Set(jwt.IssuerKey, FakeIssuer)
// 	if err != nil {
// 		return nil, fmt.Errorf("setting issuer: %w", err)
// 	}
// 	err = t.Set(jwt.AudienceKey, FakeAudience)
// 	if err != nil {
// 		return nil, fmt.Errorf("setting audience: %w", err)
// 	}
// 	err = t.Set(PermissionsClaim, claims)
// 	if err != nil {
// 		return nil, fmt.Errorf("setting permissions: %w", err)
// 	}
// 	return f.SignToken(t)
// }
