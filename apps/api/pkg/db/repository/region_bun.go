package repository

import (
	"context"
	"palmyra-pro-api/pkg/db/models"

	"github.com/uptrace/bun"
)

type regionRepo struct {
	db *bun.DB
}

func NewRegionRepository(db *bun.DB) RegionRepo {
	return &regionRepo{db: db}
}

func (r *regionRepo) GetAll(ctx context.Context) ([]models.Region, error) {
	var regions []models.Region
	err := r.db.NewSelect().
		Model(&regions).
		Scan(ctx)

	return regions, err
}

func (r *regionRepo) GetAllChildren(ctx context.Context, parentId string) ([]models.Region, error) {
	var regions []models.Region
	err := r.db.NewSelect().
		Model(&regions).
		Where("region.parent_id = ?", parentId).
		Order("region.name ASC").
		Scan(ctx)

	if err != nil {
		return nil, err
	}

	return regions, nil
}

func (r *regionRepo) GetAllChildrenByParentIds(ctx context.Context, parentIds []string) ([]models.Region, error) {
	var regions []models.Region
	err := r.db.NewSelect().
		Model(&regions).
		Where("region.parent_id IN (?)", bun.In(parentIds)).
		Order("region.name ASC").
		Scan(ctx)

	if err != nil {
		return nil, err
	}

	return regions, nil
}

func (r *regionRepo) GetById(ctx context.Context, id string) (*models.Region, error) {
	var region models.Region
	err := r.db.NewSelect().
		Model(&region).
		Where("region.id = ?", id).
		Scan(ctx)

	if err != nil {
		return nil, err
	}

	return &region, nil
}
