package repository

import (
	"context"
	"palmyra-pro-api/pkg/db/models"
)

type FormRepo interface {
	CreateForm(ctx context.Context, form *models.Form) (string, error)
	GetFormByID(ctx context.Context, id string) (*models.Form, error)
	UpdateForm(ctx context.Context, form *models.Form) error
	DeleteForm(ctx context.Context, id string) error
	ListForms(ctx context.Context, globalFilters map[string]interface{}, offset, limit int) ([]*models.Form, error)
}
