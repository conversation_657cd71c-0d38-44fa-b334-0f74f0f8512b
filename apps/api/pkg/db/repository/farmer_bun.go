package repository

import (
	"context"
	"palmyra-pro-api/pkg/db/models"
	"time"

	"github.com/uptrace/bun"
)

type farmerRepo struct {
	db *bun.DB
}

func NewFarmerRepo(db *bun.DB) FarmerRepo {
	return &farmerRepo{
		db: db,
	}
}

func (r *farmerRepo) CreateFarmer(ctx context.Context, farmer *models.Farmer) error {
	_, err := r.db.NewInsert().Model(farmer).Exec(ctx)
	return err
}

func (r *farmerRepo) GetFarmerByID(ctx context.Context, id string) (*models.Farmer, error) {
	farmer := new(models.Farmer)
	err := r.db.NewSelect().Model(farmer).Relation("Country").Where("farmer.id = ?", id).Scan(ctx)
	return farmer, err
}

func (r *farmerRepo) UpdateFarmer(ctx context.Context, farmer *models.Farmer) error {
	_, err := r.db.NewUpdate().Model(farmer).Where("id = ?", farmer.ID).Exec(ctx)
	return err
}

func (r *farmerRepo) DeleteFarmer(ctx context.Context, id string) error {
	_, err := r.db.NewUpdate().
		Model((*models.Farmer)(nil)).
		Set("is_deleted = ?", true).
		Set(`"updatedAt" = ?`, time.Now()).
		Where("id = ?", id).
		Exec(ctx)
	return err
}

func (r *farmerRepo) AllFarmersOffline(ctx context.Context) ([]*models.FarmerQuickSearch, error) {
	var farmers []*models.FarmerQuickSearch

	query := r.db.NewSelect().
		Model(&farmers).
		ColumnExpr(`farmer_quick_search.*, farm.id AS "farmId", farm.name AS "farmName"`).
		Join(`left join farm on farm.id = farmer_quick_search."farmId"`).
		Relation("Beehives").
		Relation("Beehives.Asset")

	err := query.Scan(ctx)

	return farmers, err
}

func (r *farmerRepo) QuickSearchFarmers(ctx context.Context, fuzzyFilter string, offset, limit int) ([]*models.FarmerQuickSearch, error) {
	var err error
	var farmers []*models.FarmerQuickSearch
	
	query := r.db.NewSelect().
		Model(&farmers).
		// FIXME: WTF!!! Don't use upper case in databases!!!! https://wiki.postgresql.org/wiki/Don't_Do_This#Don.27t_use_upper_case_table_or_column_names
		ColumnExpr(`farmer_quick_search.*, farm.id AS "farmId", farm.name AS "farmName"`).
		Join(`left join farm on farm.id = farmer_quick_search."farmId"`).
		Relation("Beehives").
		Relation("Beehives.Asset")

	// FIXME: Use a real fuzzy search solution.
	if len(fuzzyFilter) > 4 {
		query = query.WhereOr(`nrc ILIKE ? OR "firstName" || ' ' || "lastName" ILIKE ? OR farmer_quick_search.id = ?`, "%"+fuzzyFilter+"%", "%"+fuzzyFilter+"%", fuzzyFilter)
	} else if len(fuzzyFilter) <= 4 {
		query = query.WhereOr("farmer_quick_search.id = ?", fuzzyFilter)
	}

	query = query.Where("is_deleted = ?", false)

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	err = query.Scan(ctx)

	return farmers, err
}

func (r *farmerRepo) SearchFarmers(ctx context.Context, fuzzyFilter string, offset, limit int) ([]*models.FarmerQuickSearch, error) {
	var err error
	var farmers []*models.FarmerQuickSearch

	query := r.db.NewSelect().
		Model(&farmers).
		// FIXME: WTF!!! Don't use upper case in databases!!!! https://wiki.postgresql.org/wiki/Don't_Do_This#Don.27t_use_upper_case_table_or_column_names
		ColumnExpr(`farmer_quick_search.*, farm.id AS "farmId", farm.name AS "farmName"`).
		Join(`left join farm on farm.id = farmer_quick_search."farmId"`)

	// FIXME: Use a real fuzzy search solution.
	if len(fuzzyFilter) > 0 {
		query = query.WhereOr(`nrc ILIKE ? OR "firstName" || ' ' || "lastName" ILIKE ? OR farmer_quick_search.id = ?`, "%"+fuzzyFilter+"%", "%"+fuzzyFilter+"%", fuzzyFilter)
	}

	query = query.Where("is_deleted = ?", false)
	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	err = query.Scan(ctx, &farmers)

	return farmers, err
}

func (r *farmerRepo) ListFarmers(ctx context.Context, fuzzyFilter string, filter map[string]interface{}, offset, limit int) ([]*models.Farmer, error) {
	var farmers []*models.Farmer
	query := r.db.NewSelect().Model(&farmers)

	// Apply filters
	for key, value := range filter {
		query = query.Where("? = ?", bun.Ident(key), value)
	}

	// FIXME: Use a real fuzzy search solution.
	if len(fuzzyFilter) > 4 {
		query = query.Where(`"firstName" || ' ' || "lastName" ILIKE ?`, fuzzyFilter)
	}

	query = query.Where("is_deleted = ?", false)

	// Apply pagination
	query = query.Limit(limit).Offset(offset)

	err := query.Scan(ctx)
	return farmers, err
}
