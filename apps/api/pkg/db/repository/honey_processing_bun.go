package repository

import (
	"context"
	"fmt"
	"palmyra-pro-api/pkg/db/models"
	"time"

	"github.com/google/uuid"

	"github.com/uptrace/bun"
)

type honeyProcessingRepo struct {
	db *bun.DB
}

func NewHoneyProcessingRepo(db *bun.DB) HoneyProcessingRepo {
	return &honeyProcessingRepo{
		db: db,
	}
}

func (r *honeyProcessingRepo) Create(
	ctx context.Context,
	honeyProcessingLog *models.HoneyProcessingLog,
	bucketIds []string,
) (string, error) {
	tx, err := r.db.BeginTx(ctx, nil)
	if err != nil {
		return "", err
	}

	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	id := uuid.New().String()
	record := models.HoneyProcessingLog{
		ID:                    id,
		FactoryID:             honeyProcessingLog.FactoryID,
		ProcessTypeID:         honeyProcessingLog.ProcessTypeID,
		FactoryStaffLeadID:    honeyProcessingLog.FactoryStaffLeadID,
		WaxWeight:             honeyProcessingLog.WaxWeight,
		CentrifugeTemperature: honeyProcessingLog.CentrifugeTemperature,
		CentrifugeSpeed:       honeyProcessingLog.CentrifugeSpeed,
		HoneyTemperature:      honeyProcessingLog.HoneyTemperature,
		MeshSize:              honeyProcessingLog.MeshSize,
		PumpHopper:            honeyProcessingLog.PumpHopper,
		HoneyWeight:           honeyProcessingLog.HoneyWeight,
		BatchCode:             honeyProcessingLog.BatchCode,
		ProcessStartTS:        honeyProcessingLog.ProcessStartTS,
		ProcessEndTS:          honeyProcessingLog.ProcessEndTS,
	}

	_, err = tx.NewInsert().Model(&record).Exec(ctx)
	if err != nil {
		return "", fmt.Errorf("creating honey processing log: %w", err)
	}

	for _, bucketId := range bucketIds {
		bucketAssigned := models.HoneyProcessingLogBucket{
			BucketID:             bucketId,
			HoneyProcessingLogID: id,
		}
		_, err = tx.NewInsert().Model(&bucketAssigned).Exec(ctx)
		if err != nil {
			return "", fmt.Errorf("linking bucket [%s] to honey processing log [%s]: %w", bucketId, id, err)
		}
	}

	return id, err
}

func (r *honeyProcessingRepo) updateTx(
	ctx context.Context,
	id string,
	processingEndTS *time.Time, honeyWeight, waxWeight *float32, centrifugeTemperature *float32, centrifugeSpeed *float32, honeyTemperature *float32, meshSize *float32, pumpHopper *float32, buckets map[string]HoneyProcessingBucket,
) (found bool, err error) {
	tx, err := r.db.BeginTx(ctx, nil)
	if err != nil {
		return false, err
	}

	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	currentValue, err := r.GetById(ctx, id)
	if err != nil {
		return false, err
	}

	// Not found case
	if currentValue == nil {
		return false, nil
	}

	if len(currentValue.Buckets) != len(buckets) {
		return false, fmt.Errorf("number of buckets in the request does not match the number of buckets in the log")
	}

	// Update buckets.
	for bucketId, bucketFields := range buckets {
		_, err = tx.NewUpdate().
			Model(&models.Bucket{
				Id:                 bucketId, 
				VerifiedWeight:     bucketFields.VerifiedWeight,
				ContaminationStatus: bucketFields.ContaminationStatus,
			}).
			Column("verified_weight", "contamination_status").
			WherePK().
			Exec(ctx)

		if err != nil {
			return true, fmt.Errorf("updating bucket fields in bucket [%s] on log [%s]: %w", bucketId, id, err)
		}
	}

	newProcessEndTS := currentValue.ProcessEndTS
	if processingEndTS != nil {
		newProcessEndTS = processingEndTS
	}

	newWaxWeight := currentValue.WaxWeight
	if waxWeight != nil {
		newWaxWeight = *waxWeight
	}

	newCentrifugeTemperature := currentValue.CentrifugeTemperature
	if centrifugeTemperature != nil {
		newCentrifugeTemperature = *centrifugeTemperature
	}

	newCentrifugeSpeed := currentValue.CentrifugeSpeed
	if centrifugeSpeed != nil {
		newCentrifugeSpeed = *centrifugeSpeed
	}

	newHoneyTemperature := currentValue.HoneyTemperature
	if honeyTemperature != nil {
		newHoneyTemperature = *honeyTemperature
	}

	newMeshSize := currentValue.MeshSize
	if meshSize != nil {
		newMeshSize = *meshSize
	}

	newPumpHopper := currentValue.PumpHopper
	if pumpHopper != nil {
		newPumpHopper = *pumpHopper
	}

	newHoneyWeight := currentValue.HoneyWeight
	if honeyWeight != nil {
		newHoneyWeight = *honeyWeight
	}

	updatedValue := models.HoneyProcessingLog{
		ID:                    currentValue.ID,
		FactoryID:             currentValue.FactoryID,
		ProcessTypeID:         currentValue.ProcessTypeID,
		FactoryStaffLeadID:    currentValue.FactoryStaffLeadID,
		WaxWeight:             newWaxWeight,
		CentrifugeTemperature: newCentrifugeTemperature,
		CentrifugeSpeed:       newCentrifugeSpeed,
		HoneyTemperature:      newHoneyTemperature,
		MeshSize:              newMeshSize,
		PumpHopper:            newPumpHopper,
		HoneyWeight:           newHoneyWeight,
		BatchCode:             currentValue.BatchCode,
		ProcessStartTS:        currentValue.ProcessStartTS,
		ProcessEndTS:          newProcessEndTS,
	}

	_, err = tx.NewUpdate().Model(&updatedValue).WherePK().Exec(ctx)
	if err != nil {
		return true, fmt.Errorf("updating honey processing log: %w", err)
	}

	return true, nil
}

func (r *honeyProcessingRepo) Update(
	ctx context.Context,
	id string,
	processingEndTS *time.Time, honeyWeight, waxWeight *float32, centrifugeTemperature *float32, centrifugeSpeed *float32, honeyTemperature *float32, meshSize *float32, pumpHopper *float32,
	buckets map[string]HoneyProcessingBucket,
) (*models.HoneyProcessingLog, error) {

	found, err := r.updateTx(ctx, id, processingEndTS, honeyWeight, waxWeight, centrifugeTemperature, centrifugeSpeed, honeyTemperature, meshSize, pumpHopper, buckets)
	if err != nil {
		return nil, err
	}

	if !found {
		return nil, nil
	}

	return r.GetById(ctx, id)
}

func (r *honeyProcessingRepo) GetById(
	ctx context.Context,
	id string,
) (*models.HoneyProcessingLog, error) {
	var honeyProcessingLog models.HoneyProcessingLog
	err := r.db.NewSelect().Model(&honeyProcessingLog).
		Relation("Buckets").
		Relation("Buckets.Bucket").
		Relation("Buckets.Bucket.Transaction").
		Relation("Factory").
		Relation("ProcessType").
		Relation("FactoryStaffLead").
		Where("honey_processing_log.id = ?", id).
		Scan(ctx)

	if err != nil {
		return nil, err
	}

	return &honeyProcessingLog, nil
}

func (r *honeyProcessingRepo) QuickSearch(
	ctx context.Context,
	fuzzyFilter string, processStartTSFrom, processStartTSTo *time.Time, onlyOpen bool,
	descOrder bool,
	offset, limit int,
) ([]*models.HoneyProcessingLog, error) {
	var err error
	var logs []*models.HoneyProcessingLog

	query := r.db.NewSelect().Model(&logs).
		Relation("Buckets").
		Relation("Buckets.Bucket").
		Relation("Buckets.Bucket.Transaction").
		Relation("Factory").
		Relation("FactoryStaffLead").
		Relation("ProcessType").
		Relation("Buckets.Bucket")

	if len(fuzzyFilter) >= 3 {
		query = query.WhereOr(`honey_processing_log.batch_code ILIKE ? OR honey_processing_log.id ILIKE ?`, "%"+fuzzyFilter+"%", "%"+fuzzyFilter+"%")
	} else {
		query = query.WhereOr(`honey_processing_log.id ILIKE ?`, "%"+fuzzyFilter+"%")
	}

	if processStartTSFrom != nil {
		query.Where(`process_start_ts >= ?`, processStartTSFrom)
	}

	if processStartTSTo != nil {
		query.Where(`process_start_ts < ?`, processStartTSTo)
	}

	if onlyOpen {
		query.Where(`process_end_ts is null`)
	}

	if descOrder {
		query.Order(`process_start_ts DESC`)
	} else {
		query.Order(`process_start_ts ASC`)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	err = query.Scan(ctx)

	return logs, err

}

func (r *honeyProcessingRepo) GetDailySummaries(ctx context.Context) ([]*DailySummary, error) {
	var dailySummaries []*DailySummary

	// Query HoneyProcessingLogs and include the related Buckets
	err := r.db.NewRaw(`
		WITH DailySummary AS (
				SELECT
						hp.batch_code,
						hp.process_end_ts,
						DATE_TRUNC('day', b.harvest_date) AS day,
						SUM(b.weight) AS total_weight,
						COUNT(b.id) AS number_of_buckets,
						SUM(b.weight) - SUM(hp.wax_weight) AS estimated_honey_weight,
						SUM(hp.wax_weight) AS wax_produced
				FROM honey_processing_log hp
				JOIN honey_processing_log_buckets hpb ON hpb.honey_processing_log_id = hp.id
				JOIN bucket b ON b.id = hpb.bucket_id
				WHERE hp.process_start_ts IS NOT NULL
				GROUP BY hp.batch_code, hp.process_end_ts, day
		)
		SELECT
				ds.batch_code,
				ds.day,
				ds.process_end_ts,
				ds.total_weight,
				ds.number_of_buckets,
				ds.estimated_honey_weight,
				ds.wax_produced
		FROM DailySummary ds
		ORDER BY ds.day DESC;
	`).Scan(ctx, &dailySummaries)

	if err != nil {
		return nil, err
	}

	return dailySummaries, nil
}

func (r *honeyProcessingRepo) GetDailySummariesByBatchCode(
	ctx context.Context,
	batchCode string,
) ([]*DailySummary, error) {
	var dailySummaries []*DailySummary

	err := r.db.
		NewRaw(`
			SELECT
				hp.batch_code,
				hp.process_end_ts,
				DATE_TRUNC('day', b.harvest_date) AS day,
				SUM(b.weight) AS total_weight,
				COUNT(b.id) AS number_of_buckets,
				SUM(b.weight) - SUM(hp.wax_weight) AS estimated_honey_weight,
				SUM(hp.wax_weight) AS wax_produced
			FROM honey_processing_log hp
			JOIN honey_processing_log_buckets hpb ON hpb.honey_processing_log_id = hp.id
			JOIN bucket b ON b.id = hpb.bucket_id
			WHERE hp.BATCH_CODE = ?
			GROUP BY hp.batch_code, hp.process_end_ts, day
		`, batchCode,
		).
		Scan(ctx, &dailySummaries)

	if err != nil {
		return nil, err
	}

	return dailySummaries, nil
}

func (r *honeyProcessingRepo) GetSummaryByBatchCode(
	ctx context.Context,
	batchCode string,
) (Summary, error) {
	var summary Summary

	err := r.db.NewRaw(`
		SELECT
			BOOL_AND(hp.process_end_ts IS NOT NULL) AS is_processed,
			MIN(f.name) AS factory_location,
			MIN(hpt.name) AS process_type,
			MIN(hp.process_start_ts) AS start_process_date,
			MAX(hp.process_end_ts) AS end_process_date,
			SUM(hp.honey_weight) AS final_weight_of_honey,
			SUM(hp.wax_weight) AS final_weight_of_wax
		FROM honey_processing_log hp
		JOIN factory f ON f.id = hp.factory_id
		JOIN honey_process_type hpt ON hpt.id = hp.process_type_id
		WHERE hp.batch_code = ?
		GROUP BY hp.batch_code;
	`, batchCode).Scan(ctx, &summary)

	if err != nil {
		return Summary{}, err
	}

	return summary, nil
}
