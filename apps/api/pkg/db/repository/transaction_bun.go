package repository

import (
	"context"
	"fmt"
	"palmyra-pro-api/pkg/db/models"
	"time"

	"github.com/google/uuid"
	"github.com/uptrace/bun"
)

type transactionRepo struct {
	db *bun.DB
}

func NewTransactionRepo(db *bun.DB) TransactionRepo {
	return &transactionRepo{
		db: db,
	}
}

func (r *transactionRepo) Create(
	ctx context.Context,
	transaction *models.Transaction,
	buckets []*models.Bucket,
	transactionAssets []*models.TransactionAsset,
) (string, error) {
	tx, txErr := r.db.BeginTx(ctx, nil)

	if txErr != nil {
		return "", txErr
	}

	// Create transaction
	transactionId := uuid.New()
	transaction.Id = transactionId.String()

	_, transactionError := r.db.NewInsert().Model(transaction).Returning("id").Exec(ctx)

	if transactionError != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to create transaction: %w", transactionError)
	}

	// Create buckets
	for _, bucket := range buckets {
		id := uuid.New()
		bucket.Id = id.String()
		bucket.TransactionId = transactionId.String()
	}

	_, bucketErr := r.db.NewInsert().Model(&buckets).Exec(ctx)

	if bucketErr != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to create buckets: %w", bucketErr)
	}

	// Create transaction assets
	for _, transactionAsset := range transactionAssets {
		transactionAsset.TransactionId = transactionId.String()
	}

	_, transactionAssetErr := r.db.NewInsert().Model(&transactionAssets).Exec(ctx)

	if transactionAssetErr != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to create assets(beehives): %w", transactionAssetErr)
	}

	return transaction.Id, nil
}

func (r *transactionRepo) GetById(
	ctx context.Context,
	id string,
) (*models.Transaction, error) {
	transaction := new(models.Transaction)
	err := r.db.NewSelect().Model(transaction).
		Relation("Farmer").
		Relation("User").
		Relation("Buckets").
		Relation("Assets").
		Where("transaction.id = ?", id).
		Scan(ctx)

	if err != nil {
		return nil, err
	}
	return transaction, nil
}

func (r *transactionRepo) QuickSearch(
	ctx context.Context,
	fuzzyFilter string,
	createdAtFrom *time.Time,
	createdAtTo *time.Time,
	descOrder bool,
	offset int,
	limit int,
) ([]models.TransactionSearchResult, error) {
	var transactions []models.TransactionSearchResult

	sortColumn := "transaction.created_at"
	if descOrder {
		sortColumn += " DESC"
	} else {
		sortColumn += " ASC"
	}

	bucketSubquery := r.db.NewSelect().
		Table("bucket").
		ColumnExpr("transaction_id").
		ColumnExpr("SUM(weight) AS total_weight").
		ColumnExpr("COUNT(*) AS bucket_count").
		ColumnExpr("MIN(honey_type) AS honey_type").
		ColumnExpr("MIN(moisture_content) AS min_moisture_content").
		ColumnExpr("MAX(moisture_content) AS max_moisture_content").
		Group("transaction_id")

	query := r.db.NewSelect().
		Model(&transactions).
		ColumnExpr("MIN(transaction.id) AS id").
		ColumnExpr("MIN(transaction.created_at) AS created_at").
		ColumnExpr("MIN(transaction.price_per_kg) AS price_per_kg").
		ColumnExpr("MIN(b.total_weight) AS total_weight").
		ColumnExpr("MIN(b.bucket_count) AS bucket_count").
		ColumnExpr("MIN(b.honey_type) AS honey_type").
		ColumnExpr("MIN(b.min_moisture_content) AS min_moisture_content").
		ColumnExpr("MIN(b.max_moisture_content) AS min_moisture_content").
		ColumnExpr("MIN(b.max_moisture_content) AS max_moisture_content").
		ColumnExpr("MIN(r.name) AS region_name").
		ColumnExpr("MIN(c.name) AS chiefdom_name").
		ColumnExpr("MIN(z.name) AS zone_name").
		ColumnExpr("MIN(v.name) AS village_name").
		ColumnExpr(`MIN(f."firstName") AS farmer_name`).
		ColumnExpr(`MIN(f."lastName") AS farmer_last_name`).
		Join("LEFT JOIN (?) AS b ON b.transaction_id = transaction.id", bucketSubquery).
		Join("LEFT JOIN region AS r ON r.id = transaction.region_id").
		Join("LEFT JOIN region AS c ON c.id = transaction.chiefdom_id").
		Join("LEFT JOIN region AS z ON z.id = transaction.zone_id").
		Join("LEFT JOIN region AS v ON v.id = transaction.village_id").
		Join("LEFT JOIN farmer AS f ON f.id = transaction.farmer_id").
		Group("transaction.id").
		OrderExpr(sortColumn)

	if len(fuzzyFilter) >= 3 {
		query = query.Where("transaction.id::text ILIKE ?", "%"+fuzzyFilter+"%")
	}

	if createdAtFrom != nil {
		query = query.Where("transaction.created_at >= ?", createdAtFrom)
	}

	if createdAtTo != nil {
		query = query.Where("transaction.created_at < ?", createdAtTo)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	err := query.Scan(ctx)
	if err != nil {
		return nil, err
	}

	return transactions, nil
}

func (r *transactionRepo) GetTransactionsNotOnChain(
	ctx context.Context,
) ([]*models.Transaction, error) {
	transactions := make([]*models.Transaction, 0)
	err := r.db.NewSelect().
		Model(&transactions).
		Where("transaction.ipfs_hash IS NULL").
		Relation("Farmer").
		Relation("Buckets").
		Order("transaction.created_at DESC").
		Scan(ctx)

	if err != nil {
		return nil, err
	}
	return transactions, nil
}

func (r *transactionRepo) UpdateTransactionsWithHashes(
	ctx context.Context,
	ids []string,
	ipfsHash string,
	palmyraHash string,
) error {
	_, err := r.db.NewUpdate().
		Model(&models.Transaction{}).
		Set("ipfs_hash = ?, palmyra_hash = ?", ipfsHash, palmyraHash).
		Where("id IN (?)", bun.In(ids)).
		Exec(ctx)

	if err != nil {
		return err
	}
	return nil
}
