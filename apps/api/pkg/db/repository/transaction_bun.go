package repository

import (
	"context"
	"fmt"
	"palmyra-pro-api/pkg/db/models"
	"time"

	"github.com/google/uuid"
	"github.com/uptrace/bun"
)

type transactionRepo struct {
	db *bun.DB
}

func NewTransactionRepo(db *bun.DB) TransactionRepo {
	return &transactionRepo{
		db: db,
	}
}

func (r *transactionRepo) Create(
	ctx context.Context,
	transaction *models.Transaction,
	buckets []*models.Bucket,
	transactionAssets []*models.TransactionAsset,
) (string, error) {
	tx, txErr := r.db.BeginTx(ctx, nil)

	if txErr != nil {
		return "", txErr
	}

	// Create transaction
	transactionId := uuid.New()
	transaction.Id = transactionId.String()

	_, transactionError := r.db.NewInsert().Model(transaction).Returning("id").Exec(ctx)

	if transactionError != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to create transaction: %w", transactionError)
	}

	// Create buckets
	for _, bucket := range buckets {
		id := uuid.New()
		bucket.Id = id.String()
		bucket.TransactionId = transactionId.String()
	}

	_, bucketErr := r.db.NewInsert().Model(&buckets).Exec(ctx)

	if bucketErr != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to create buckets: %w", bucketErr)
	}

	// Create transaction assets
	for _, transactionAsset := range transactionAssets {
		transactionAsset.TransactionId = transactionId.String()
	}

	_, transactionAssetErr := r.db.NewInsert().Model(&transactionAssets).Exec(ctx)

	if transactionAssetErr != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to create assets(beehives): %w", transactionAssetErr)
	}

	return transaction.Id, nil
}

func (r *transactionRepo) GetById(
	ctx context.Context,
	id string,
) (*models.Transaction, error) {
	transaction := new(models.Transaction)
	err := r.db.NewSelect().Model(transaction).
		Relation("Farmer").
		Relation("User").
		Relation("Buckets").
		Relation("Assets").
		Where("transaction.id = ?", id).
		Scan(ctx)

	if err != nil {
		return nil, err
	}
	return transaction, nil
}

func (r *transactionRepo) QuickSearch(
	ctx context.Context,
	fuzzyFilter string,
	createdAtFrom *time.Time,
	createdAtTo *time.Time,
	descOrder bool,
	offset int,
	limit int,
) ([]models.TransactionSearchResult, error) {
	var transactions []models.TransactionSearchResult

	var sortColumn string
	if descOrder {
		sortColumn = "transaction.created_at DESC"
	} else {
		sortColumn = "transaction.created_at ASC"
	}

	query := r.db.NewSelect().Model(&transactions).
		Join("JOIN bucket ON bucket.transaction_id = transaction.id").
		Join("JOIN region as r ON r.id = transaction.region_id").
		Join("JOIN region as c ON c.id = transaction.chiefdom_id").
		Join("JOIN region as z ON z.id = transaction.zone_id").
		Join("JOIN region as v ON v.id = transaction.village_id").
		ColumnExpr("transaction.*, SUM(bucket.weight) AS total_weight").
		ColumnExpr("MIN(bucket.honey_type) AS honey_type").
		ColumnExpr("MIN(bucket.moisture_content) AS min_moisture_content").
		ColumnExpr("MAX(bucket.moisture_content) AS max_moisture_content").
		ColumnExpr("COUNT(bucket.id) AS bucket_count").
		ColumnExpr("MIN(r.name) AS region_name").
		ColumnExpr("MIN(c.name) AS chiefdom_name").
		ColumnExpr("MIN(z.name) AS zone_name").
		ColumnExpr("MIN(v.name) AS village_name")

	// FIXME: Use a real fuzzy search solution.
	if len(fuzzyFilter) >= 3 {
		query = query.WhereOr(`transaction.id ILIKE ?`, "%"+fuzzyFilter+"%")
	}

	if createdAtFrom != nil {
		query.Where("transaction.created_at >= ?", createdAtFrom)
	}

	if createdAtTo != nil {
		query.Where("transaction.created_at < ?", createdAtTo)
	}

	query = query.
		Group("transaction.id").
		Order(sortColumn)

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	err := query.Scan(ctx)

	if err != nil {
		return nil, err
	}
	return transactions, nil
}

func (r *transactionRepo) GetTransactionsNotOnChain(
	ctx context.Context,
) ([]*models.Transaction, error) {
	transactions := make([]*models.Transaction, 0)
	err := r.db.NewSelect().
		Model(&transactions).
		Where("transaction.ipfs_hash IS NULL").
		Relation("Farmer").
		Relation("Buckets").
		Order("transaction.created_at DESC").
		Scan(ctx)

	if err != nil {
		return nil, err
	}
	return transactions, nil
}

func (r *transactionRepo) UpdateTransactionsWithHashes(
	ctx context.Context,
	ids []string,
	ipfsHash string,
	palmyraHash string,
) error {
	_, err := r.db.NewUpdate().
		Model(&models.Transaction{}).
		Set("ipfs_hash = ?, palmyra_hash = ?", ipfsHash, palmyraHash).
		Where("id IN (?)", bun.In(ids)).
		Exec(ctx)

	if err != nil {
		return err
	}
	return nil
}
