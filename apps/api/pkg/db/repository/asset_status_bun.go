package repository

import (
	"context"
	"fmt"
	"github.com/google/uuid"
	"github.com/uptrace/bun"
	"palmyra-pro-api/pkg/db/models"
	"time"
)

type assetStatusRepo struct {
	db *bun.DB
}

func NewAssetStatusRepo(db *bun.DB) AssetStatusRepo {
	return &assetStatusRepo{
		db: db,
	}
}

func (a *assetStatusRepo) GetAssetStatusByID(ctx context.Context, id string) (*models.AssetStatus, error) {
	assetStatus := new(models.AssetStatus)
	err := a.db.NewSelect().
		Model(assetStatus).
		Relation("Asset").
		Relation("Farmer").
		Where("asset_status.id = ?", id).
		Scan(ctx)
	return assetStatus, err
}

func (a *assetStatusRepo) CreateAssetStatus(ctx context.Context, assetStatus models.AssetStatus) (string, error) {
	tx, err := a.db.BeginTx(ctx, nil)
	if err != nil {
		return "", err
	}

	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	// FIXME: We suppose that the Asset does not exists, but we need to review how to have a log.

	// Insert the Asset.
	if assetStatus.Asset == nil {
		err = fmt.Errorf("the asset related to the new asset status is nil")
		return "", err
	}

	now := time.Now()

	assetId := uuid.New().String()
	assetStatus.Asset.ID = assetId
	assetStatus.Asset.CreatedAt = now
	assetStatus.Asset.UpdatedAt = now

	assetStatusId := uuid.New().String()
	assetStatus.AssetId = assetId
	assetStatus.ID = assetStatusId
	assetStatus.CreatedAt = now
	assetStatus.UpdatedAt = now

	_, err = tx.NewInsert().Model(assetStatus.Asset).Exec(ctx)
	if err != nil {
		return "", err
	}

	_, err = tx.NewInsert().Model(&assetStatus).Exec(ctx)
	if err != nil {
		return "", err
	}

	return assetStatusId, err
}

func (a *assetStatusRepo) UpdateAssetStatus(ctx context.Context, assetStatus models.AssetStatus) error {
	// FIXME: We are going to update always the last and unique asset status, but we need to review how to have a log.

	// Insert the Asset.
	if assetStatus.Asset == nil {
		return fmt.Errorf("the asset related to the new asset status is nil")
	}

	currentAssetStatus, err := a.GetAssetStatusByID(ctx, assetStatus.ID)
	if err != nil {
		return err
	}

	tx, errTx := a.db.BeginTx(ctx, nil)
	if errTx != nil {
		return errTx
	}

	defer func() {
		if errTx != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	now := time.Now()

	assetStatus.Asset.CreatedAt = currentAssetStatus.Asset.CreatedAt
	assetStatus.Asset.UpdatedAt = now
	assetStatus.CreatedAt = currentAssetStatus.CreatedAt
	assetStatus.UpdatedAt = now

	_, errTx = tx.NewUpdate().Model(assetStatus.Asset).Where("id = ?", assetStatus.AssetId).Exec(ctx)
	if errTx != nil {
		return errTx
	}

	_, errTx = tx.NewUpdate().Model(&assetStatus).Where("id = ?", assetStatus.ID).Exec(ctx)
	if errTx != nil {
		return errTx
	}

	return errTx
}

func (a *assetStatusRepo) DeleteAssetStatus(ctx context.Context, id string) error {
	currentAssetStatus, err := a.GetAssetStatusByID(ctx, id)
	if err != nil {
		return err
	}

	if currentAssetStatus == nil {
		return fmt.Errorf("the asset status %s does not exist", id)
	}

	tx, errTx := a.db.BeginTx(ctx, nil)
	if errTx != nil {
		return errTx
	}

	defer func() {
		if errTx != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	now := time.Now()

	currentAssetStatus.Status = models.CntBeehiveStatusDeleted
	currentAssetStatus.Asset.UpdatedAt = now
	currentAssetStatus.UpdatedAt = now

	_, errTx = tx.NewUpdate().Model(currentAssetStatus.Asset).Where("id = ?", currentAssetStatus.AssetId).Exec(ctx)
	if errTx != nil {
		return errTx
	}

	_, errTx = tx.NewUpdate().Model(currentAssetStatus).Where("id = ?", currentAssetStatus.ID).Exec(ctx)
	if errTx != nil {
		return errTx
	}

	return errTx

}

// AssetsByFarmerId returns all Assets related to a farmer.
// ```sql
// SELECT
//
//	asset.*
//
// FROM asset, farmer, "businessFarm"
// WHERE farmer."farmId" = "businessFarm"."farmId"
//
//	AND asset."businessId" = "businessFarm"."businessId"
//	AND farmer.id = '5';
//
// ```
func (a *assetStatusRepo) Search(ctx context.Context, fuzzyFilter, farmerId, status *string, globalFilters map[string]interface{}, offset, limit int) ([]*models.AssetStatus, error) {
	var err error
	var assetStatuses []*models.AssetStatus

	// FIXME: We need to retrieve only the last update
	query := a.db.NewSelect().
		Model(&assetStatuses).
		Relation("Asset").
		Relation("Farmer")

	if fuzzyFilter != nil {
		query = query.WhereOr(` asset.name ILIKE ?`, "%"+*fuzzyFilter+"%")
	}

	if farmerId != nil {
		query = query.Where(`asset_status."farmerId" = ?`, *farmerId)
	}

	if status != nil {
		query = query.Where("asset_status.status = ?", *status)
	}

	// Apply filters
	for key, value := range globalFilters {
		query = query.Where("? = ?", bun.Ident(key), value)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	err = query.Scan(ctx, &assetStatuses)

	return assetStatuses, err
}
