package repository

import (
	"context"
	"palmyra-pro-api/pkg/db/models"

	"github.com/uptrace/bun"
)

type roleRepo struct {
	db *bun.DB
}

func NewRoleRepo(db *bun.DB) RoleRepo {
	return &roleRepo{
		db: db,
	}
}

func (r *roleRepo) GetAll(ctx context.Context, allowedRoles []string) ([]*models.Role, error) {
	var roles []*models.Role
	query := r.db.NewSelect().
		Model(&roles).
		Order("displayName asc")
		
	if len(allowedRoles) > 0{
		query.Where("id IN (?)", bun.In(allowedRoles))
	}

	err := query.Scan(ctx)

	if err != nil {
		return nil, err
	}

	return roles, nil
}
