package repository

import (
	"context"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"palmyra-pro-api/pkg/db/models"
	"testing"
)

func TestAssetsByFarmerIdRepo(t *testing.T) {
	db := setupTestConnection()
	repo := NewAssetStatusRepo(db)
	ctx := context.Background()

	statusActive := models.CntBeehiveStatusActive
	farmerId := "4"
	assetsStatus, err := repo.Search(ctx, nil, &farmerId, &statusActive, nil, 0, 100)
	require.NoError(t, err, "Failed searching assets by farmerId.")
	assert.Equal(t, 5, len(assetsStatus), "Looking for farmerId.")
	for i := range assetsStatus {
		assert.Equal(t, farmerId, assetsStatus[i].Farmer.ID, "Looking only for farmerId 4")
	}

	assetStatus, err := repo.GetAssetStatusByID(ctx, assetsStatus[0].ID)
	require.NoError(t, err, "Failed getting asset status by id.")
	assert.Equal(t, assetStatus.ID, assetsStatus[0].ID)

	fuzzyQuery := "hive B"
	assetsStatus, err = repo.Search(ctx, &fuzzyQuery, &farmerId, &statusActive, nil, 0, 100)
	require.NoError(t, err, "Failed searching assets by farmerId.")
	assert.Equal(t, 1, len(assetsStatus), "Looking for farmerId.")

	lat := float32(-14.842776862816425)
	lon := float32(26.204080839289265)
	bussinessId := "1"
	assetTypeId := "1"
	farmerId = "6"
	newEntry := models.AssetStatus{
		Status:    "active",
		Latitude:  &lat,
		Longitude: &lon,
		Metadata: map[string]any{
			models.CntBeehiveObservations: "Few observations here",
			models.CntBeehiveIsFunctional: true,
			models.CntBeehiveToBeRepaired: false,
			models.CntBeehiveOccupied:     true,
		},
		FarmerId: farmerId,
		Asset: &models.Asset{
			Name:        "New asset",
			AssetTypeID: &assetTypeId,
			BusinessID:  &bussinessId,
			CreatedBy:   nil,
		},
	}

	id, err := repo.CreateAssetStatus(ctx, newEntry)
	require.NoError(t, err, "Failed to create a new assets.")

	currentEntry, err := repo.GetAssetStatusByID(ctx, id)
	require.NoError(t, err, "Failed getting new asset status.")
	assert.Equal(t, currentEntry.ID, id)

	newEntry.ID = id
	newEntry.AssetId = currentEntry.AssetId
	newEntry.Asset.ID = currentEntry.Asset.ID
	currentEntry.Farmer = nil
	assert.EqualExportedValues(t, newEntry, *currentEntry)

	newEntry.Asset.Name = "New Name Updated"
	newEntry.Metadata[models.CntBeehiveOccupied] = false

	err = repo.UpdateAssetStatus(ctx, newEntry)
	require.NoError(t, err, "Failed to updating an asset status.")

	currentEntry, err = repo.GetAssetStatusByID(ctx, id)
	require.NoError(t, err, "Failed getting updated asset status.")
	assert.Equal(t, currentEntry.ID, id)

	currentEntry.Farmer = nil
	assert.EqualExportedValues(t, newEntry, *currentEntry)

	err = repo.DeleteAssetStatus(ctx, currentEntry.ID)
	require.NoError(t, err, "Failed to delete an asset status.")

	// farmers, err = repo.AssetsByFarmerId(ctx, "0", nil, 0, 100)
	// require.NoError(t, err, "Failed searching assets by farmerId.")
	// assert.Equal(t, 0, len(farmers), "Not found anything for asset for this farmer.")
	//
	// filters := map[string]interface{}{"assetTypeId": "2"}
	// farmers, err = repo.AssetsByFarmerId(ctx, "5", filters, 0, 100)
	// require.NoError(t, err, "Failed searching assets by farmerId.")
	// assert.Equal(t, 0, len(farmers), "Not found anything for asset for this farmer.")
	//
	// filters = map[string]interface{}{"assetTypeId": "1"}
	// farmers, err = repo.AssetsByFarmerId(ctx, "5", filters, 0, 100)
	// require.NoError(t, err, "Failed searching assets by farmerId.")
	// assert.Equal(t, 5, len(farmers), "Filter by asset type.")

	// farmers, err = repo.QuickSearchFarmers(ctx, "5", 0, 0)
	// require.NoError(t, err, "Failed searching farmers using fuzzy search.")
	// assert.Equal(t, 1, len(farmers), "1 will find one because it's searching by the code")

	// 	farmers, err = repo.QuickSearchFarmers(ctx, "Smith", 10, 0)
	// 	require.NoError(t, err, "Failed searching farmers using fuzzy search.")
	// 	assert.Equal(t, 2, len(farmers), "Expecting to find 2 farmers with name Smith")
}
