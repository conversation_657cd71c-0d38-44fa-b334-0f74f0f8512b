package repository

import (
	"context"
	"fmt"
	"palmyra-pro-api/pkg/db/models"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestHoneyProcessingRepo(t *testing.T) {
	db := setupTestConnection()
	repo := NewHoneyProcessingRepo(db)
	ctx := context.Background()

	startTime := time.Date(1975, time.April, 22, 0, 0, 0, 0, time.UTC)
	honeyProcessing := models.HoneyProcessingLog{
		FactoryID:             "1",
		ProcessTypeID:         "1",
		FactoryStaffLeadID:    "2",
		WaxWeight:             10,
		CentrifugeTemperature: 10,
		CentrifugeSpeed:       10,
		HoneyTemperature:      10,
		MeshSize:              10,
		PumpHopper:            10,
		HoneyWeight:           20,
		BatchCode:             "EP-SIM-L-25",
		ProcessStartTS:        startTime,
		ProcessEndTS:          nil,
	}

	honeyProcessingId, err := repo.Create(ctx, &honeyProcessing, []string{
		"B1",
		"B2",
	})
	require.NoError(t, err, "Failed inserting honey processing log.")

	honeyProcessingLog, err := repo.GetById(ctx, honeyProcessingId)
	require.NoError(t, err, "Failed getting honey processing log back.")
	assert.Equal(t, honeyProcessingId, honeyProcessingLog.ID)
	assert.Equal(t, 2, len(honeyProcessingLog.Buckets))
	assert.NotNil(t, honeyProcessingLog.Factory)
	assert.NotNil(t, honeyProcessingLog.ProcessType)

	honeyProcessingLogs, err := repo.QuickSearch(
		ctx,
		"",
		nil, nil, true, true, 0, 100,
	)

	require.NoError(t, err, "Failed getting honey processing log search.")
	// assert.Equal(t, 1, len(honeyProcessingLogs))
	for _, log := range honeyProcessingLogs {
		fmt.Println(log.Factory)
	}

	endTime := time.Date(1975, time.April, 23, 0, 0, 0, 0, time.UTC)
	honeyProcessingLog, err = repo.Update(
		ctx,
		honeyProcessingLog.ID,
		&endTime,
		nil, nil, nil, nil, nil, nil, nil,
		map[string]HoneyProcessingBucket{
			"B1": {
				VerifiedWeight:      10,
				ContaminationStatus: "clean",
			},
			"B2": {
				VerifiedWeight:      12,
				ContaminationStatus: "contaminated",
			},
		},
	)
	require.NoError(t, err, "Failed getting honey processing log back.")
	assert.NotNil(t, honeyProcessingLog.ProcessEndTS)
	assert.Equal(t, 2, len(honeyProcessingLog.Buckets))
	for _, bucket := range honeyProcessingLog.Buckets {
		assert.NotNil(t, bucket.Bucket)
		assert.Greaterf(t, bucket.Bucket.VerifiedWeight, float32(0), "Verified weight should be greater than 0")
	}

}
