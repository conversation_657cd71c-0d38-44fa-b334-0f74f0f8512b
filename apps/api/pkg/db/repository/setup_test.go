package repository

import (
	"github.com/uptrace/bun"
	"palmyra-pro-api/pkg/db"
)

func setupTestConnection() *bun.DB {

	// Create Repository connections.
	connection, err := db.NewDB(&db.Config{
		Name: "palmira_pro_db", Host: "localhost", Port: "5432", User: "postgres", Password: "mysecretpassword", SSLMode: "disable",
		Debug:        true,
		MaxOpenConn:  10,
		MaxIdleConns: 10,
	})

	if err != nil {
		panic(err)
	}

	// Create Bun ORM instance
	return connection
}
