package repository

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestRoleRepo(t *testing.T) {
	db := setupTestConnection()
	repo := NewRoleRepo(db)
	ctx := context.Background()
	var allowedRoles []string

	users, err := repo.GetAll(ctx, allowedRoles)

	require.NoError(t, err, "Failed getting all roles.")
	assert.Equal(t, 6, len(users))
	// fmt.Println(*users[0])
	// fmt.Println(users[0].DisplayName)
}
