package repository

import (
	"context"
	"palmyra-pro-api/pkg/db/models"

	"github.com/uptrace/bun"
)

type countryRepo struct {
	db *bun.DB
}

func NewCountryRepository(db *bun.DB) CountryRepo {
	return &countryRepo{db: db}
}

func (r *countryRepo) GetById(ctx context.Context, id string) (*models.Country, error) {
	var country models.Country
	err := r.db.NewSelect().
		Model(&country).
		Where("id = ?", id).
		Scan(ctx)

	if err != nil {
		return nil, err
	}

	return &country, nil
}