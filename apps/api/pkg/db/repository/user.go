package repository

import (
	"context"
	"palmyra-pro-api/pkg/db/models"
)

type UserRepo interface {
	SearchUsers(ctx context.Context, fuzzyFilter string, offset, limit int, roles []string) ([]*models.User, error)
	CreateUser(ctx context.Context, user *models.User) error
 	GetUserById(ctx context.Context, id string) (*models.User, error)
 	UpdateUser(ctx context.Context, user *models.User) error
 	DeleteUser(ctx context.Context, id string) error
}
