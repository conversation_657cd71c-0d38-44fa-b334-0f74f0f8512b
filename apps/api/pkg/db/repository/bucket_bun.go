package repository

import (
	"context"
	"palmyra-pro-api/pkg/db/models"

	"github.com/uptrace/bun"
)

type bucketRepo struct {
	db *bun.DB
}

func NewBucketRepo(db *bun.DB) BucketRepo {
	return &bucketRepo{
		db: db,
	}
}

func (r *bucketRepo) Search(
	ctx context.Context,
	regionId, chiefdomId, zoneId, villageId string,
	honeyType *string,
	fuzzyFilter string,
) ([]*models.Bucket, error) {
	buckets := make([]*models.Bucket, 0)
	query := r.db.NewSelect().
		Model(&buckets).
		Where("transaction.region_id = ?", regionId).
		Where("transaction.chiefdom_id = ?", chiefdomId).
		Where("transaction.zone_id = ?", zoneId).
		Where("transaction.village_id = ?", villageId).
		// Limit to buckets that are not yet linked to any honey processing log
		Where("NOT EXISTS (SELECT 1 FROM honey_processing_log_buckets WHERE honey_processing_log_buckets.bucket_id = bucket.id)")

	if honeyType != nil {
		query = query.Where("honey_type = ?", *honeyType)
	}

	if len(fuzzyFilter) >= 1 {
		query = query.WhereGroup(" AND ", func(q *bun.SelectQuery) *bun.SelectQuery {
			return q.Where(`"bucket_id" ILIKE ?`, "%"+fuzzyFilter+"%").
				WhereOr(`"transaction"."farmer_id" ILIKE ?`, "%"+fuzzyFilter+"%")
		})
	}

	err := query.Relation("Transaction.Farmer").Scan(ctx)

	if err != nil {
		return nil, err
	}

	return buckets, nil
}

func (r *bucketRepo) GetBucketsByBatchCode(
	ctx context.Context,
	batchCode string,
) ([]*models.Bucket, error) {
	buckets := make([]*models.Bucket, 0)
	err := r.db.NewSelect().
		Model(&buckets).
		Join("JOIN honey_processing_log_buckets hpb ON hpb.bucket_id = bucket.id").
		Join("JOIN honey_processing_log hpl ON hpl.id = hpb.honey_processing_log_id").
		Where("hpl.batch_code = ?", batchCode).
		Relation("Transaction.Farmer").
		Relation("Transaction.Assets.AssetStatus").
		Relation("Transaction.Assets.AssetStatus.Asset").
		Scan(ctx)

	if err != nil {
		return nil, err
	}

	return buckets, nil
}

func (r *bucketRepo) GetBucketById(
	ctx context.Context,
	id string,
) (*models.Bucket, error) {
	bucket := &models.Bucket{}
	err := r.db.NewSelect().
		Model(bucket).
		Where("bucket.id = ?", id).
		Relation("Transaction").
		Scan(ctx)

	if err != nil {
		return nil, err
	}

	return bucket, nil
}
