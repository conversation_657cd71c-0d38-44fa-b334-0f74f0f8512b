package repository

import (
	"context"
	"palmyra-pro-api/pkg/db/models"
)

type BucketRepo interface {
	Search(
		ctx context.Context,
		regionId, chiefdomId, zoneId, villageId string,
		honeyType *string,
		fuzzyFilter string,
	) ([]*models.Bucket, error)
	GetBucketsByBatchCode(ctx context.Context, batchCode string) ([]*models.Bucket, error)
	GetBucketById(ctx context.Context, id string) (*models.Bucket, error)
}
