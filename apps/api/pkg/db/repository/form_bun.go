package repository

import (
	"context"
	"github.com/google/uuid"
	"github.com/uptrace/bun"
	"palmyra-pro-api/pkg/db/models"
)

type formRepo struct {
	db *bun.DB
}

func NewFormRepo(db *bun.DB) FormRepo {
	return &formRepo{
		db: db,
	}
}

func (r *formRepo) CreateForm(ctx context.Context, form *models.Form) (string, error) {

	id := uuid.New()
	form.ID = id.String()

	_, err := r.db.NewInsert().Model(form).Returning("id").Exec(ctx)
	return form.ID, err
}

func (r *formRepo) GetFormByID(ctx context.Context, id string) (*models.Form, error) {
	form := new(models.Form)
	err := r.db.NewSelect().Model(form).Relation("CreatedByUser").Where("form.id = ?", id).Scan(ctx)
	return form, err
}

func (r *formRepo) UpdateForm(ctx context.Context, form *models.Form) error {
	_, err := r.db.NewUpdate().Model(form).Where("id = ?", form.ID).Exec(ctx)
	return err
}

func (r *formRepo) DeleteForm(ctx context.Context, id string) error {
	_, err := r.db.NewDelete().Model((*models.Form)(nil)).Where("id = ?", id).Exec(ctx)
	return err
}

func (r *formRepo) ListForms(ctx context.Context, filter map[string]interface{}, offset, limit int) ([]*models.Form, error) {
	var forms []*models.Form
	query := r.db.NewSelect().Model(&forms)

	// Apply filters
	for key, value := range filter {
		query = query.Where("? = ?", bun.Ident(key), value)
	}

	// Apply pagination
	query = query.Limit(limit).Offset(offset)

	err := query.Scan(ctx)
	return forms, err
}
