package repository

import (
	"context"
	"fmt"
	"palmyra-pro-api/pkg/db/models"
	"time"

	"github.com/uptrace/bun"
)

type userRepo struct {
	db *bun.DB
}

func NewUserRepo(db *bun.DB) UserRepo {
	return &userRepo{
		db: db,
	}
}

func (r *userRepo) SearchUsers(ctx context.Context, fuzzyFilter string, offset, limit int, roles []string) ([]*models.User, error) {
	var err error
	var users []*models.User

	query := r.db.NewSelect().
		Model(&users).
		Where("is_deleted = ?", false)

	if len(fuzzyFilter) > 0 {
		query = query.WhereOr(`first_name || ' ' || last_name ILIKE ? OR user.id = ?`, "%"+fuzzyFilter+"%", "%"+fuzzyFilter+"%", fuzzyFilter)
	}

	if len(roles) > 0 {
		query = query.Where("role IN (?)", bun.In(roles))
	}	

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	err = query.Scan(ctx, &users)
	return users, err
}


func (r *userRepo) CreateUser(ctx context.Context, user *models.User) error {
	_, err := r.db.NewInsert().Model(user).Exec(ctx)
	if err != nil {
		fmt.Printf("Error inserting user: %v", err)
	}
	return err
}

func (r *userRepo) GetUserById(ctx context.Context, id string) (*models.User, error) {
	user := new(models.User)
	err := r.db.NewSelect().Model(user).Where("id = ?", id).Scan(ctx)
	return user, err
}

func (r *userRepo) UpdateUser(ctx context.Context, user *models.User) error {
	_, err := r.db.NewUpdate().Model(user).Where("id = ?", user.ID).Exec(ctx)
	return err
}

func (r *userRepo) DeleteUser(ctx context.Context, id string) error {
	_, err := r.db.NewUpdate().
		Model((*models.User)(nil)).
		Set("is_deleted = ?", true).
		Set(`"updated_at" = ?`, time.Now()).
		Where("id = ?", id).
		Exec(ctx)
	return err
}