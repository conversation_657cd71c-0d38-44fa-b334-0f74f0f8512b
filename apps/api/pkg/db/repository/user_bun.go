package repository

import (
	"context"
	"palmyra-pro-api/pkg/db/models"

	"github.com/uptrace/bun"
)

type userRepo struct {
	db *bun.DB
}

func NewUserRepo(db *bun.DB) UserRepo {
	return &userRepo{
		db: db,
	}
}

func (r *userRepo) Search(
	ctx context.Context,
	fuzzyFilter *string,
	roles *[]string,
	offset,
	limit int,
) ([]*models.User, error) {

	var users []*models.User
	query := r.db.NewSelect().
		Model(&users).
		Relation("Role").
		Order("name asc")

	// FIXME: Use a real fuzzy search solution.
	if fuzzyFilter != nil {
		query = query.WhereOr(`"firstName" || ' ' || "lastName" ILIKE ? OR farmer_quick_search.id = ?`, "%"+*fuzzyFilter+"%", *fuzzyFilter)
	}

	if roles != nil && len(*roles) > 0 {
		query = query.Where("role.name IN (?)", bun.In(*roles))
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	err := query.Relation("Role").Scan(ctx)

	if err != nil {
		return nil, err
	}

	return users, nil
}
