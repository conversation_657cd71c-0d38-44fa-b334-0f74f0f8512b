package repository

import (
	"context"
	"palmyra-pro-api/pkg/db/models"
	"time"

	"github.com/uptrace/bun"
)

type formComplianceSubmissionRepo struct {
	db *bun.DB
}

func NewFormComplianceSubmissionRepo(db *bun.DB) FormComplianceSubmissionRepo {
	return &formComplianceSubmissionRepo{
		db: db,
	}
}

// QuickSearchForms search compliance submission forms.
// select u.id user_id, u.name user_name, f.id farmer_id, (f."firstName" || ' ' || f."lastName") famer_name,  form.*
// from form
// join "user" u on u.id = form."createdBy"
// join farmer f on f.id = (form.metadata ->> 'farmerId')::text
func (r *formComplianceSubmissionRepo) QuickSearchForms(
	ctx context.Context,
	fuzzyFilter string, CreatedAtFrom, CreatedAtTo *time.Time,
	descOrder bool,
	offset, limit int,
) ([]*models.FormComplianceSubmissionQuickSearch, error) {
	var err error
	var forms []*models.FormComplianceSubmissionQuickSearch

	query := r.db.NewSelect().
		Model(&forms).
		ColumnExpr(`
    u.id user_id, u.name user_name,
    f.id farmer_id, (f."firstName" || ' ' || f."lastName") farmer_name, f.gender farmer_gender,
    form.id, form.name, form.description, form.metadata, form."businessId",
	form."createdAt", form."updatedAt",
	r.name farmer_zone_name
`).
		Join(`join "user" u on u.id = form."createdBy"`).
		Join(`join farmer f on f.id = (form.metadata ->> 'farmerId')::text`).
		Join(`join region r on (r.level = 4 and f.zone_id = r.id)`)

	// FIXME: Use a real fuzzy search solution.
	if len(fuzzyFilter) >= 3 {
		query = query.WhereOr(`(form.id || ' ' || f."firstName" || ' ' || f."lastName" ) ILIKE ? OR f.id ILIKE ?`, "%"+fuzzyFilter+"%", fuzzyFilter, fuzzyFilter)
	} else {
		query = query.WhereOr(`f.id ILIKE ?`, "%"+fuzzyFilter+"%")
	}

	if CreatedAtFrom != nil {
		query.Where(`form."createdAt" >= ?`, CreatedAtFrom)
	}

	if CreatedAtTo != nil {
		query.Where(`form."createdAt" < ?`, CreatedAtTo)
	}

	if descOrder {
		query.Order(`form.createdAt DESC`)
	} else {
		query.Order(`form.createdAt ASC`)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	err = query.Scan(ctx, &forms)

	return forms, err
}
