package repository

import (
	"context"
	"palmyra-pro-api/pkg/db/models"
	"time"
)

type HoneyProcessingBucket struct {
	VerifiedWeight      float32
	ContaminationStatus string
}

type DailySummary struct {
	BatchCode            string     `json:"batch_code"`
	ProcessEndTS         *time.Time `json:"process_end_ts"`
	Day                  time.Time  `json:"day"`
	TotalWeight          float32    `json:"total_weight"`
	NumberOfBuckets      int        `json:"number_of_buckets"`
	EstimatedHoneyWeight float32    `json:"estimated_honey_weight"`
	WaxProduced          float32    `json:"wax_produced"`
}

type Summary struct {
	IsProcessed        bool      `json:"is_processed"`
	FactoryLocation    string    `json:"factory_location"`
	ProcessType        string    `json:"process_type"`
	StartProcessDate   time.Time `json:"start_process_date"`
	EndProcessDate     time.Time `json:"end_process_date"`
	FinalWeightOfHoney float32   `json:"final_weight_of_honey"`
	FinalWeightOfWax   float32   `json:"final_weight_of_wax"`
}

type HoneyProcessingRepo interface {
	Create(
		ctx context.Context,
		honeyProcessingLog *models.HoneyProcessingLog,
		bucketIds []string,
	) (string, error)

	Update(
		ctx context.Context,
		id string,
		processingEndTS *time.Time, honeyWeight, waxWeight *float32, centrifugeTemperature *float32, centrifugeSpeed *float32, honeyTemperature *float32, meshSize *float32, pumpHopper *float32,
		buckets map[string]HoneyProcessingBucket,
	) (*models.HoneyProcessingLog, error)

	GetById(ctx context.Context, id string) (*models.HoneyProcessingLog, error)

	QuickSearch(
		ctx context.Context,
		fuzzyFilter string, ProcessStartTSFrom, ProcessStartTSTo *time.Time, onlyOpen bool,
		descOrder bool,
		offset, limit int,
	) ([]*models.HoneyProcessingLog, error)

	GetDailySummaries(ctx context.Context) ([]*DailySummary, error)

	GetDailySummariesByBatchCode(ctx context.Context, batchCode string) ([]*DailySummary, error)

	GetSummaryByBatchCode(ctx context.Context, batchCode string) (Summary, error)
}
