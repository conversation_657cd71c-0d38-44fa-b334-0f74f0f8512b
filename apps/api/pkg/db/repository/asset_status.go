package repository

import (
	"context"
	"palmyra-pro-api/pkg/db/models"
)

type AssetStatusRepo interface {
	Search(ctx context.Context, fuzzyFilter, farmerId, status *string, globalFilters map[string]interface{}, offset, limit int) ([]*models.AssetStatus, error)
	CreateAssetStatus(ctx context.Context, assetStatus models.AssetStatus) (string, error)
	GetAssetStatusByID(ctx context.Context, id string) (*models.AssetStatus, error)
	UpdateAssetStatus(ctx context.Context, assetStatus models.AssetStatus) error
	DeleteAssetStatus(ctx context.Context, id string) error
}
