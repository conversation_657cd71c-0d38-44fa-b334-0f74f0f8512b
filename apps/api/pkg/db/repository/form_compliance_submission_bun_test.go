package repository

import (
	"context"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"testing"
)

func TestQuickSearchFormComplianceSubmissionRepo(t *testing.T) {
	db := setupTestConnection()
	repo := NewFormComplianceSubmissionRepo(db)
	ctx := context.Background()

	farmers, err := repo.QuickSearchForms(ctx, "Joe", nil, nil, true, 0, 0)
	require.NoError(t, err, "Failed searching farmers using fuzzy search.")
	assert.Greater(t, len(farmers), 0, "Joe does not apply to the filter because only 3 chars.")

	farmers, err = repo.QuickSearchForms(ctx, "5", nil, nil, true, 0, 0)
	require.NoError(t, err, "Failed searching farmers using fuzzy search.")
	assert.Equal(t, 1, len(farmers), "1 will find one because it's searching by the code")

	farmers, err = repo.QuickSearchForms(ctx, "Joe Montana", nil, nil, true, 0, 0)
	require.NoError(t, err, "Failed searching farmers using fuzzy search.")
	assert.Equal(t, 1, len(farmers), "1 will find one because it's searching by the code")

	farmers, err = repo.QuickSearchForms(ctx, "Smith", nil, nil, true, 0, 10)
	require.NoError(t, err, "Failed searching farmers using fuzzy search.")
	assert.Equal(t, 2, len(farmers), "Expecting to find 2 farmers with name Smith")
}
