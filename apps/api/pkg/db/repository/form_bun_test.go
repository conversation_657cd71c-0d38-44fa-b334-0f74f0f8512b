package repository

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"palmyra-pro-api/pkg/db/models"
)

func TestFormRepo(t *testing.T) {
	db := setupTestConnection()
	repo := NewFormRepo(db)
	ctx := context.Background()

	// Cleanup after test
	defer func() {
		_, _ = db.NewDelete().Model((*models.Form)(nil)).Where("id = ?", "test-form").Exec(ctx)
	}()

	// Test Create Form

	desc := "Secret Form XX"
	form := &models.Form{
		Name:        "FormXX",
		Description: &desc,
		Metadata:    nil,
		BusinessID:  "1",
		CreatedBy:   "817Hs1WZ9PwCkeRwipPOINTDIeYN_001",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	id, err := repo.CreateForm(ctx, form)
	require.NoError(t, err, "Failed to create form")

	// Test Get Form by ID
	retrievedForm, err := repo.GetFormByID(ctx, id)
	require.NoError(t, err, "Failed to fetch form by ID")
	assert.Equal(t, "FormXX", retrievedForm.Name, "Form Name mismatch")

	// Test Update Form
	form.ID = id
	form.Name = "FormYY"
	err = repo.UpdateForm(ctx, form)
	require.NoError(t, err, "Failed to update form")

	updatedForm, err := repo.GetFormByID(ctx, id)
	require.NoError(t, err, "Failed to fetch updated form")
	assert.Equal(t, "FormYY", updatedForm.Name, "Form name not updated")

	// Test Delete Form
	err = repo.DeleteForm(ctx, id)
	require.NoError(t, err, "Failed to delete form")

	deletedForm, err := repo.GetFormByID(ctx, id)
	assert.Error(t, err, "Expected error when fetching deleted form")
	assert.Nil(t, deletedForm, "Deleted form should be nil")
}
