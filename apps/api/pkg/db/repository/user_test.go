package repository

import (
	"context"
	"fmt"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"testing"
)

func TestUserRepo(t *testing.T) {
	db := setupTestConnection()
	repo := NewUserRepo(db)
	ctx := context.Background()

	users, err := repo.Search(ctx, nil, nil, 0, 100)
	require.NoError(t, err, "Failed getting users search.")
	assert.Equal(t, 15, len(users))

	users, err = repo.Search(ctx, nil, &[]string{"owner"}, 0, 100)
	require.NoError(t, err, "Failed getting users search.")
	assert.Equal(t, 2, len(users))
	fmt.Println(users[0].Role)

	users, err = repo.Search(ctx, nil, &[]string{"owner", "zone_lead_farmer"}, 0, 100)
	require.NoError(t, err, "Failed getting users search.")
	assert.Equal(t, 3, len(users))
	fmt.Println(users[0].Role)
}
