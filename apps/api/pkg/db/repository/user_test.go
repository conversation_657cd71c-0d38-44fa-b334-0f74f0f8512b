package repository

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestUserRepo(t *testing.T) {
	db := setupTestConnection()
	repo := NewUserRepo(db)
	ctx := context.Background()

	users, err := repo.SearchUsers(ctx, "<PERSON>", 0, 100, []string{"admin", "staff"})
	require.NoError(t, err, "Failed getting users search.")
	assert.Equal(t, 15, len(users))

	users, err = repo.SearchUsers(ctx, "5", 0, 100, []string{"admin", "staff"})
	require.NoError(t, err, "Failed getting users search.")
	assert.Equal(t, 2, len(users))

	users, err = repo.SearchUsers(ctx, "Smith", 0, 100, []string{"admin", "staff"})
	require.NoError(t, err, "Failed getting users search.")
	assert.Equal(t, 3, len(users))

	users, err = repo.SearchUsers(ctx, "", 0, 100, []string{"admin", "staff"})
	require.NoError(t, err, "Failed getting users by role.")
	assert.Equal(t, 19, len(users))
}
