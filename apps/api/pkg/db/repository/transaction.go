package repository

import (
	"context"
	"palmyra-pro-api/pkg/db/models"
	"time"
)

// FIXME: This should be call honeyHarvestingRepo to avoid confusion.

type TransactionRepo interface {
	Create(
		ctx context.Context,
		form *models.Transaction,
		buckets []*models.Bucket,
		transactionAssets []*models.TransactionAsset,
	) (string, error)
	GetById(ctx context.Context, id string) (*models.Transaction, error)
	QuickSearch(
		ctx context.Context,
		fuzzyFilter string,
		createdAtFrom *time.Time,
		createdAtTo *time.Time,
		descOrder bool,
		offset int,
		limit int,
	) ([]models.TransactionSearchResult, error)
	GetTransactionsNotOnChain(ctx context.Context) ([]*models.Transaction, error)
	UpdateTransactionsWithHashes(
		ctx context.Context,
		ids []string,
		ipfsHash string,
		palmyraHash string,
	) error
}
