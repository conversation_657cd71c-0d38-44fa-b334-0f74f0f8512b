package models

import (
	"github.com/uptrace/bun"
	"time"
)

type FormComplianceSubmissionQuickSearch struct {
	bun.BaseModel `bun:"table:form,alias:form"`

	ID                string                 `bun:"id,pk"`
	Name              string                 `bun:"name,notnull"`
	Description       *string                `bun:"description,nullzero"`
	FarmerId          string                 `bun:"farmer_id,nullzero"`
	FarmerName        string                 `bun:"farmer_name,nullzero""`
	FarmerGender      string                 `bun:"farmer_gender,nullzero""`
	CreatedById       string                 `bun:"user_id,nullzero"`
	CreatedByName     string                 `bun:"user_name,nullzero""`
	Metadata          map[string]interface{} `bun:"metadata,type:jsonb,default:{}"`
	BusinessID        string                 `bun:"businessId,nullzero"`
	FarmerVillageName string                 `bun:"farmer_village_name"`
	FarmerZoneName    string                 `bun:"farmer_zone_name"`
	CreatedAt         time.Time              `bun:"createdAt,default:current_timestamp"`
	UpdatedAt         time.Time              `bun:"updatedAt,default:current_timestamp"`
}
