package models

import (
	"time"

	"github.com/uptrace/bun"
)

type TransactionSearchResult struct {
	bun.BaseModel      `bun:"table:transaction,alias:transaction"`
	Id                 string    `bun:"id,pk"`
	FarmerID           string    `bun:"farmer_id,notnull"`
	SaleDate           time.Time `bun:"sale_date,notnull"`
	PricePerKg         float64   `bun:"price_per_kg,notnull"`
	HoneyType          string    `bun:"honey_type,notnull"`
	CreatedBy          string    `bun:"created_by,nullzero"`
	CreatedAt          time.Time `bun:"created_at,default:current_timestamp"`
	UpdatedAt          time.Time `bun:"updated_at,default:current_timestamp"`
	TotalWeight        float64   `bun:"total_weight"`
	BucketCount        int       `bun:"bucket_count"`
	MinMoistureContent float64   `bun:"min_moisture_content"`
	MaxMoistureContent float64   `bun:"max_moisture_content"`
	BatchCode          string    `bun:"batch_code,notnull"`
	RegionId           string    `bun:"region_id,notnull"`
	RegionName         string    `bun:"region_name,notnull"`
	ChiefdomId         string    `bun:"chiefdom_id,notnull"`
	ChiefdomName       string    `bun:"chiefdom_name,notnull"`
	ZoneId             string    `bun:"zone_id,notnull"`
	ZoneName           string    `bun:"zone_name,notnull"`
	VillageId          string    `bun:"village_id,notnull"`
	VillageName        string    `bun:"village_name,notnull"`
	IPFSHash           string    `bun:"ipfs_hash,nullzero"`
	PalmyraHash        string    `bun:"palmyra_hash,nullzero"`
	FarmerName         string    `bun:"farmer_name,notnull"`
	FarmerLastName     string    `bun:"farmer_last_name,notnull"`
}
