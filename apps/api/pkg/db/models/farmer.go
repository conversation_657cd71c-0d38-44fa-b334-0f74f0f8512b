package models

import (
	"time"

	"github.com/uptrace/bun"
)

type Farmer struct {
	bun.BaseModel `bun:"table:farmer"`

	ID                    string  `bun:"id,pk"`
	FirstName             string  `bun:"firstName,nullzero"`
	LastName              string  `bun:"lastName,nullzero"`
	Gender                string  `bun:"gender,nullzero"`
	Phone                 string  `bun:"phone,nullzero"`
	NRC                   string  `bun:"nrc,nullzero"`
	MaritalStatus         string  `bun:"maritalStatus,nullzero"`
	Dob                   *string `bun:"dob,nullzero"`
	HouseholdSize         string  `bun:"householdSize,nullzero"`
	EstimatedAnnualIncome float32 `bun:"estimatedAnnualIncome,nullzero"`
	SourceOfIncome        string  `bun:"sourceOfIncome,nullzero"`
	Role                  string  `bun:"role,notnull"`
	UserID                string  `bun:"userId,nullzero"`
	FarmID                string  `bun:"farmId,nullzero"`

	CountryID   string `bun:"countryId,nullzero"`
	CountryName string `json:"countryName" bun:"-"`

	RegionID   string `bun:"region_id,nullzero"`
	ChiefdomID string `bun:"chiefdom_id,nullzero"`
	ZoneID     string `bun:"zone_id,nullzero"`
	VillageID  string `bun:"village_id,nullzero"`

	Metadata  map[string]interface{} `bun:"metadata,type:jsonb,default:{}"`
	CreatedAt time.Time              `bun:"createdAt,default:current_timestamp"`
	UpdatedAt time.Time              `bun:"updatedAt,default:current_timestamp"`
	IsDeleted bool                   `bun:"is_deleted,default:false"`

	Country *Country `bun:"rel:belongs-to,join:countryId=id"`
}

type FarmerQuickSearch struct {
	bun.BaseModel         `bun:"table:farmer"`
	ID                    string                 `bun:"id,pk"`
	FirstName             string                 `bun:"firstName,nullzero"`
	LastName              string                 `bun:"lastName,nullzero"`
	Gender                string                 `bun:"gender,nullzero"`
	Phone                 string                 `bun:"phone,nullzero"`
	NRC                   string                 `bun:"nrc,nullzero"`
	MaritalStatus         string                 `bun:"maritalStatus,nullzero"`
	Dob                   *string                `bun:"dob,nullzero"`
	HouseholdSize         string                 `bun:"householdSize,nullzero"`
	EstimatedAnnualIncome float32                `bun:"estimatedAnnualIncome,nullzero"`
	SourceOfIncome        string                 `bun:"sourceOfIncome,nullzero"`
	Role                  string                 `bun:"role,notnull"`
	UserID                string                 `bun:"userId,nullzero"`
	FarmID                string                 `bun:"farmId,nullzero"`
	FarmName              string                 `bun:"farmName,nullzero"`
	Metadata              map[string]interface{} `bun:"metadata,type:jsonb,default:{}"`
	CreatedAt             time.Time              `bun:"createdAt,default:current_timestamp"`
	UpdatedAt             time.Time              `bun:"updatedAt,default:current_timestamp"`
	IsDeleted             bool                   `bun:"is_deleted,default:false"`

	CountryID   string `bun:"countryId,nullzero"`
	CountryName string `json:"countryName" bun:"-"`

	RegionID   string        `bun:"region_id,nullzero"`
	ChiefdomID string        `bun:"chiefdom_id,nullzero"`
	ZoneID     string        `bun:"zone_id,nullzero"`
	VillageID  string        `bun:"village_id,nullzero"`
	Beehives   []AssetStatus `bun:"rel:has-many,join:id=farmerId"`
}

type Farm struct {
	bun.BaseModel `bun:"table:farm"`

	ID        string    `bun:"id,pk"`
	Name      string    `bun:"name,notnull"`
	City      string    `bun:"city,notnull"`
	CountryID string    `bun:"countryId,nullzero"`
	Latitude  string    `bun:"latitude,nullzero"`
	Longitude string    `bun:"longitude,nullzero"`
	CreatedAt time.Time `bun:"createdAt,default:current_timestamp"`
	UpdatedAt time.Time `bun:"updatedAt,default:current_timestamp"`
}
