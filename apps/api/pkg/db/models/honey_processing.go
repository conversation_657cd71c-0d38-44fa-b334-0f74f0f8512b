package models

import (
	"time"

	"github.com/uptrace/bun"
)

type HoneyProcessingLog struct {
	bun.BaseModel `bun:"table:honey_processing_log"`

	ID                    string     `bun:"id,pk,type:text" json:"id"`
	FactoryID             string     `bun:"factory_id,notnull,type:text" json:"factory_id"`
	ProcessTypeID         string     `bun:"process_type_id,notnull,type:text" json:"process_type_id"`
	FactoryStaffLeadID    string     `bun:"factory_staff_lead_id,notnull,type:text" json:"factory_staff_lead_id"`
	WaxWeight             float32    `bun:"wax_weight,notnull,type:double precision" json:"wax_weight"`
	HoneyWeight           float32    `bun:"honey_weight,notnull,type:double precision" json:"honey_weight"`
	CentrifugeTemperature float32    `bun:"centrifuge_temperature,notnull,type:double precision" json:"centrifuge_temperature"`
	CentrifugeSpeed       float32    `bun:"centrifuge_speed,notnull,type:double precision" json:"centrifuge_speed"`
	HoneyTemperature      float32    `bun:"honey_temperature,notnull,type:double precision" json:"honey_temperature"`
	MeshSize              float32    `bun:"mesh_size,notnull,type:double precision" json:"mesh_size"`
	PumpHopper            float32    `bun:"pump_hopper,notnull,type:double precision" json:"pump_hopper"`
	BatchCode             string     `bun:"batch_code,notnull,type:text" json:"batch_code"`
	ProcessStartTS        time.Time  `bun:"process_start_ts,type:timestamp" json:"process_start_ts,omitempty"`
	ProcessEndTS          *time.Time `bun:"process_end_ts,type:timestamp" json:"process_end_ts,omitempty"`

	Factory          *Factory                   `bun:"rel:belongs-to,join:factory_id=id"`
	ProcessType      *HoneyProcessType          `bun:"rel:belongs-to,join:process_type_id=id"`
	Buckets          []HoneyProcessingLogBucket `bun:"rel:has-many,join:id=honey_processing_log_id"`
	FactoryStaffLead *User                      `bun:"rel:belongs-to,join:factory_staff_lead_id=id"`
}

type HoneyProcessingLogBucket struct {
	bun.BaseModel `bun:"table:honey_processing_log_buckets"`

	BucketID             string `bun:"bucket_id,pk,type:text" json:"bucket_id"`
	HoneyProcessingLogID string `bun:"honey_processing_log_id,pk,type:text" json:"honey_processing_log_id"`

	HoneyProcessingLog *HoneyProcessingLog `bun:"rel:belongs-to,join:honey_processing_log_id=id"`
	Bucket             *Bucket             `bun:"rel:belongs-to,join:bucket_id=id"`
}

type PostProcessingInventory struct {
	bun.BaseModel `bun:"table:post_processing_inventory"`

	ID        string  `bun:"id,pk,type:text" json:"id"`
	BatchCode string  `bun:"batch_code,notnull,type:text" json:"batch_code"`
	PourDate  string  `bun:"pour_date,notnull,type:timestamp" json:"pour_date"`
	Weight    float32 `bun:"weight,notnull,type:double precision" json:"weight"`
}
