package models

import (
	"time"

	"github.com/uptrace/bun"
)

type Role struct {
	bun.BaseModel `bun:"table:role"`

	ID          string                   `bun:"id,pk"`
	Name        string                   `bun:"name,notnull"`
	DisplayName string                   `bun:"displayName,notnull"`
	Permissions []map[string]interface{} `bun:"permissions,type:jsonb,default:{}"`
}

type User struct {
	bun.BaseModel `bun:"table:user"`

	ID            string                 `bun:"id,pk"`
	ExternalUID    string                `bun:"external_uid,unique,nullzero"`
	Username      string                 `bun:"username,nullzero"`
	Name          string                 `bun:"name,nullzero"`
	FirstName     string                 `bun:"first_name,nullzero"`
	LastName      string                 `bun:"last_name,nullzero"`
	Email         string                 `bun:"email,notnull,unique"`
	Phone         string                 `bun:"phone,nullzero"`
	Dob           string                 `bun:"dob,nullzero"`
	JobTitle      string                 `bun:"job_title,nullzero"`
	Photos        map[string]interface{} `bun:"photos,type:jsonb,default:{}"`
	Location      string                 `bun:"location,nullzero"`
	Latitude      string                 `bun:"latitude,nullzero"`
	Longitude     string                 `bun:"longitude,nullzero"`
	EmployerID    string                 `bun:"employerId,nullzero"`
	State         map[string]interface{} `bun:"state,type:jsonb,default:{}"`
	UpdatedAt     time.Time              `bun:"updated_at,default:current_timestamp"`
	IsOnboarded   bool                   `bun:"isOnboarded,notnull,default:false"`
	EmailVerified bool                   `bun:"emailVerified,notnull,default:false"`
	Image         string                 `bun:"image,nullzero"`
	CreatedAt     time.Time              `bun:"created_at,default:current_timestamp"`
	Role          string                 `bun:"role,notnull"`
	IsDeleted     bool                   `bun:"is_deleted,default:false"`
}
