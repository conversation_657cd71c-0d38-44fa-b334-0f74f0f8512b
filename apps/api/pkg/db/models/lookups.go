package models

import (
	"github.com/uptrace/bun"
)

type Factory struct {
	bun.BaseModel `bun:"table:factory"`

	ID         string `bun:",pk,type:text" json:"id"`
	Name       string `bun:"name,notnull,type:text" json:"name"`
	BusinessID string `bun:"business_id,notnull,type:text" json:"business_id"`

	Business *Business `bun:"rel:belongs-to,join:business_id=id"`
}

type HoneyProcessType struct {
	bun.BaseModel `bun:"table:honey_process_type"`

	ID   string `bun:"id,pk,type:text" json:"id"`
	Name string `bun:"name,notnull,type:text" json:"name"`
}

type Country struct {
	bun.BaseModel `bun:"table:country"`

	ID          string `bun:"id,pk,type:text" json:"id"`
	Code        string `bun:"code,unique,notnull,type:text" json:"code"`
	Name        string `bun:"name,notnull,type:text" json:"name"`
	DisplayName string `bun:"displayName,notnull,type:text" json:"displayName"`
}

type Business struct {
	bun.BaseModel `bun:"table:business"`

	ID          string  `bun:"id,pk,type:text" json:"id"`
	Name        string  `bun:"name,notnull,type:text" json:"name"`
	Industry    *string `bun:"industry,type:text" json:"industry,omitempty"`
	Description *string `bun:"description,type:text" json:"description,omitempty"`
	Bio         *string `bun:"bio,type:text" json:"bio,omitempty"`
	Photo       *string `bun:"photo,type:text" json:"photo,omitempty"`
	Zipcode     *string `bun:"zipcode,type:text" json:"zipcode,omitempty"`
	City        *string `bun:"city,type:text" json:"city,omitempty"`
	CountryID   *string `bun:"countryId,type:text" json:"countryId,omitempty"`
	OwnerID     *string `bun:"ownerId,type:text" json:"ownerId,omitempty"`
	CreatedAt   *string `bun:"createdAt,default:CURRENT_TIMESTAMP,type:timestamp" json:"createdAt,omitempty"`
	UpdatedAt   *string `bun:"updatedAt,default:CURRENT_TIMESTAMP,type:timestamp" json:"updatedAt,omitempty"`

	Country *Country `bun:"rel:belongs-to,join:country_id=id"`
}
