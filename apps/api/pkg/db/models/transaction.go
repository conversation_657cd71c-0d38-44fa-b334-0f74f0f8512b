package models

import (
	"time"

	"github.com/uptrace/bun"
)

type Transaction struct {
	bun.BaseModel `bun:"table:transaction"`
	Id            string             `bun:"id,pk"`
	RegionId      string             `bun:"region_id,notnull"`
	ChiefdomId    string             `bun:"chiefdom_id,notnull"`
	ZoneId        string             `bun:"zone_id,notnull"`
	VillageId     string             `bun:"village_id,notnull"`
	FarmerID      string             `bun:"farmer_id,notnull"`
	SaleDate      time.Time          `bun:"sale_date,notnull"`
	PricePerKg    float64            `bun:"price_per_kg,notnull"`
	CreatedBy     string             `bun:"created_by,nullzero"`
	IPFSHash      string             `bun:"ipfs_hash,nullzero"`
	PalmyraHash   string             `bun:"palmyra_hash,nullzero"`
	CreatedAt     time.Time          `bun:"created_at,default:current_timestamp"`
	UpdatedAt     time.Time          `bun:"updated_at,default:current_timestamp"`
	Farmer        *Farmer            `bun:"rel:belongs-to,join:farmer_id=id"`
	User          *User              `bun:"rel:belongs-to,join:created_by=id"`
	Buckets       []Bucket           `bun:"rel:has-many,join:id=transaction_id"`
	Assets        []TransactionAsset `bun:"rel:has-many,join:id=transaction_id"`
}
