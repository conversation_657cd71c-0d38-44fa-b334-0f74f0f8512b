package models

import (
	"time"

	"github.com/uptrace/bun"
)

type Bucket struct {
	bun.BaseModel      `bun:"table:bucket"`
	Id                 string                     `bun:"id,pk"`
	BucketId           string                     `bun:"bucket_id,notnull"`
	HarvestDate        time.Time                  `bun:"harvest_date,notnull"`
	MoistureContent    float32                    `bun:"moisture_content,notnull"`
	ContaminationStatus string                    `bun:"contamination_status,default:clean"`
	Weight             float32                    `bun:"weight,notnull"`
	VerifiedWeight     float32                    `bun:"verified_weight,notnull"`
	TransactionId      string                     `bun:"transaction_id,notnull"`
	HoneyType          string                     `bun:"honey_type,notnull"`
	Transaction        *Transaction               `bun:"rel:belongs-to,join:transaction_id=id"`
	CreatedAt          time.Time                  `bun:"created_at,default:current_timestamp"`
	UpdatedAt          time.Time                  `bun:"updated_at,default:current_timestamp"`
	HoneyProcessingLog []HoneyProcessingLogBucket `bun:"rel:has-many,join:id=bucket_id"`
}
