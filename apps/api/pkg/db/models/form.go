package models

import (
	"time"

	"github.com/uptrace/bun"
)

type Form struct {
	bun.BaseModel `bun:"table:form"`

	ID            string                 `bun:"id,pk"`
	Name          string                 `bun:"name,notnull"`
	Description   *string                `bun:"description,nullzero"`
	Metadata      map[string]interface{} `bun:"metadata,type:jsonb,default:{}"`
	BusinessID    string                 `bun:"businessId,nullzero"`
	CreatedBy     string                 `bun:"createdBy,nullzero"`
	CreatedAt     time.Time              `bun:"createdAt,default:current_timestamp"`
	UpdatedAt     time.Time              `bun:"updatedAt,default:current_timestamp"`
	CreatedByUser *User                  `bun:"rel:belongs-to,join:createdBy=id"`
}
