package models

import (
	"github.com/uptrace/bun"
	"time"
)

const (
	CntBeehiveOccupied      = "beehive_occupied"
	CntBeehiveToBeRepaired  = "beehive_repair"
	CntBeehiveIsFunctional  = "beehive_functional"
	CntBeehiveObservations  = "beehive_observation"
	CntBeehiveStatusActive  = "active"
	CntBeehiveStatusDeleted = "deleted"
)

// Asset represents the "asset" table in the database
type Asset struct {
	bun.BaseModel `bun:"table:asset"`

	ID   string `bun:"id,pk"`
	Name string `bun:"name,notnull"`
	// Status string `bun:"status,notnull"`
	// Latitude    *float32                `bun:"latitude,nullzero"`
	// Longitude   *float32                `bun:"longitude,nullzero"`
	// Metadata    map[string]interface{} `bun:"metadata,type:jsonb,default:{}"`
	AssetTypeID *string `bun:"assetTypeId,nullzero"`
	BusinessID  *string `bun:"businessId,nullzero"`

	CreatedBy *string   `bun:"createdBy,nullzero"`
	CreatedAt time.Time `bun:"createdAt,default:current_timestamp"`
	UpdatedAt time.Time `bun:"updatedAt,default:current_timestamp"`
}

type AssetStatus struct {
	bun.BaseModel `bun:"table:assetStatus,alias:asset_status"`

	ID        string                 `bun:"id,pk"`
	Status    string                 `bun:"status,notnull"`
	Latitude  *float32               `bun:"latitude,nullzero"`
	Longitude *float32               `bun:"longitude,nullzero"`
	Metadata  map[string]interface{} `bun:"metadata,type:jsonb,default:{}"`
	AssetId   string                 `bun:"assetId,nullzero"`
	FarmerId  string                 `bun:"farmerId,nullzero"`
	CreatedBy *string                `bun:"createdBy,nullzero"`
	CreatedAt time.Time              `bun:"createdAt,default:current_timestamp"`
	UpdatedAt time.Time              `bun:"updatedAt,default:current_timestamp"`

	Asset  *Asset  `bun:"rel:belongs-to,join:assetId=id"`
	Farmer *Farmer `bun:"rel:belongs-to,join:farmerId=id"`
}
