package client

import (
	"encoding/json"
	"fmt"
	"net/http"
)

type CheckResponse struct {
	ID             string         `json:"id"`
	Type           string         `json:"type"`
	Status         string         `json:"status"`
	Error          string         `json:"error"`
	TxID           string         `json:"txid"`
	AdditionalInfo AdditionalInfo `json:"additionalInfo"`
}

type AdditionalInfo struct {
	TokenName         string `json:"tokenName"`
	MetadataReference string `json:"metadataReference"`
}

func (c *Client) CheckTransactionById(id string) (*CheckResponse, error) {
	resp, err := c.httpClient.Get(fmt.Sprintf("%s/check/%s", c.baseURL, id))

	if err != nil {
		return nil, err
	}

	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status: %s", resp.Status)
	}

	var response CheckResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, err
	}

	return &response, nil
}
