package client

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
)

type PalmyraRequest struct {
	TokenName         string `json:"tokenName"`
	MetadataReference string `json:"metadataReference"`
}

type PalmyraResponse struct {
	Message string `json:"message"`
	ID      string `json:"id"`
}

func (c *Client) PalmyraTokenizeCommodity(ipfsUrl string, tokenName string) (*PalmyraResponse, error) {
	payload := PalmyraRequest{
		TokenName:         tokenName,
		MetadataReference: ipfsUrl,
	}

	jsonPayload, err := json.Marshal(payload)

	if err != nil {
		return nil, err
	}

	resp, err := c.httpClient.Post(fmt.Sprintf("%s/palmyra/tokenizeCommodity", c.baseURL), "application/json", bytes.NewBuffer(jsonPayload))

	if err != nil {
		return nil, err
	}

	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		return nil, fmt.Errorf("unexpected status: %s", resp.Status)
	}

	var response PalmyraResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, err
	}

	return &response, nil
}
