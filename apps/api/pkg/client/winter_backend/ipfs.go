package client

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"palmyra-pro-api/pkg/db/models"
	schemas "palmyra-pro-api/pkg/schemas/honey_harvesting"
	"time"
)

type IPFSResponse struct {
	Hash string `json:"hash"`
}

func (c *Client) UploadHoneyHarvestingToIPFS(transactions []models.Transaction) (*IPFSResponse, error) {
	events := make([]schemas.HoneyHarvestingV1Event, len(transactions))
	for i, transaction := range transactions {
		// In case of honey harvesting inputs and outputs are the same
		inputsOutputs := make([]schemas.HoneyHarvestingV1InputOutput, len(transaction.Buckets))
		for j, bucket := range transaction.Buckets {
			inputsOutputs[j] = schemas.HoneyHarvestingV1InputOutput{
				Id:             fmt.Sprintf("%s:1.0", bucket.Id),
				ItemDefinition: "zg:nnz:bucket:1.0",
				Properties: []schemas.HoneyHarvestingV1InputOutputProperties{
					{
						Quantity: bucket.Weight,
						Unit:     "kg",
					},
				},
			}
		}

		events[i] = schemas.HoneyHarvestingV1Event{
			EventType: "honeyHarvesting",
			EventTime: transaction.SaleDate,
			Inputs:    inputsOutputs,
			Outputs:   inputsOutputs,
			Properties: schemas.HoneyHarvestingV1Properties{
				TransactionID: transaction.Id,
				Sellers: []schemas.HoneyHarvestingV1Person{
					{
						Id:   transaction.Farmer.ID,
						Name: transaction.Farmer.FirstName + " " + transaction.Farmer.LastName,
					},
				},
				Buyers: []schemas.HoneyHarvestingV1Person{
					{
						Name: "Nature's Nectar Zambia",
					},
				},
			},
		}
	}

	body, err := json.Marshal(&schemas.HoneyHarvestingV1{
		SchemaVersion: "g:nnz:honeyHarvesting:0.1",
		LogTime:       time.Now(),
		Events:        events,
	})

	if err != nil {
		return nil, err
	}

	resp, err := c.httpClient.Post(fmt.Sprintf("%s/ipfs", c.baseURL), "application/json", bytes.NewBuffer(body))

	if err != nil {
		return nil, err
	}

	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		return nil, fmt.Errorf("unexpected status: %s", resp.Status)
	}

	var response IPFSResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, err
	}

	return &response, nil
}
