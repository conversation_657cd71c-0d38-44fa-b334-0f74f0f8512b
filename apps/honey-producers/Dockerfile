#FROM node:22 AS builder
#
#WORKDIR /app
#
#RUN npm install turbo@2.4.4
#RUN npm install next@14.2.25

# Build Stage
FROM node:22-alpine AS builder

WORKDIR /repo

# Copy monorepo files
COPY . .

ARG NEXT_PUBLIC_AUTH_URL
ENV NEXT_PUBLIC_AUTH_URL=$NEXT_PUBLIC_AUTH_URL

ARG NEXT_PUBLIC_BACKEND_URL
ENV NEXT_PUBLIC_BACKEND_URL=$NEXT_PUBLIC_BACKEND_URL

# Install dependencies (Turborepo-aware)
RUN npm ci

# Build only the Next.js app
RUN npm run build --workspace=apps/honey-producers

# Production Image
FROM node:22-alpine AS runner

WORKDIR /app

ENV NODE_ENV=production

# Copy only what's needed for production
COPY --from=builder /repo/apps/honey-producers/.next ./.next
COPY --from=builder /repo/apps/honey-producers/public ./public
COPY --from=builder /repo/apps/honey-producers/package.json ./package.json
COPY --from=builder /repo/apps/honey-producers/next.config.mjs ./next.config.mjs

# Copy shared deps (Next.js is in root)
COPY --from=builder /repo/node_modules ./node_modules
COPY --from=builder /repo/apps/honey-producers/node_modules ./node_modules

# Expose port
EXPOSE 3000

CMD ["npx", "next", "start"]
