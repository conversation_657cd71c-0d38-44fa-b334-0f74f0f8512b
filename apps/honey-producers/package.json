{"name": "honey-producers", "version": "0.1.0", "private": true, "scripts": {"dev": "npx next dev -p 8080", "build": "npx next build", "start": "npx next start -p 8080", "lint": "npx next lint"}, "dependencies": {"@hookform/resolvers": "^4.1.2", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@repo/api-specs": "*", "@repo/dummy": "*", "@repo/validation": "*", "@serwist/next": "^9.0.14", "@tanstack/react-query": "^5.66.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "firebase": "^11.10.0", "lucide-react": "^0.475.0", "next": "14.2.25", "next-themes": "^0.4.6", "openapi-fetch": "^0.13.4", "openapi-react-query": "^0.3.0", "qrcode.react": "^4.2.0", "react": "^18", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-hook-form": "^7.54.2", "sonner": "^2.0.3", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.25", "openapi-typescript": "^7.6.1", "postcss": "^8", "serwist": "^9.0.14", "tailwindcss": "^3.4.1", "typescript": "^5.8.2"}}