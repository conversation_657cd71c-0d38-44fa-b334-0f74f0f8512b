"use client"

import { useState } from "react"
import { getAllFromIDB, deleteFromIDB, clearIDBStore } from "@/lib/idb"
import api from "@/lib/api"

const STORE_NAMES = [
  "honey-harvesting",
  "farmersToCreate",
  "farmersToUpdate",
  "beehivesToCreate",
  "beehivesToUpdate",
] as const

export function useOfflineSyncManager() {
  const [hasPendingData, setHasPendingData] = useState(false)
  const [syncError, setSyncError] = useState<string | null>(null)
  const [isSyncing, setIsSyncing] = useState(false)
  const [hasJustSynced, setHasJustSynced] = useState(false)

  // React Query mutation to send data to the server
  const mutations = {
    "honey-harvesting": api.useMutation("post", "/honey-harvesting"),
    farmersToCreate: api.useMutation("post", "/farmers"),
    farmersToUpdate: api.useMutation("post", "/farmers/{id}"),
    beehivesToCreate: api.useMutation("post", "/beehives"),
    beehivesToUpdate: api.useMutation("post", "/beehives/{id}"),
  }

  // Check if any pending data in any store
  const checkPendingData = async () => {
    let pending = false
    for (const storeName of STORE_NAMES) {
      const saved = await getAllFromIDB(storeName)
      if (saved.length > 0) {
        pending = true
        break
      }
    }
    setHasPendingData(pending)
  }

  // Helper function to update IDs in objects
  const updateObjectIds = (
    obj: any,
    farmerIdMapping: Record<string, string>,
    beehiveIdMapping: Record<string, string>
  ) => {
    const updated = { ...obj }

    // Update farmer ID
    if (updated.farmerId && farmerIdMapping[updated.farmerId]) {
      updated.farmerId = farmerIdMapping[updated.farmerId]
    }

    // Update beehive ID (single)
    if (updated.beehiveId && beehiveIdMapping[updated.beehiveId]) {
      updated.beehiveId = beehiveIdMapping[updated.beehiveId]
    }

    // Update beehive IDs (array) - for farmers
    if (updated.beehiveIds && Array.isArray(updated.beehiveIds)) {
      updated.beehiveIds = updated.beehiveIds.map((id: string) => {
        if (beehiveIdMapping[id]) {
          return beehiveIdMapping[id]
        }
        return id
      })
    }

    // Update beehives array (for honey harvesting)
    if (updated.beehives && Array.isArray(updated.beehives)) {
      updated.beehives = updated.beehives.map((id: string) => {
        if (beehiveIdMapping[id]) {
          return beehiveIdMapping[id]
        }
        return id
      })
    }

    return updated
  }

  // Main sync function with proper error handling and rollback
  const syncData = async (): Promise<boolean> => {
    setIsSyncing(true)
    setSyncError(null)
    let syncedSomething = false

    // Store original data for potential rollback
    const originalData = {
      farmersToCreate: await getAllFromIDB<any>("farmersToCreate"),
      beehivesToCreate: await getAllFromIDB<any>("beehivesToCreate"),
      farmersToUpdate: await getAllFromIDB<any>("farmersToUpdate"),
      beehivesToUpdate: await getAllFromIDB<any>("beehivesToUpdate"),
      honeyHarvesting: await getAllFromIDB<any>("honey-harvesting"),
    }

    try {
      // Step 1: Sync farmers and collect ID mappings
      const farmerIdMapping: Record<string, string> = {}
      const syncedFarmerIds: string[] = []

      for (const farmer of originalData.farmersToCreate) {
        try {
          const response = await mutations.farmersToCreate.mutateAsync({
            body: farmer,
          })
          const realId = response.id

          if (realId) {
            farmerIdMapping[farmer.id] = realId
            syncedFarmerIds.push(farmer.id)
            await deleteFromIDB("farmersToCreate", farmer.id)
            syncedSomething = true
          } else {
            throw new Error("No real ID returned from server")
          }
        } catch (error: any) {
          console.error(`❌ Error syncing farmer ${farmer.id}:`, error)
          // Stop the entire sync process if farmers fail
          throw new Error(
            `Failed to sync farmer ${farmer.id}: ${error.message}`
          )
        }
      }

      // Step 2: Sync beehives with updated farmer IDs
      const beehiveIdMapping: Record<string, string> = {}
      const syncedBeehiveIds: string[] = []

      for (const beehive of originalData.beehivesToCreate) {
        try {
          // Update beehive with real farmer ID before syncing
          const updatedBeehive = updateObjectIds(beehive, farmerIdMapping, {})

          // Validate that farmer ID exists
          if (
            updatedBeehive.farmerId === beehive.farmerId &&
            farmerIdMapping[beehive.farmerId]
          ) {
            throw new Error(
              `Farmer ID mapping failed for beehive ${beehive.id}`
            )
          }

          const response = await mutations.beehivesToCreate.mutateAsync({
            body: updatedBeehive,
          })
          const realId = response.id

          if (realId) {
            beehiveIdMapping[beehive.id] = realId
            syncedBeehiveIds.push(beehive.id)
            await deleteFromIDB("beehivesToCreate", beehive.id)
            syncedSomething = true
          } else {
            throw new Error("No real ID returned from server")
          }
        } catch (error: any) {
          console.error(`❌ Error syncing beehive ${beehive.id}:`, error)
          throw new Error(
            `Failed to sync beehive ${beehive.id}: ${error.message}`
          )
        }
      }

      // Step 3: Sync farmers to update with real beehive IDs
      for (const farmer of originalData.farmersToUpdate) {
        try {
          const updatedFarmer = updateObjectIds(
            farmer,
            farmerIdMapping,
            beehiveIdMapping
          )

          await mutations.farmersToUpdate.mutateAsync({
            params: { path: { id: farmer.id } },
            body: updatedFarmer,
          })

          await deleteFromIDB("farmersToUpdate", farmer.id)
          syncedSomething = true
        } catch (error: any) {
          console.error(`❌ Error updating farmer ${farmer.id}:`, error)
          throw new Error(
            `Failed to update farmer ${farmer.id}: ${error.message}`
          )
        }
      }

      // Step 4: Sync beehive updates
      for (const beehive of originalData.beehivesToUpdate) {
        try {
          const updatedBeehive = updateObjectIds(
            beehive,
            farmerIdMapping,
            beehiveIdMapping
          )

          await mutations.beehivesToUpdate.mutateAsync({
            params: { path: { id: beehive.id } },
            body: updatedBeehive,
          })

          await deleteFromIDB("beehivesToUpdate", beehive.id)
          syncedSomething = true
        } catch (error: any) {
          console.error(`❌ Error updating beehive ${beehive.id}:`, error)
          throw new Error(
            `Failed to update beehive ${beehive.id}: ${error.message}`
          )
        }
      }

      // Step 5: Sync honey harvesting with all real IDs
      for (const record of originalData.honeyHarvesting) {
        try {
          const updatedRecord = updateObjectIds(
            record,
            farmerIdMapping,
            beehiveIdMapping
          )

          // Validate the data structure before sending
          if (!updatedRecord.farmerId) {
            throw new Error("Missing farmerId in honey harvesting record")
          }
          if (
            !updatedRecord.beehives ||
            !Array.isArray(updatedRecord.beehives) ||
            updatedRecord.beehives.length === 0
          ) {
            throw new Error(
              "Missing or invalid beehives array in honey harvesting record"
            )
          }

          const response = await mutations["honey-harvesting"].mutateAsync({
            body: updatedRecord,
          })
          await deleteFromIDB("honey-harvesting", record.id)
          syncedSomething = true
        } catch (error: any) {
          console.error(
            `❌ Error syncing honey harvesting ${record.id}:`,
            error
          )
          console.error(
            "❌ Error details:",
            error.response?.data || error.message
          )
          throw new Error(
            `Failed to sync honey harvesting ${record.id}: ${error.message}`
          )
        }
      }

      // Clear downloaded stores if sync was successful
      if (syncedSomething) {
        await clearIDBStore("farmersDownloaded")
        await clearIDBStore("beehivesDownloaded")
      }

      setHasJustSynced(true)
      setHasPendingData(false)

      setTimeout(() => setHasJustSynced(false), 2000)
    } catch (error: any) {
      console.error("❌ Sync failed, attempting rollback:", error)
      setSyncError(error.message || "Unknown error")
    } finally {
      setIsSyncing(false)
    }

    return syncedSomething
  }

  return {
    hasPendingData,
    syncData,
    isSyncing,
    syncError,
    hasJustSynced,
    setHasJustSynced,
    checkPendingData,
  }
}
