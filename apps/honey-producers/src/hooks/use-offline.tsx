"use client"

import { useState, useEffect } from "react"
import { toast } from "sonner"
import { notificationManager } from "@/lib/notification-manager"
import {
  getAllFromIDB,
  storeFarmersFromAPI,
  storeLocationsInIndexedDB,
  storeBeehivesFromAPI,
  STORE_NAMES,
  type Farmer,
  type Beehive,
  type Location,
} from "@/lib/idb"
import api from "@/lib/api"
import { useOfflineSyncManager } from "@/hooks/useSyncOfflineForms"

const OFFLINE_KEY = "app-offline-mode"

// Global state with persistence
let globalIsOffline = false

// Function to load state from localStorage
const loadOfflineState = () => {
  if (typeof window !== "undefined") {
    const saved = localStorage.getItem(OFFLINE_KEY)
    globalIsOffline = saved ? JSON.parse(saved) : false
  }
  return globalIsOffline
}

// Function to save state
const saveOfflineState = (isOffline: boolean) => {
  globalIsOffline = isOffline
  if (typeof window !== "undefined") {
    localStorage.setItem(OFFLINE_KEY, JSON.stringify(isOffline))
  }
}

export function useOffline() {
  const [isOffline, setIsOffline] = useState(() => loadOfflineState())
  const [farmers, setFarmers] = useState<Farmer[]>([])
  const [beehives, setBeehives] = useState<Beehive[]>([])
  const [locations, setLocations] = useState<Location[] | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const { syncData } = useOfflineSyncManager()

  const { data: farmersListOffline } = api.useQuery("get", "/farmers/offline")
  const { data: regionsTree } = api.useQuery("get", "/regions/tree")
  const { data: beehivesListOffline } = api.useQuery("get", "/beehives")

  const farmersList = farmersListOffline?.results?.map((farmer) => ({
    ...farmer,
    createdAt: farmer.createdAt || new Date().toISOString(),
    updatedAt: farmer.updatedAt || new Date().toISOString(),
  }))

  const beehivesList = beehivesListOffline?.results?.map((beehive) => ({
    ...beehive,
  }))

  const toggleOfflineMode = async () => {
    setIsLoading(true)

    try {
      if (!isOffline) {
        if (!farmersList || farmersList.length === 0) {
          const errorMsg = "No farmer data available for offline mode"
          toast.error(errorMsg)
          notificationManager.addNotification({
            type: "error",
            title: "Offline Mode Failed",
            description: errorMsg,
          })
          return
        }

        if (!beehivesList || beehivesList.length === 0) {
          const errorMsg = "No beehive data available for offline mode"
          toast.error(errorMsg)
          notificationManager.addNotification({
            type: "error",
            title: "Offline Mode Failed",
            description: errorMsg,
          })
          return
        }

        if (!regionsTree) {
          const errorMsg = "No region data available for offline mode"
          toast.error(errorMsg)
          notificationManager.addNotification({
            type: "error",
            title: "Offline Mode Failed",
            description: errorMsg,
          })
          return
        }

        const loadingToast = toast.loading("Preparing offline mode...", {
          description: "Downloading local data",
        })

        await storeFarmersFromAPI(farmersList)
        await storeBeehivesFromAPI(beehivesList)
        await storeLocationsInIndexedDB([regionsTree as Location])

        const downloadFarmers = await getAllFromIDB<Farmer>(
          STORE_NAMES.farmersDownloaded
        )
        const downloadBeehives = await getAllFromIDB<Beehive>(
          STORE_NAMES.beehivesDownloaded
        )
        const offlineLocations = await getAllFromIDB<Location>(
          STORE_NAMES.location
        )

        setFarmers(downloadFarmers)
        setBeehives(downloadBeehives)
        setLocations(offlineLocations)

        saveOfflineState(true)
        setIsOffline(true)

        toast.dismiss(loadingToast)
        const successMsg = `${downloadFarmers.length} farmers, ${downloadBeehives.length} beehives and ${offlineLocations.length} regions available`
        toast.success("Offline mode activated", {
          description: successMsg,
          duration: 4000,
        })

        notificationManager.addNotification({
          type: "success",
          title: "Offline Mode Activated",
          description: successMsg,
        })
      } else {
        const loadingToast = toast.loading("Synchronizing...", {
          description: "Sending data to server",
        })

        try {
          const synced = await syncData()
          setLocations(null)
          saveOfflineState(false)
          setIsOffline(false)

          toast.dismiss(loadingToast)

          const successMsg = synced
            ? "Data synchronized successfully"
            : "No data to synchronize"

          toast.success("Online mode activated", {
            description: successMsg,
            duration: 4000,
          })

          notificationManager.addNotification({
            type: "success",
            title: "Online Mode Activated",
            description: successMsg,
          })
        } catch (syncError) {
          toast.dismiss(loadingToast)
          const errorMsg = "Some data could not be synchronized"
          toast.error("Synchronization error", {
            description: errorMsg,
            action: {
              label: "Retry",
              onClick: () => syncData(),
            },
          })

          notificationManager.addNotification({
            type: "error",
            title: "Synchronization Error",
            description: errorMsg,
            action: {
              label: "Retry",
              onClick: () => syncData(),
            },
          })
          throw syncError
        }
      }
    } catch (error) {
      console.error("Error while toggling mode:", error)
      const errorMsg =
        error instanceof Error ? error.message : "An unexpected error occurred"
      toast.error("Error while toggling mode", {
        description: errorMsg,
      })

      notificationManager.addNotification({
        type: "error",
        title: "Mode Change Error",
        description: errorMsg,
      })
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    const handleOnline = () => {
      if (isOffline) {
        const infoMsg = "You can now synchronize your data"
        toast.info("Internet connection restored", {
          description: infoMsg,
          action: {
            label: "Synchronize",
            onClick: () => toggleOfflineMode(),
          },
        })

        notificationManager.addNotification({
          type: "info",
          title: "Connection Restored",
          description: infoMsg,
          action: {
            label: "Synchronize",
            onClick: () => toggleOfflineMode(),
          },
        })
      }
    }

    const handleOffline = () => {
      if (!isOffline) {
        const warningMsg = "Switch to offline mode to continue working"
        toast.warning("Internet connection lost", {
          description: warningMsg,
          action: {
            label: "Offline mode",
            onClick: () => toggleOfflineMode(),
          },
        })

        notificationManager.addNotification({
          type: "warning",
          title: "Connection Lost",
          description: warningMsg,
          action: {
            label: "Offline mode",
            onClick: () => toggleOfflineMode(),
          },
        })
      }
    }

    window.addEventListener("online", handleOnline)
    window.addEventListener("offline", handleOffline)

    return () => {
      window.removeEventListener("online", handleOnline)
      window.removeEventListener("offline", handleOffline)
    }
  }, [isOffline])

  return {
    isOffline,
    farmers,
    beehives,
    locations,
    isLoading,
    toggleOfflineMode,
    syncData,
  }
}

// Simple hook to just read the state
export function useOfflineStatus() {
  const [isOffline, setIsOffline] = useState(() => loadOfflineState())

  // Listen to localStorage changes
  useEffect(() => {
    const handleStorageChange = () => {
      const newState = loadOfflineState()
      setIsOffline(newState)
    }

    window.addEventListener("storage", handleStorageChange)

    // Check state on mount
    handleStorageChange()

    return () => window.removeEventListener("storage", handleStorageChange)
  }, [])

  return { isOffline }
}
