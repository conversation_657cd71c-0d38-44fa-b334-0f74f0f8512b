"use client"

import { FC } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useFormContext } from "react-hook-form"
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import type { ResetPasswordFormValues } from "@repo/validation/reset-password"

interface Props {
  onSubmit: (data: ResetPasswordFormValues) => Promise<void>
}

export const ResetPasswordForm: FC<Props> = ({ onSubmit }) => {
  const { control, handleSubmit } = useFormContext<ResetPasswordFormValues>()

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="px-6 space-y-6">
      <FormField
        control={control}
        name="password"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="font-medium">New Password</FormLabel>
            <FormControl>
              <Input type="password" placeholder="password" {...field} />
            </FormControl>
            <FormMessage className="text-xs" />
          </FormItem>
        )}
      />

      <FormField
        control={control}
        name="confirmPassword"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="font-medium">Confirm Password</FormLabel>
            <FormControl>
              <Input type="password" placeholder="password" {...field} />
            </FormControl>
            <FormMessage className="text-xs" />
          </FormItem>
        )}
      />

      <Button type="submit" className="w-full">
        Reset Password
      </Button>
    </form>
  )
}
