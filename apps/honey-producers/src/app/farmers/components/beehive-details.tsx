import React from "react"
import { <PERSON><PERSON><PERSON><PERSON>, Card, CardHeader } from "@/components/ui/card"
import { DetailsValue } from "@/components/details-value"
import { FormValues } from "@repo/validation/add-beehive"
import { ImageCarousel } from "@/components/image-carousel"
import { ArrowLeft, Edit } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useRouter } from "next/navigation"

export interface BeehiveDetailsProps {
  beehiveData: FormValues
  beehiveId: string
  farmerId: string
}

function StatusBadge({ status }: { status: string }) {
  return status === "active" ? (
    <Badge className="bg-emerald-100 text-emerald-800 hover:bg-emerald-200  max-sm:p-1">
      Active
    </Badge>
  ) : (
    <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-200  max-sm:p-1">
      Inactive
    </Badge>
  )
}

function OccupancyBadge({ isOccupied }: { isOccupied: boolean }) {
  return isOccupied === false ? (
    <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-200  max-sm:p-1">
      Vacant
    </Badge>
  ) : (
    <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200  max-sm:p-1">
      Occupied
    </Badge>
  )
}

function RepairBadge({ needsToBeRepaired }: { needsToBeRepaired: boolean }) {
  return needsToBeRepaired === true ? (
    <Badge className="bg-rose-100 text-rose-800 hover:bg-rose-200  max-sm:p-1">
      Repair Required
    </Badge>
  ) : (
    <Badge className="bg-background text-gray-600 hover:bg-gray-200 hover:text-gray-800  max-sm:p-1">
      No Repair Required
    </Badge>
  )
}

function FunctionalBadge({ isFunctional }: { isFunctional: boolean }) {
  return isFunctional === true ? (
    <Badge className="bg-green-100 text-green-800 hover:bg-green-200  max-sm:p-1">
      Functional
    </Badge>
  ) : (
    <Badge className="bg-background text-gray-600 hover:bg-gray-200 hover:text-gray-800  max-sm:p-1">
      Not Functional
    </Badge>
  )
}

const BeehiveDetails: React.FC<BeehiveDetailsProps> = ({
  beehiveData,
  beehiveId,
  farmerId,
}) => {
  const router = useRouter()
  return (
    <Card className="container bg-primary-foreground border shadow rounded-lg pt-6 p-2 md:p-8 max-w-3xl mx-auto">
      <div className="flex justify-between">
        <Button
          variant="outline"
          size="sm"
          className="h-8 w-8 p-0 inline-flex items-center"
          onClick={() =>
            router.push(`/farmers/farmer?tab=beehives#${farmerId}`)
          }
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="sm"
          className="h-8 w-8 p-0 inline-flex items-center"
          onClick={() =>
            router.push(
              `/farmers/farmer/beehive/edit#${farmerId}::${beehiveId}`
            )
          }
        >
          <Edit className="h-4 w-4" />
        </Button>
      </div>

      <CardHeader className="max-sm:p-2 space-y-4 mb-6 items-center p-0">
        <h1 className="text-2xl font-semibold text-center max-sm:text-xl">
          {beehiveData.name}
        </h1>
        <div className="max-sm:flex-wrap flex gap-2">
          <StatusBadge status={beehiveData.status} />
          <OccupancyBadge isOccupied={beehiveData.isOccupied} />
          <RepairBadge needsToBeRepaired={beehiveData.needsToBeRepaired} />
          <FunctionalBadge isFunctional={beehiveData.isFunctional} />
        </div>
      </CardHeader>

      <CardContent className="max-sm:p-2 space-y-6">
        <div className="bg-background border shadow rounded-lg p-6 grid grid-cols-2 gap-4">
          <DetailsValue label="Name" value={beehiveData.name} />
          <DetailsValue label="Status" value={beehiveData.status} />
          <DetailsValue label="Latitude" value={beehiveData.latitude} />
          <DetailsValue label="Longitude" value={beehiveData.longitude} />
          <DetailsValue
            label="Is the beehive functional ?"
            value={beehiveData.isFunctional ? "Yes" : "No"}
          />
          <DetailsValue
            label="Does the beehive need repairs ?"
            value={beehiveData.needsToBeRepaired ? "Yes" : "No"}
          />
          <DetailsValue
            label="Is the beehive occupied ?"
            value={beehiveData.isOccupied ? "Yes" : "No"}
          />
          <DetailsValue
            label="Observation"
            value={beehiveData.observation || "No observations"}
          />
          {/* Special handling for images */}
          <div className="col-span-2">
            <p className="text-xs uppercase text-muted-foreground mb-2">
              Images
            </p>
            {beehiveData.images && beehiveData.images.length > 0 ? (
              <ImageCarousel images={beehiveData.images} />
            ) : (
              <p className="text-muted-foreground">No images uploaded</p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default BeehiveDetails
