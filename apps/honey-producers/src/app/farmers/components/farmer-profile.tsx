"use client"

import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { Edit } from "lucide-react"
import { DetailsValue } from "@/components/details-value"
import { paths } from "@repo/api-specs"

interface FarmerDetailsProps {
  farmerData: paths["/farmers/{id}"]["get"]["responses"]["200"]["content"]["application/json"]
  farmerId: string
}

const FarmerProfile: React.FC<FarmerDetailsProps> = ({
  farmerData,
  farmerId,
}) => {
  const router = useRouter()
  return (
    <Card className="border border-gray-200 rounded-t-none">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <h3 className="text-lg font-semibold text-rose-600 flex items-center">
          <span className="h-2 w-2 rounded-full bg-rose-600 mr-2"></span>
          Info
        </h3>
        <Button
          variant="outline"
          size="sm"
          className="h-8 w-8 p-0"
          onClick={() => router.push(`/farmers/farmer/edit#${farmerId}`)}
        >
          <Edit className="h-4 w-4" />
        </Button>
      </CardHeader>
      <CardContent>
        <h3 className="text-base font-semibold mb-4">Personal Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <DetailsValue label="First Name" value={farmerData.firstName} />
          <DetailsValue label="Last Name" value={farmerData.lastName} />
          <DetailsValue label="Gender" value={farmerData.gender} />

          <DetailsValue label="Birth Date" value={farmerData.dob} />
          <DetailsValue label="Mobile Number" value={farmerData.phone} />
          <DetailsValue label="Nationality" value={farmerData.countryId} />

          <DetailsValue label="NRC Number" value={farmerData.nrc} />
          <DetailsValue
            label="Number of People in Household"
            value={farmerData.householdSize}
          />
          <DetailsValue
            label="Marital Status"
            value={farmerData.maritalStatus}
          />
        </div>
      </CardContent>
      <CardContent>
        <h3 className="text-base font-semibold mb-4">
          Professional Information & Location
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <DetailsValue label="Role" value={farmerData.roleDisplayName} />

          <DetailsValue label="Region" value={farmerData.regionId} />
          <DetailsValue label="Chiefdom" value={farmerData.chiefdomId} />
          <DetailsValue label="Zone" value={farmerData.zoneId} />
          <DetailsValue label="Village" value={farmerData.villageId} />
        </div>
      </CardContent>
    </Card>
  )
}

export default FarmerProfile
