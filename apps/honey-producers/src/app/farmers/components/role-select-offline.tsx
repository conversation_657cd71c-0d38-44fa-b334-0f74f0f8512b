"use client"

import React from "react"
import { useFormContext } from "react-hook-form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  FormControl,
  FormItem,
  FormLabel,
  FormMessage,
  FormField,
} from "@/components/ui/form"
import { FormValues } from "@repo/validation/add-farmer"

type Role = {
  id: string
  displayName: string
}

const offlineRoles: Role[] = [
  { id: "6", displayName: "Farmer" },
  { id: "5", displayName: "Zone Lead Farmer" },
]

export default function OfflineRoleSelector() {
  const { control } = useFormContext<FormValues>()
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <FormField
        name="role"
        control={control}
        render={({ field }) => (
          <FormItem>
            <FormLabel className="font-medium">
              Select a Role <span className="text-red-500">*</span>
            </FormLabel>
            <FormControl>
              <Select
                onValueChange={(value) =>
                  field.onChange(
                    offlineRoles.find((role) => role.id === value) ?? undefined
                  )
                }
                value={field.value?.id}
              >
                <SelectTrigger className="border-gray-300 bg-white">
                  <SelectValue placeholder="Select a role" />
                </SelectTrigger>
                <SelectContent>
                  {offlineRoles.map((role) => (
                    <SelectItem key={role.id} value={role.id}>
                      {role.displayName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormControl>
            <FormMessage className="text-xs" />
          </FormItem>
        )}
      />
    </div>
  )
}
