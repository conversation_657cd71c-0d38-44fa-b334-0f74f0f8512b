"use client"

import type React from "react"
import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Edit,
  MapPin,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Search,
  PlusCircle,
  Loader,
} from "lucide-react"
import { Input } from "@/components/ui/input"
import { useOffline } from "@/hooks/use-offline"

interface Beehive {
  id: string
  name: string
  status: string
  latitude: number
  longitude: number
  isFunctional: boolean
  needsToBeRepaired: boolean
  isOccupied: boolean
  observations?: string
  farmerId: string
}

interface BeehivesListProps {
  farmerId: string
  isLoading: boolean
  offlineBeehives: any[]
}

function getBeehiveColorClass(beehive: Beehive): string {
  if (!beehive.status) return "bg-amber-600"
  if (beehive.needsToBeRepaired) return "bg-rose-600"
  return "bg-emerald-600"
}

function getBeehiveIcon(beehive: Beehive) {
  if (!beehive.status) {
    return <XCircle className="h-6 w-6 text-white" />
  }
  if (beehive.needsToBeRepaired) {
    return <AlertTriangle className="h-6 w-6 text-white" />
  }
  return <CheckCircle className="h-6 w-6 text-white" />
}

function StatusBadge({ status }: { status: string }) {
  return status ? (
    <Badge className="bg-emerald-100 text-emerald-800 hover:bg-emerald-200 max-sm:hidden">
      Active
    </Badge>
  ) : (
    <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-200">
      Inactive
    </Badge>
  )
}

function OccupancyBadge({ isOccupied }: { isOccupied: boolean }) {
  return isOccupied ? (
    <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-200">
      Vacant
    </Badge>
  ) : (
    <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200">
      Occupied
    </Badge>
  )
}

function RepairBadge({ needsToBeRepaired }: { needsToBeRepaired: boolean }) {
  return needsToBeRepaired ? (
    <Badge className="bg-rose-100 text-rose-800 hover:bg-rose-200 max-sm:hidden">
      Repair Required
    </Badge>
  ) : (
    <Badge className="bg-background text-gray-600 hover:bg-gray-200 hover:text-gray-800 max-sm:hidden">
      No Repair Required
    </Badge>
  )
}

const BeehivesList: React.FC<BeehivesListProps> = ({
  farmerId,
  isLoading,
  offlineBeehives,
}) => {
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState("")
  const { isOffline } = useOffline()

  return (
    <div className="border bg-background border-gray-200 rounded-t-none rounded-lg p-6 space-y-4 max-sm:p-4">
      {/* Search and Add Button*/}
      <div className="flex flex-col sm:flex-row gap-2 sm:items-center">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search by beehive name"
            className="pl-10 bg-white"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            disabled={isOffline}
          />
        </div>
        <Button
          variant="default"
          onClick={() => router.push(`/farmers/farmer/create#${farmerId}`)}
        >
          <PlusCircle className="h-4 w-4" />
          Add Beehive
        </Button>
      </div>

      {/* Beehives list */}
      <Card className="border shadow rounded-lg">
        <CardContent className="p-0">
          {isLoading ? (
            <div className="flex justify-center items-center py-10">
              <Loader className="h-6 w-6 animate-spin text-primary" />
              <span className="ml-2">Loading records...</span>
            </div>
          ) : offlineBeehives && offlineBeehives.length > 0 ? (
            <ul className="divide-y divide-gray-100">
              {offlineBeehives.map((beehive) => (
                <li
                  key={beehive.id}
                  className="p-4 hover:bg-gray-50 transition-colors cursor-pointer max-sm:p-0"
                  onClick={() =>
                    router.push(
                      `/farmers/farmer/beehive#${farmerId}::${beehive.id}`
                    )
                  }
                >
                  <div className="flex items-center p-4 max-sm:p-2">
                    <div className="flex-shrink-0 mr-4 max-sm:mr-0">
                      <div
                        className={`w-10 h-10 rounded-full flex items-center justify-center ${getBeehiveColorClass(beehive)}`}
                      >
                        {getBeehiveIcon(beehive)}
                      </div>
                    </div>

                    <div className="flex-1 min-w-0 max-sm:flex-col max-sm:text-center">
                      <h3 className="text-lg font-semibold text-gray-900 max-sm:text-sm">
                        {beehive.name}
                      </h3>
                      <div className="flex max-sm:hidden items-center text-sm text-gray-500 mt-1">
                        <MapPin className="h-4 w-4 mr-1 flex-shrink-0" />
                        <span className="truncate">
                          Lat/Long: {beehive.latitude}, {beehive.longitude}
                        </span>
                      </div>
                      <div className="md:hidden">
                        <OccupancyBadge isOccupied={beehive.isOccupied} />
                      </div>
                    </div>

                    <div className="flex flex-wrap max-sm:hidden gap-2 ml-4">
                      <StatusBadge status={beehive.status} />
                      <OccupancyBadge isOccupied={beehive.isOccupied} />
                      <RepairBadge
                        needsToBeRepaired={beehive.needsToBeRepaired}
                      />
                    </div>

                    <div className="ml-4 flex-shrink-0 flex space-x-2 max-sm:flex-col max-sm:items-center max-sm:ml-0 max-sm:space-x-0">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-blue-600 hover:text-white hover:bg-blue-600"
                        onClick={(e) => {
                          e.stopPropagation()
                          router.push(
                            `/farmers/farmer/beehive/edit#${farmerId}::${beehive.id}`
                          )
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          ) : (
            <div className="p-8 text-center text-gray-500">
              No beehives found. Try a different search term.
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default BeehivesList
