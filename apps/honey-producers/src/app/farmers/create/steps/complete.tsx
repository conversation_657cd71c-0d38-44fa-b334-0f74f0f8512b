"use client"

import { FC } from "react"
import { Card, CardContent, CardTitle } from "@/components/ui/card"
import { NextButton } from "@/components/form/next-button"
import { CheckCircle, WifiOff, Wifi, AlertTriangle } from "lucide-react"
import { useOffline } from "@/hooks/use-offline"

export interface CompleteStepFarmerData {
  id: string
  firstName: string
  lastName: string
}

interface Props {
  handleNext: () => void
  isLast: boolean
  formPath?: string
  farmerData?: CompleteStepFarmerData
  isLoading?: boolean
}

export const CompleteStep: FC<Props> = ({
  handleNext,
  isLast,
  formPath,
  farmerData,
  isLoading,
}) => {
  const { isOffline } = useOffline()
  return (
    <div className="w-full mx-auto flex flex-col gap-6 items-center justify-center min-h-[calc(100vh-14rem)]">
      <Card className="w-full mx-auto bg-primary-foreground">
        <CardContent className="text-center">
          <div className="flex flex-col items-center justify-center pt-6">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <CheckCircle className="w-10 h-10 text-green-600" />
              <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-white rounded-full flex items-center justify-center border-2 border-green-100">
                {isOffline ? (
                  <WifiOff className="w-3 h-3 text-orange-500" />
                ) : (
                  <Wifi className="w-3 h-3 text-green-500" />
                )}
              </div>
            </div>

            <CardTitle className="text-2xl font-semibold mb-2">
              {isOffline
                ? "Farmer saved locally !"
                : "Farmer Added Successfully!"}
            </CardTitle>
            <p className="text-gray-500 mb-6">
              {farmerData?.firstName} {farmerData?.lastName} has been added to
              the system.
            </p>

            {isOffline ? (
              <div className="bg-orange-50 border border-yellow-200 rounded-lg p-4 w-full">
                <div className="flex justify-center items-center gap-2 mb-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-500" />
                  <h3 className="font-bold text-yellow-800">Offline mode</h3>
                </div>
                <p className="text-sm text-yellow-700">
                  Your data is stored locally and will automatically sync when
                  you're back online.
                </p>
              </div>
            ) : (
              <div className="bg-blue-500/20 rounded-lg p-4 mb-6 w-full">
                <h3 className="font-medium text-blue-800 mb-2">
                  Farmer ID: <span className="font-bold">{farmerData?.id}</span>
                </h3>
                <p className="text-sm text-blue-700">
                  This ID can be used to identify the farmer in the system.
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <div className="w-full">
        <NextButton
          isLast={isLast}
          handleNext={handleNext}
          formPath={formPath}
          isLoading={isLoading}
        />
      </div>
    </div>
  )
}
