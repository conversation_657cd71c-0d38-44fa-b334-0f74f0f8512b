"use client"

import { useState } from "react"
import { use<PERSON>orm, SubmitHandler, FormProvider } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { type FormValues, formSchema } from "@repo/validation/add-farmer"
import { AddFarmerForm, ConfirmStep, CompleteStep } from "./steps/"
import { Step, Stepper } from "@/components/form/stepper"
import { CompleteStepFarmerData } from "./steps/complete"
import api from "@/lib/api"
import { useOffline } from "@/hooks/use-offline"
import { saveToIDB } from "@/lib/idb"
import { useMutation } from "@tanstack/react-query"
import { generateUUID } from "@/lib/utils"

export default function AddFarmerPage() {
  const [step, setStep] = useState(1)
  const [farmerData, setFarmerData] = useState<CompleteStepFarmerData>()
  const [isLoading, setIsLoading] = useState(false)
  const { isOffline } = useOffline()

  const methods = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      gender: "",
      phone: "",
      country: "",
      nrc: "",
      householdSize: "",
      maritalStatus: "",
      sourceOfIncome: "",
      regionId: "",
      zoneId: "",
      chiefdomId: "",
      villageId: "",
      role: {},
    },
  })

  const mutation = api.useMutation("post", "/farmers", {
    onSuccess: (response) => {
      const firstName = methods.getValues("firstName")
      const lastName = methods.getValues("lastName")

      setFarmerData({
        id: response.id || "",
        firstName,
        lastName,
      })
      methods.reset()
      setStep(step + 1)

      setIsLoading(false)
    },
    onError: (e) => {
      console.log(e.message)

      setIsLoading(false)
    },
  })

  const offlineMutation = useMutation({
    mutationFn: async (payload: any) => {
      const offlinePayload = {
        id: generateUUID(),
        ...payload,
      }

      await saveToIDB("farmersToCreate", offlinePayload)
      await saveToIDB("farmersDownloaded", offlinePayload)

      return offlinePayload
    },
    onSuccess: (response) => {
      const firstName = methods.getValues("firstName")
      const lastName = methods.getValues("lastName")

      setFarmerData({
        id: response.id || "",
        firstName,
        lastName,
      })
      methods.reset()
      setStep(step + 1)
      setIsLoading(false)
    },
    onError: (error) => {
      console.error("Local save error:", error)
      setIsLoading(false)
    },
  })

  const stepComponents = [AddFarmerForm, ConfirmStep, CompleteStep]

  const stepSections = [
    { id: 1, label: "Add Farmer", range: [1, 1] },
    { id: 2, label: "Review", range: [2, 2] },
    { id: 3, label: "Finish", range: [3, 3] },
  ]

  const stepperState: Step[] = stepSections.map(({ id, label, range }) => {
    const [start, end] = range
    return {
      id,
      label,
      status:
        step > end
          ? "completed"
          : step >= start && step <= end
            ? "current"
            : "upcoming",
    }
  })

  const moveToNextStep = async () => {
    // Trigger validation before moving to the next step
    const isValid = await methods.trigger([
      "firstName",
      "lastName",
      "gender",
      "dob",
      "phone",
      "country",
      "nrc",
      "householdSize",
      "maritalStatus",
      "estimatedAnnualIncome",
      "sourceOfIncome",
      "regionId",
      "zoneId",
      "chiefdomId",
      "villageId",
      "role",
    ])

    if (!isValid) return

    setStep(step + 1)
  }

  const onSubmit: SubmitHandler<FormValues> = (data) => {
    setIsLoading(true)

    const payload = {
      firstName: data.firstName,
      lastName: data.lastName,
      gender: data.gender,
      dob: data.dob,
      phone: data.phone,
      countryId: data.country,
      nrc: data.nrc,
      householdSize: data.householdSize,
      maritalStatus: data.maritalStatus,
      estimatedAnnualIncome: data.estimatedAnnualIncome,
      sourceOfIncome: data.sourceOfIncome,
      regionId: data.regionId,
      zoneId: data.zoneId,
      chiefdomId: data.chiefdomId,
      villageId: data.villageId,
      roleId: data.role.id,
      roleDisplayName: data.role.displayName,
    }

    if (!isOffline) {
      mutation.mutate({ body: payload })
    } else {
      offlineMutation.mutate(payload)
    }
  }

  const handleEdit = (sectionId: number) => {
    const stepId = stepSections.find((section) => section.id === sectionId)
      ?.range[0]

    if (!stepId) return

    setStep(stepId)
  }

  const renderStep = () => {
    const CurrentStepComponent = stepComponents[step - 1]

    return (
      <CurrentStepComponent
        handleNext={moveToNextStep}
        isLast={step === stepComponents.length - 1}
        formPath={farmerData && `/farmers/farmer#${farmerData.id}`}
        handleEdit={handleEdit}
        farmerData={farmerData}
        isLoading={isLoading}
      />
    )
  }

  return (
    <FormProvider {...methods}>
      <div className="flex items-center flex-col md:mx-4">
        <Stepper
          steps={stepperState}
          progress={(step / stepComponents.length) * 100}
          backDisabled={!!farmerData}
        />
        <div className="w-full max-w-[800px]">
          <form onSubmit={methods.handleSubmit(onSubmit)}>{renderStep()}</form>
        </div>
      </div>
    </FormProvider>
  )
}
