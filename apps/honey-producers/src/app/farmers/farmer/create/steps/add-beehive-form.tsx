"use client"

import { useState, useRef, FC } from "react"
import { useFormContext } from "react-hook-form"
import { FormValues } from "@repo/validation/add-beehive"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
// import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
// import { ImagePlus, Loader } from "lucide-react"
// import { ImageCarousel } from "@/components/image-carousel"
import { NextButton } from "@/components/form/next-button"

interface Props {
  handleNext: () => void
  isLast: boolean
  formPath?: string
  isLoading?: boolean
}

export const AddBeehiveForm: FC<Props> = ({
  handleNext,
  isLast,
  formPath,
  isLoading,
}) => {
  const { control, setValue, watch } = useFormContext<FormValues>()
  const router = useRouter()

  // const [isUploading, setIsUploading] = useState(false)
  // const fileInputRef = useRef<HTMLInputElement>(null)

  // // Get the current images from the form state
  // const images = watch("images") || []

  // // Handle image upload
  // const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   const file = e.target.files?.[0]
  //   if (!file) return

  //   setIsUploading(true)

  //   // Create a preview URL
  //   const imageUrl = URL.createObjectURL(file)

  //   // Simulate a short upload delay to show loading state
  //   setTimeout(() => {
  //     // Add the new image URL to the form state
  //     const newImages = [...images, imageUrl]
  //     setValue("images", newImages, { shouldValidate: true })

  //     // Reset the input
  //     e.target.value = ""

  //     setIsUploading(false)
  //   }, 1000)
  // }

  // // Handle image removal
  // const handleRemoveImage = (index: number) => {
  //   // Get current images
  //   const currentImages = images

  //   // If the image has a URL created with URL.createObjectURL, revoke it to free memory
  //   if (
  //     typeof currentImages[index] === "string" &&
  //     currentImages[index].startsWith("blob:")
  //   ) {
  //     URL.revokeObjectURL(currentImages[index])
  //   }

  //   // Remove the image URL from the form state
  //   const newImages = [...currentImages]
  //   newImages.splice(index, 1)
  //   setValue("images", newImages, { shouldValidate: true })
  // }

  return (
    <div>
      <Card className="w-full max-w-4xl mx-auto bg-primary-foreground">
        <CardHeader className="flex flex-row justify-between">
          <CardTitle className="text-base font-bold">Add a Beehive</CardTitle>
        </CardHeader>

        <CardContent className="mb-6">
          <div className="grid gap-6">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <FormField
                control={control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">
                      Beehive Name <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="ex: Beehive A"
                        className="border-gray-200 bg-background"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">
                      Status <span className="text-red-500">*</span>
                    </FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="border-gray-200 bg-background">
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <FormField
                control={control}
                name="latitude"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">
                      Latitude <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="ex: 5"
                        className="border-gray-200 bg-background"
                        {...field}
                        value={field.value ?? ""}
                        onChange={(e) => {
                          const value =
                            e.target.value === ""
                              ? undefined
                              : Number(e.target.value)
                          field.onChange(value)
                        }}
                      />
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="longitude"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">
                      Longitude <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="ex: 5"
                        className="border-gray-200 bg-background"
                        {...field}
                        value={field.value ?? ""}
                        onChange={(e) => {
                          const value =
                            e.target.value === ""
                              ? undefined
                              : Number(e.target.value)
                          field.onChange(value)
                        }}
                      />
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />
            </div>

            <div className="space-y-3">
              <FormField
                control={control}
                name="isFunctional"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormLabel>
                      Is the beehive functional ?{" "}
                      <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={(value) =>
                          field.onChange(value === "true")
                        }
                        value={field.value?.toString()}
                        className="flex flex-col space-y-1 md:flex-row md:space-y-0 md:space-x-4"
                      >
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="true" />
                          </FormControl>
                          <FormLabel className="font-normal">Yes</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="false" />
                          </FormControl>
                          <FormLabel className="font-normal">No</FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="space-y-3">
              <FormField
                control={control}
                name="needsToBeRepaired"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormLabel>
                      Does the beehive need repairs ?{" "}
                      <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={(value) =>
                          field.onChange(value === "true")
                        }
                        value={field.value?.toString()}
                        className="flex flex-col space-y-1 md:flex-row md:space-y-0 md:space-x-4"
                      >
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="true" />
                          </FormControl>
                          <FormLabel className="font-normal">Yes</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="false" />
                          </FormControl>
                          <FormLabel className="font-normal">No</FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="space-y-3">
              <FormField
                control={control}
                name="isOccupied"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormLabel>
                      Is the beehive occupied ?{" "}
                      <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={(value) =>
                          field.onChange(value === "true")
                        }
                        value={field.value?.toString()}
                        className="flex flex-col space-y-1 md:flex-row md:space-y-0 md:space-x-4"
                      >
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="true" />
                          </FormControl>
                          <FormLabel className="font-normal">Yes</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="false" />
                          </FormControl>
                          <FormLabel className="font-normal">No</FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="space-y-2">
              <FormField
                control={control}
                name="observation"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormLabel>Observation</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter your observations here..."
                        className="border-gray-200 bg-background min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* <div className="space-y-3">
              <FormField
                control={control}
                name="images"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <div className="flex justify-between items-center">
                      <Label>Images</Label>
                      <div>
                        <input
                          type="file"
                          id="image-upload"
                          accept="image/*"
                          className="hidden"
                          ref={fileInputRef}
                          onChange={handleImageUpload}
                        />
                        <Button
                          type="button"
                          variant="default"
                          size="sm"
                          onClick={() => fileInputRef.current?.click()}
                          disabled={isUploading}
                        >
                          {isUploading ? (
                            <>
                              <span className="animate-spin mr-2">
                                <Loader />
                              </span>
                              Uploading...
                            </>
                          ) : (
                            <>
                              <ImagePlus className="h-4 w-4 mr-2" />
                              Add Images
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                    <FormControl>
                      <div>
                        <ImageCarousel
                          images={field.value || []}
                          onRemoveImage={handleRemoveImage}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div> */}
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-2">
        <Button
          variant="outline"
          onClick={() => router.push(`/farmers`)}
          className="w-full mt-4"
        >
          Cancel
        </Button>
        <NextButton
          isLast={isLast}
          handleNext={handleNext}
          formPath={formPath}
          isLoading={isLoading}
        />
      </div>
    </div>
  )
}
