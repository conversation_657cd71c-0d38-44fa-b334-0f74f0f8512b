"use client"

import { FC } from "react"
import { Card, CardContent, CardTitle } from "@/components/ui/card"
import { NextButton } from "@/components/form/next-button"
import { CheckCircle } from "lucide-react"

export interface CompleteStepBeehiveData {
  id: string
}

interface Props {
  handleNext: () => void
  isLast: boolean
  formPath?: string
  beehiveData?: CompleteStepBeehiveData
  isLoading?: boolean
}

export const CompleteStep: FC<Props> = ({
  handleNext,
  isLast,
  formPath,
  beehiveData,
  isLoading,
}) => {
  return (
    <div className="w-full mx-auto flex flex-col gap-6 items-center justify-center min-h-[calc(100vh-14rem)]">
      <Card className="w-full mx-auto bg-primary-foreground">
        <CardContent className="text-center">
          <div className="flex flex-col items-center justify-center pt-6">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <CheckCircle className="w-10 h-10 text-green-600" />
            </div>

            <CardTitle className="text-2xl font-semibold mb-2">
              Beehive Added Successfully!
            </CardTitle>
            <p className="text-gray-500 mb-6">
              A Beehive has been added to the system.
            </p>

            <div className="bg-blue-500/20 rounded-lg p-4 mb-6 w-full">
              <h3 className="font-medium text-blue-800 mb-2">
                Beehive ID: <span className="font-bold">{beehiveData?.id}</span>
              </h3>
              <p className="text-sm text-blue-700">
                This ID can be used to identify the beehive in the system.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="w-full">
        <NextButton
          isLast={isLast}
          handleNext={handleNext}
          formPath={formPath}
          isLoading={isLoading}
        />
      </div>
    </div>
  )
}
