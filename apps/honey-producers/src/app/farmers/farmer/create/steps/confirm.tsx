"use client"

import type { <PERSON> } from "react"
import { useFormContext } from "react-hook-form"
import type { FormValues } from "@repo/validation/add-beehive"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { NextButton } from "@/components/form/next-button"
// import { ImageCarousel } from "@/components/image-carousel"
import { DetailsValue } from "@/components/details-value"
import { Pencil } from "lucide-react"

interface Props {
  handleNext: () => void
  isLast: boolean
  formPath?: string
  handleEdit: (sectionId: number) => void
  isLoading?: boolean
}

export const ConfirmStep: FC<Props> = ({
  handleNext,
  isLast,
  formPath,
  handleEdit,
  isLoading,
}) => {
  const { getValues } = useFormContext<FormValues>()
  const beehiveData = getValues()

  return (
    <div className="space-y-4">
      <Card className="bg-primary-foreground border">
        <CardHeader className="flex flex-row justify-between items-center max-sm:p-4">
          <CardTitle>Beehive Summary</CardTitle>
          <div
            role="button"
            className="h-7 w-7 rounded-md hover:bg-accent hover:text-accent-foreground p-1"
            onClick={(e) => {
              e.stopPropagation()
              handleEdit(1)
            }}
          >
            <Pencil className="h-4 w-4" />
          </div>
        </CardHeader>
        <CardContent className="max-sm:p-2 space-y-6">
          <div className="bg-background border shadow rounded-lg p-6 grid grid-cols-2 gap-4">
            <DetailsValue label="Name" value={beehiveData.name} />
            <DetailsValue label="Status" value={beehiveData.status} />
            <DetailsValue label="Latitude" value={beehiveData.latitude} />
            <DetailsValue label="Longitude" value={beehiveData.longitude} />
            <DetailsValue
              label="Is the beehive functional ?"
              value={beehiveData.isFunctional ? "Yes" : "No"}
            />
            <DetailsValue
              label="Does the beehive need repairs ?"
              value={beehiveData.needsToBeRepaired ? "Yes" : "No"}
            />
            <DetailsValue
              label="Is the beehive occupied ?"
              value={beehiveData.isOccupied ? "Yes" : "No"}
            />
            <DetailsValue
              label="Observation"
              value={beehiveData.observation || "No observations"}
            />
            {/* Special handling for images */}
            {/* <div className="col-span-2">
              <p className="text-xs uppercase text-muted-foreground mb-2">
                Images
              </p>
              {beehiveData.images && beehiveData.images.length > 0 ? (
                <ImageCarousel images={beehiveData.images} />
              ) : (
                <p className="text-muted-foreground">No images uploaded</p>
              )}
            </div> */}
          </div>
        </CardContent>
      </Card>

      <NextButton
        isLast={isLast}
        handleNext={handleNext}
        formPath={formPath}
        isLoading={isLoading}
      />
    </div>
  )
}
