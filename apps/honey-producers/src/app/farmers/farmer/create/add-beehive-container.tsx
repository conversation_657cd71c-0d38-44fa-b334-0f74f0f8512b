"use client"

import { useState } from "react"
import { use<PERSON>orm, SubmitHandler, FormProvider } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { type FormValues, formSchema } from "@repo/validation/add-beehive"
import { AddBeehiveForm, ConfirmStep, CompleteStep } from "./steps/"
import { Step, Stepper } from "@/components/form/stepper"
import { CompleteStepBeehiveData } from "./steps/complete"
import api from "@/lib/api"
import { useOffline } from "@/hooks/use-offline"
import { getAllFromIDB, deleteFromIDB, saveToIDB } from "@/lib/idb"
import { useMutation } from "@tanstack/react-query"
import { generateUUID } from "@/lib/utils"

async function addBeehiveToFarmerStores(
  beehive: { id: string; name: string },
  farmerId: string
) {
  const stores = ["farmersToCreate", "farmersDownloaded"]

  for (const store of stores) {
    const farmers = await getAllFromIDB<any>(store)
    const farmer = farmers.find((f) => f.id === farmerId)
    if (!farmer) continue

    const updatedFarmer = {
      ...farmer,
      beehives: [...(farmer.beehives || []), beehive],
      updatedAt: new Date().toISOString(),
    }

    await deleteFromIDB(store, farmerId)
    await saveToIDB(store, updatedFarmer)
  }
}

interface Props {
  farmerId: string
}

export const AddBeehiveContainer: React.FC<Props> = ({ farmerId }) => {
  const [step, setStep] = useState(1)
  const [beehiveData, setBeehiveData] = useState<CompleteStepBeehiveData>()
  const [isLoading, setIsLoading] = useState(false)
  const { isOffline } = useOffline()

  const methods = useForm({
    resolver: zodResolver(formSchema),
  })

  const mutation = api.useMutation("post", "/beehives", {
    onSuccess: (response) => {
      setBeehiveData({
        id: response.id || "",
      })
      methods.reset()
      setStep(step + 1)

      setIsLoading(false)
    },
    onError: (e) => {
      console.log(e.message)

      setIsLoading(false)
    },
  })

  const offlineMutation = useMutation({
    mutationFn: async (payload: any) => {
      const beehiveId = generateUUID()
      const offlinePayload = {
        id: beehiveId,
        ...payload,
      }

      await saveToIDB("beehivesToCreate", offlinePayload)
      await saveToIDB("beehivesDownloaded", offlinePayload)

      const summary = { id: beehiveId, name: payload.name }
      await addBeehiveToFarmerStores(summary, payload.farmerId)

      return offlinePayload
    },
    onSuccess: (beehiveId) => {
      setBeehiveData(beehiveId)
      methods.reset()
      setStep(step + 1)

      setIsLoading(false)
    },
    onError: (e) => {
      console.log(e.message)

      setIsLoading(false)
    },
  })

  const stepComponents = [AddBeehiveForm, ConfirmStep, CompleteStep]

  const stepSections = [
    { id: 1, label: "Add Beehive", range: [1, 1] },
    { id: 2, label: "Review", range: [2, 2] },
    { id: 3, label: "Finish", range: [3, 3] },
  ]

  const stepperState: Step[] = stepSections.map(({ id, label, range }) => {
    const [start, end] = range
    return {
      id,
      label,
      status:
        step > end
          ? "completed"
          : step >= start && step <= end
            ? "current"
            : "upcoming",
    }
  })

  const moveToNextStep = async () => {
    // Trigger validation before moving to the next step
    const isValid = await methods.trigger([
      "name",
      "status",
      "latitude",
      "longitude",
      "isFunctional",
      "needsToBeRepaired",
      "isOccupied",
      "observation",
      // "images",
    ])

    if (!isValid) return

    setStep(step + 1)
  }

  const onSubmit: SubmitHandler<FormValues> = (data) => {
    setIsLoading(true)

    const payload = {
      name: data.name,
      status: data.status,
      latitude: data.latitude,
      longitude: data.longitude,
      isFunctional: data.isFunctional,
      needsToBeRepaired: data.needsToBeRepaired,
      isOccupied: data.isOccupied,
      observations: data.observation,
      farmerId: farmerId,
      // images: data.images,
    }
    if (!isOffline) {
      mutation.mutate({ body: payload })
    } else {
      offlineMutation.mutate(payload)
    }
  }

  const handleEdit = (sectionId: number) => {
    const stepId = stepSections.find((section) => section.id === sectionId)
      ?.range[0]

    if (!stepId) return

    setStep(stepId)
  }

  const renderStep = () => {
    const CurrentStepComponent = stepComponents[step - 1]

    return (
      <CurrentStepComponent
        handleNext={moveToNextStep}
        isLast={step === stepComponents.length - 1}
        formPath={
          beehiveData &&
          `/farmers/farmer/beehive#${farmerId}::${beehiveData.id}`
        }
        handleEdit={handleEdit}
        beehiveData={beehiveData}
        isLoading={isLoading}
      />
    )
  }

  return (
    <FormProvider {...methods}>
      <div className="flex items-center flex-col md:mx-4">
        <Stepper
          steps={stepperState}
          progress={(step / stepComponents.length) * 100}
          backDisabled={!!beehiveData}
        />
        <div className="w-full max-w-[800px]">
          <form onSubmit={methods.handleSubmit(onSubmit)}>{renderStep()}</form>
        </div>
      </div>
    </FormProvider>
  )
}
