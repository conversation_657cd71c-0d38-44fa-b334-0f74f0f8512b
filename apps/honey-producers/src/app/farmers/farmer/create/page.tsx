"use client"
import { useEffect, useState } from "react"
import { AddBeehiveContainer } from "./add-beehive-container"

export default function BeehiveManagement() {
  const [farmerId, setFarmerId] = useState<string | null>(null)

  useEffect(() => {
    if (typeof window !== "undefined") {
      const hash = window.location.hash
      if (hash) {
        setFarmerId(hash.replace("#", ""))
      }
    }
  }, [])

  if (!farmerId) {
    return <p className="text-center mt-10">Loading...</p>
  }

  return (
    <div>
      <AddBeehiveContainer farmerId={farmerId} />
    </div>
  )
}
