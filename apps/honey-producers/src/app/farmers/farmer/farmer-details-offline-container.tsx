"use client"

import { useEffect, useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import FarmerProfile from "../components/farmer-profile"
import BeehivesListOffline from "../components/beehives-list-offline"
import { CardContent, CardHeader } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ArrowLeft, Camera, Loader } from "lucide-react"
import { ProfilePhotoDialog } from "@/components/profile-photo-dialog"
import { getAllFromIDB } from "@/lib/idb"

interface Props {
  farmerId: string
}

export const FarmerDetailsOfflineContainer: React.FC<Props> = ({
  farmerId,
}) => {
  const router = useRouter()
  const searchParams = useSearchParams()

  const [farmerData, setFarmerData] = useState<any | null>(null)
  const [beehivesD<PERSON>, setBeehives] = useState<any | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isProfilePhotoDialogOpen, setIsProfilePhotoDialogOpen] =
    useState(false)
  const [profilePhotoUrl, setProfilePhotoUrl] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState("profile")

  useEffect(() => {
    const tabParam = searchParams.get("tab")
    if (tabParam === "beehives" || tabParam === "profile") {
      setActiveTab(tabParam)
    }
  }, [searchParams])

  useEffect(() => {
    const loadOfflineData = async () => {
      setIsLoading(true)
      try {
        const allFarmers = await getAllFromIDB("farmersDownloaded")
        const farmer = allFarmers.find((f: any) => f.id === farmerId)
        setFarmerData(farmer || null)
        const allBeehives = await getAllFromIDB("beehivesDownloaded")
        const beehives = allBeehives.filter(
          (b: any) => String(b.farmerId) === String(farmerId)
        )
        setBeehives(beehives)
      } catch (error) {
        console.error("Failed to load offline farmer data:", error)
        setFarmerData(null)
      } finally {
        setIsLoading(false)
      }
    }

    loadOfflineData()
  }, [farmerId])

  if (isLoading) {
    return (
      <div className="flex justify-center items-center">
        <Loader className="h-6 w-6 animate-spin text-primary" />
        <span className="ml-2">Loading offline farmer data...</span>
      </div>
    )
  }

  if (!farmerData) {
    return (
      <div className="flex items-center justify-center">
        <CardContent className="pt-6 flex flex-col items-center text-center space-y-4">
          <p className="text-red-500 font-medium">
            Farmer not available offline.
          </p>
          <Button className="w-full" onClick={() => router.push(`/farmers`)}>
            Back to Farmer List
          </Button>
        </CardContent>
      </div>
    )
  }

  const getInitials = () =>
    `${farmerData.firstName?.charAt(0) ?? ""}${farmerData.lastName?.charAt(0) ?? ""}`

  const handlePhotoUpload = (photoUrl: string | null) => {
    setProfilePhotoUrl(photoUrl)
    setIsProfilePhotoDialogOpen(false)
  }

  return (
    <div>
      <CardHeader className="relative text-center space-y-4">
        <div className="flex justify-between gap-2">
          <Button variant="outline" onClick={() => router.push(`/farmers`)}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <span className="flex items-center px-3 py-1 rounded-md text-sm font-medium bg-gray-100 text-gray-500 border border-gray-200">
            ID: {farmerId}
          </span>
        </div>
        <div className="flex items-center justify-center gap-3">
          <div className="relative group">
            <div className="h-12 w-12 rounded-full border-2 border-gray-200 overflow-hidden flex items-center justify-center bg-blue-600 text-white">
              {profilePhotoUrl ? (
                <img
                  src={profilePhotoUrl}
                  alt={`${farmerData.firstName} ${farmerData.lastName}`}
                  className="h-full w-full object-cover"
                />
              ) : (
                <span className="text-sm font-medium">{getInitials()}</span>
              )}
            </div>
            <Button
              variant="secondary"
              size="icon"
              className="absolute -bottom-1 -right-1 h-6 w-6 rounded-full shadow-md opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={() => setIsProfilePhotoDialogOpen(true)}
            >
              <Camera className="h-3 w-3" />
            </Button>
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 max-sm:text-xl">
              {farmerData.firstName} {farmerData.lastName}
            </h1>
            <span className="inline-flex px-3 py-1 rounded-full text-sm font-medium bg-emerald-100 text-emerald-800 max-sm:text-xs">
              {farmerData.role}
            </span>
          </div>
        </div>
      </CardHeader>

      <CardContent className="max-sm:p-4">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="flex h-10 w-full bg-transparent p-0">
            <TabsTrigger
              value="profile"
              className="h-10 flex-1 data-[state=active]:shadow-none data-[state=active]:border data-[state=active]:border-gray-200 data-[state=active]:text-blue-600 rounded-t-lg rounded-b-none"
            >
              Profile
            </TabsTrigger>
            <TabsTrigger
              value="beehives"
              className="h-10 flex-1 data-[state=active]:shadow-none data-[state=active]:border data-[state=active]:border-gray-200 data-[state=active]:text-blue-600 rounded-t-lg rounded-b-none"
            >
              Beehives ({beehivesData?.length ?? 0})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="profile" className="mt-0">
            <FarmerProfile farmerId={farmerId} farmerData={farmerData} />
          </TabsContent>

          <TabsContent value="beehives" className="mt-0">
            <BeehivesListOffline
              farmerId={farmerId}
              isLoading={isLoading}
              offlineBeehives={beehivesData}
            />
          </TabsContent>
        </Tabs>
      </CardContent>

      <ProfilePhotoDialog
        open={isProfilePhotoDialogOpen}
        onOpenChange={setIsProfilePhotoDialogOpen}
        currentPhotoUrl=""
        userName={`${farmerData.firstName} ${farmerData.lastName}`}
        onPhotoUpload={handlePhotoUpload}
      />
    </div>
  )
}
