"use client"
import { useEffect, useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { Loader } from "lucide-react"
import api from "@/lib/api"
import { useOfflineSyncManager } from "@/hooks/useSyncOfflineForms"
import BeehiveDetails from "../../components/beehive-details"
import { FormValues } from "@repo/validation/add-beehive"
import { useOffline } from "@/hooks/use-offline"
import { getAllFromIDB } from "@/lib/idb"

interface Props {
  farmerId: string
  beehiveId: string
}

export const BeehiveDetailsContainer: React.FC<Props> = ({
  farmerId,
  beehiveId,
}) => {
  const router = useRouter()
  const [beehiveOfflineData, setBeehiveOfflineData] =
    useState<FormValues | null>()
  const { isOffline } = useOffline()

  useEffect(() => {
    if (!farmerId || !beehiveId) {
      router.push("/404")
    }
  }, [farmerId, beehiveId, router])

  if (!farmerId || !beehiveId) {
    return null
  }

  const {
    data: beehiveData,
    isLoading,
    refetch,
  } = api.useQuery("get", "/beehives/{id}", {
    params: {
      path: {
        id: beehiveId,
      },
    },
  })

  useEffect(() => {
    const fetchOfflineData = async () => {
      try {
        const localData = await getAllFromIDB<FormValues>("beehivesDownloaded")
        const beehiveOffline = localData.find((b) => b.id === beehiveId)
        setBeehiveOfflineData(beehiveOffline)
      } catch (error) {
        console.error("Failed to load offline farmer data:", error)
        setBeehiveOfflineData(null)
      }
    }
    fetchOfflineData()
  }, [beehiveId])

  const { hasJustSynced, setHasJustSynced } = useOfflineSyncManager()

  useEffect(() => {
    if (hasJustSynced) {
      refetch()
      setHasJustSynced(false)
    }
  }, [hasJustSynced, refetch, setHasJustSynced])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="flex gap-2 justify-center pt-6">
            <Loader className="h-6 w-6 animate-spin text-primary" />
            <span className="ml-2">Loading beehive data...</span>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!beehiveData && !beehiveOfflineData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <p className="mb-4 text-center text-muted-foreground">
              Beehive data not find.
            </p>
            <Button
              className="w-full mt-4"
              onClick={() =>
                router.push(`/farmers/farmer?tab=beehives#${farmerId}`)
              }
            >
              Back to Beehives List
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div>
      <BeehiveDetails
        beehiveData={(!isOffline ? beehiveData : beehiveOfflineData)!}
        farmerId={farmerId}
        beehiveId={beehiveId}
      />
    </div>
  )
}
