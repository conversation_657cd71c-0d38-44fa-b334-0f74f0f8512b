"use client"
import { useEffect, useState } from "react"
import { BeehiveDetailsContainer } from "./beehive-details-container"

export default function BeehiveManagement() {
  const [farmerId, setFarmerId] = useState<string | null>(null)
  const [beehiveId, setBeehiveId] = useState<string | null>(null)

  useEffect(() => {
    if (typeof window !== "undefined") {
      const hash = window.location.hash.slice(1)
      const [farmerIdFromHash, beehiveIdFromHash] = hash.split("::")

      if (farmerIdFromHash && beehiveIdFromHash) {
        setFarmerId(farmerIdFromHash)
        setBeehiveId(beehiveIdFromHash)
      } else {
        setFarmerId(null)
        setBeehiveId(null)
      }
    }
  }, [])

  if (!farmerId || !beehiveId) {
    return null
  }

  return (
    <div>
      <BeehiveDetailsContainer farmerId={farmerId} beehiveId={beehiveId} />
    </div>
  )
}
