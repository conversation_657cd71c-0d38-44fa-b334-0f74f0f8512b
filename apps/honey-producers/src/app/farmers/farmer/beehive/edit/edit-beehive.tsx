"use client"

import { useState, useRef } from "react"
import { useFormContext } from "react-hook-form"
import { Input } from "@/components/ui/input"
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import type { FormValues } from "@repo/validation/add-beehive"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ImagePlus, Loader } from "lucide-react"
import { Button } from "@/components/ui/button"
// import { Label } from "@/components/ui/label"
// import { ImageCarousel } from "@/components/image-carousel"

interface BeehiveImage {
  url: string
  file?: File
}

export const EditBeehivePage = () => {
  const { control } = useFormContext<FormValues>()
  // const [images, setImages] = useState<BeehiveImage[]>([])
  // const [isUploading, setIsUploading] = useState(false)
  // const fileInputRef = useRef<HTMLInputElement>(null)

  // // Handle image upload
  // const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   const file = e.target.files?.[0]
  //   if (!file) return

  //   setIsUploading(true)

  //   // Create a preview URL
  //   const imageUrl = URL.createObjectURL(file)

  //   // Simulate a short upload delay to show loading state
  //   setTimeout(() => {
  //     // Add the new image to the images array
  //     setImages((prev) => [...prev, { url: imageUrl, file }])

  //     // Reset the input
  //     e.target.value = ""

  //     setIsUploading(false)
  //   }, 1000)
  // }

  // // Handle image removal
  // const handleRemoveImage = (index: number) => {
  //   setImages((prev) => {
  //     const newImages = [...prev]
  //     // If the image has a URL created with URL.createObjectURL, revoke it to free memory
  //     if (newImages[index].file) {
  //       URL.revokeObjectURL(newImages[index].url)
  //     }
  //     newImages.splice(index, 1)
  //     return newImages
  //   })
  // }

  return (
    <div>
      <Card className="bg-primary-foreground">
        <CardHeader className="flex flex-row justify-between items-center px-6 py-4">
          <CardTitle className="text-base font-bold">Edit Beehive</CardTitle>
        </CardHeader>

        <CardContent>
          <div className="grid gap-6">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <FormField
                control={control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">
                      Beehive Name <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="ex: Beehive A"
                        className="border-gray-200 bg-background"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">
                      Status <span className="text-red-500">*</span>
                    </FormLabel>
                    <Select {...field} defaultValue={field.value}>
                      <SelectTrigger
                        id="status"
                        className="border-gray-200 bg-background"
                      >
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active" className="text-emerald-600">
                          Active
                        </SelectItem>
                        <SelectItem value="inactive" className="text-amber-600">
                          Inactive
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <FormField
                control={control}
                name="latitude"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">
                      Latitude <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="ex: 5"
                        className="border-gray-200 bg-background"
                        {...field}
                        value={field.value ?? ""}
                        onChange={(e) => {
                          const value =
                            e.target.value === ""
                              ? undefined
                              : Number(e.target.value)
                          field.onChange(value)
                        }}
                      />
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="longitude"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">
                      Longitude <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="ex: 5"
                        className="border-gray-200 bg-background"
                        {...field}
                        value={field.value ?? ""}
                        onChange={(e) => {
                          const value =
                            e.target.value === ""
                              ? undefined
                              : Number(e.target.value)
                          field.onChange(value)
                        }}
                      />
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={control}
              name="isFunctional"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>Is the beehive functional?</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={(value) =>
                        field.onChange(value === "true")
                      }
                      value={field.value?.toString()}
                      className="flex flex-col space-y-1 md:flex-row md:space-y-0 md:space-x-4"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="true" />
                        </FormControl>
                        <FormLabel className="font-normal">Yes</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="false" />
                        </FormControl>
                        <FormLabel className="font-normal">No</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="needsToBeRepaired"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>Does the beehive need repairs?</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={(value) =>
                        field.onChange(value === "true")
                      }
                      value={field.value?.toString()}
                      className="flex flex-col space-y-1 md:flex-row md:space-y-0 md:space-x-4"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="true" />
                        </FormControl>
                        <FormLabel className="font-normal">Yes</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="false" />
                        </FormControl>
                        <FormLabel className="font-normal">No</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="isOccupied"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>Is the beehive occupied?</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={(value) =>
                        field.onChange(value === "true")
                      }
                      value={field.value?.toString()}
                      className="flex flex-col space-y-1 md:flex-row md:space-y-0 md:space-x-4"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="true" />
                        </FormControl>
                        <FormLabel className="font-normal">Yes</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="false" />
                        </FormControl>
                        <FormLabel className="font-normal">No</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-2">
              <FormField
                control={control}
                name="observation"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">Observation</FormLabel>
                    <FormControl>
                      <Textarea {...field} rows={3} className="bg-background" />
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />
            </div>

            {/* <div className="space-y-3">
              <div className="flex justify-between items-center">
                <Label>Images</Label>
                <div>
                  <input
                    type="file"
                    id="image-upload"
                    accept="image/*"
                    className="hidden"
                    ref={fileInputRef}
                    onChange={handleImageUpload}
                  />
                  <Button
                    variant="default"
                    size="sm"
                    onClick={() => fileInputRef.current?.click()}
                    disabled={isUploading}
                  >
                    {isUploading ? (
                      <>
                        <span className="animate-spin mr-2">
                          <Loader />
                        </span>
                        Uploading...
                      </>
                    ) : (
                      <>
                        <ImagePlus className="h-4 w-4 mr-2" />
                        Add Images
                      </>
                    )}
                  </Button>
                </div>
              </div>

              <ImageCarousel
                images={images.map((img) => img.url)}
                onRemoveImage={(index) => handleRemoveImage(index)}
              />
            </div> */}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
