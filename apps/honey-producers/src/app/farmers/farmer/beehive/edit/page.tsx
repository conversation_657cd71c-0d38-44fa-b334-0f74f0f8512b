"use client"
import { useEffect, useState } from "react"
import { BeehiveEditPage } from "./edit-beehive-container"

export default function BeehiveEditManagement() {
  const [farmerId, setFarmerId] = useState<string | null>(null)
  const [beehiveId, setBeehiveId] = useState<string | null>(null)

  useEffect(() => {
    if (typeof window !== "undefined") {
      const hash = window.location.hash.slice(1)
      const [farmerIdFromHash, beehiveIdFromHash] = hash.split("::")

      if (farmerIdFromHash && beehiveIdFromHash) {
        setFarmerId(farmerIdFromHash)
        setBeehiveId(beehiveIdFromHash)
      } else {
        setFarmerId(null)
        setBeehiveId(null)
      }
    }
  }, [])

  if (!farmerId || !beehiveId) {
    return null
  }

  return (
    <div>
      <BeehiveEditPage beehiveId={beehiveId} farmerId={farmerId} />
    </div>
  )
}
