"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { useForm, type SubmitHandler, FormProvider } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Card, CardContent } from "@/components/ui/card"
import { Save, Loader } from "lucide-react"
import { type FormValues, formSchema } from "@repo/validation/add-beehive"
import api from "@/lib/api"
import { EditBeehivePage } from "./edit-beehive"
import { useOffline } from "@/hooks/use-offline"
import { getAllFromIDB, saveToIDB } from "@/lib/idb"
import { useMutation } from "@tanstack/react-query"

interface Props {
  farmerId: string
  beehiveId: string
}

export const BeehiveEditPage: React.FC<Props> = ({ beehiveId, farmerId }) => {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const { isOffline } = useOffline()

  useEffect(() => {
    if (!farmerId || !beehiveId) {
      router.push("/404")
    }
  }, [farmerId, beehiveId, router])

  if (!farmerId || !beehiveId) {
    return null
  }

  // Fetch farmer data
  const { data: beehive, isLoading: isBeehiveLoading } = api.useQuery(
    "get",
    "/beehives/{id}",
    {
      params: {
        path: {
          id: beehiveId,
        },
      },
      enabled: !isOffline,
    }
  )

  // Initialize form with default values
  const methods = useForm<FormValues>({
    resolver: zodResolver(formSchema),
  })

  // Update form values when farmer data is loaded
  useEffect(() => {
    if (beehive) {
      // Convert all values to strings to avoid issues with Select components
      methods.reset({
        id: beehiveId,
        name: beehive.name || "",
        status:
          beehive.status === "active" || beehive.status === "inactive"
            ? beehive.status
            : undefined,
        latitude: beehive.latitude || 0,
        longitude: beehive.longitude || 0,
        needsToBeRepaired:
          beehive.needsToBeRepaired === undefined
            ? true
            : beehive.needsToBeRepaired,
        isOccupied:
          beehive.isOccupied === undefined ? true : beehive.isOccupied,
        observation: beehive.observations || "",
        isFunctional:
          beehive.isFunctional === undefined ? true : beehive.isFunctional,
      })
    }
  }, [beehive])

  // Update form values when farmer data is loaded
  useEffect(() => {
    const fetchBeehive = async () => {
      if (!beehiveId) return

      if (isOffline) {
        const allBeehive = await getAllFromIDB("beehivesDownloaded")
        const offlineBeehive = allBeehive.find(
          (b: any) => b.farmerId === farmerId
        ) as FormValues

        if (offlineBeehive) {
          methods.reset({
            id: beehiveId,
            name: offlineBeehive.name || "",
            status:
              offlineBeehive.status === "active" ||
              offlineBeehive.status === "inactive"
                ? offlineBeehive.status
                : undefined,
            latitude: offlineBeehive.latitude || 0,
            longitude: offlineBeehive.longitude || 0,
            needsToBeRepaired:
              offlineBeehive.needsToBeRepaired === undefined
                ? true
                : offlineBeehive.needsToBeRepaired,
            isOccupied:
              offlineBeehive.isOccupied === undefined
                ? true
                : offlineBeehive.isOccupied,
            observation: offlineBeehive.observation || "",
            isFunctional:
              offlineBeehive.isFunctional === undefined
                ? true
                : offlineBeehive.isFunctional,
          })
        }
      }
    }

    fetchBeehive()
  }, [beehiveId, isOffline, methods, router])

  const updateMutation = api.useMutation("post", "/beehives/{id}", {
    onSuccess: () => {
      router.push(`/farmers/farmer?tab=beehives#${farmerId}`)

      setIsLoading(false)
    },
    onError: (e) => {
      console.log(e.message)
      setIsLoading(false)
    },
  })

  const updateMutationOffline = useMutation({
    mutationFn: async (payload: any) => {
      const offlinePayload = {
        ...payload,
      }

      await saveToIDB("beehivesToCreate", offlinePayload)
      await saveToIDB("beehivesDownloaded", offlinePayload)

      return offlinePayload
    },

    onSuccess: () => {
      router.push(`/farmers/farmer?tab=beehives#${farmerId}`)

      setIsLoading(false)
    },
    onError: (e) => {
      console.log(e.message)
      setIsLoading(false)
    },
  })

  const onSubmit: SubmitHandler<FormValues> = (data) => {
    setIsLoading(true)
    const payload = {
      name: data.name,
      status: data.status,
      latitude: data.latitude,
      longitude: data.longitude,
      needsToBeRepaired: data.needsToBeRepaired,
      isOccupied: data.isOccupied,
      observations: data.observation,
      isFunctional: data.isFunctional,
      farmerId: farmerId,
    }

    console.log(payload)

    if (!isOffline) {
      updateMutation.mutate({
        params: {
          path: {
            id: beehiveId,
          },
        },
        body: payload,
      })
    } else {
      updateMutationOffline.mutate({
        id: beehiveId,
        ...payload,
      })
    }
  }

  if (isBeehiveLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6 flex items-center justify-center">
            <Loader className="h-6 w-6 animate-spin text-primary mr-2" />
            <p>Loading beehive data...</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <FormProvider {...methods}>
      <div className="w-full max-w-4xl mx-auto">
        <form onSubmit={methods.handleSubmit(onSubmit)}>
          <EditBeehivePage />
          <div className="mt-4 flex gap-2 ">
            <Button
              variant="outline"
              onClick={() =>
                router.push(`/farmers/farmer?tab=beehives#${farmerId}`)
              }
              className="w-full"
            >
              Cancel
            </Button>
            <Button
              variant="default"
              type="submit"
              disabled={isLoading}
              className="w-full"
            >
              {isLoading ? (
                <>
                  <Loader className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </FormProvider>
  )
}
