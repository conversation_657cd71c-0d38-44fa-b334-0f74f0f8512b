"use client"

import { useState, useEffect, useCallback } from "react"
import { useRouter } from "next/navigation"
import {
  Search,
  PlusCircle,
  Loader,
  TriangleAlert,
  AlertCircle,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Alert, AlertDescription } from "@/components/ui/alert"
import type { FormValues } from "@repo/validation/add-farmer"
import api from "@/lib/api"
import { FarmersListOnline } from "./components/farmer-list-online"
import { FarmersListOffline } from "./components/farmer-list-offline"
import { useOfflineSyncManager } from "@/hooks/useSyncOfflineForms"
import { getAllFromIDB, STORE_NAMES } from "@/lib/idb"
import { useOffline } from "@/hooks/use-offline"

export default function FarmersList() {
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState("")
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [farmerToDelete, setFarmerToDelete] = useState<FormValues | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const [offlineFarmers, setOfflineFarmers] = useState<FormValues[]>([])
  const { isOffline } = useOffline()
  const [isLoadingOffline, setIsLoadingOffline] = useState(false)
  const [filteredFarmers, setFilteredFarmers] = useState<FormValues[]>([])
  const [errors, setError] = useState<string | null>(null)

  const {
    data: farmers,
    isLoading,
    refetch,
  } = api.useQuery("get", "/farmers", {
    params: {
      query: {
        query: searchTerm,
      },
    },
  })

  const { hasJustSynced, setHasJustSynced } = useOfflineSyncManager()
  useEffect(() => {
    const fetchOfflineData = async () => {
      const localData = await getAllFromIDB<FormValues>("farmersDownloaded")
      setOfflineFarmers(localData)
    }
    fetchOfflineData()
  }, [hasJustSynced])

  // Load farmers from IndexedDB
  const loadFarmers = useCallback(async () => {
    setIsLoadingOffline(true)

    try {
      const localFarmers = await getAllFromIDB<FormValues>(
        STORE_NAMES.farmersDownloaded
      )
      setOfflineFarmers(localFarmers)
      setFilteredFarmers(localFarmers)
    } catch (error) {
      console.error("Error loading farmers from IndexedDB:", error)
      setOfflineFarmers([])
      setFilteredFarmers([])
      setError(
        "Failed to load farmer. Please try again or contact support if the problem persists."
      )
    } finally {
      setIsLoadingOffline(false)
    }
  }, [])

  // Filter farmers based on search input
  const filterFarmers = useCallback(() => {
    if (!searchTerm.trim()) {
      setFilteredFarmers(offlineFarmers)
      return
    }

    const query = searchTerm.toLowerCase().trim()
    const results = offlineFarmers.filter((farmer) => {
      const fullName =
        `${farmer.firstName || ""} ${farmer.lastName || ""}`.toLowerCase()
      const id = farmer.id?.toLowerCase() || ""

      return fullName.includes(query) || id.includes(query)
    })

    setFilteredFarmers(results)
  }, [searchTerm, offlineFarmers])

  // Load farmers on component mount
  useEffect(() => {
    loadFarmers()
  }, [loadFarmers])

  // Filter farmers when search input changes
  useEffect(() => {
    filterFarmers()
  }, [filterFarmers])

  useEffect(() => {
    if (hasJustSynced) {
      refetch()
      setHasJustSynced(false)
    }
  }, [hasJustSynced, refetch, setHasJustSynced])

  const deleteMutation = api.useMutation("delete", "/farmers/{id}", {
    onSuccess: () => {
      refetch()
      setIsDeleteDialogOpen(false)
      setFarmerToDelete(null)
      setIsDeleting(false)
      setError(null)
    },
    onError: (error) => {
      console.error("Error while deleting:", error)
      setIsDeleting(false)
      setError(
        error?.message ||
          "Failed to delete farmer. Please try again or contact support if the problem persists."
      )
    },
  })

  const handleDeleteFarmer = async () => {
    if (!farmerToDelete || !farmerToDelete.id) return
    setIsDeleting(true)
    setError(null)
    try {
      await deleteMutation.mutate({
        params: {
          path: {
            id: farmerToDelete.id,
          },
        },
      })
    } catch (error) {
      console.error("Additional error:", error)
    }
  }

  const openDeleteDialog = (farmer: any) => {
    console.log(farmer)
    setFarmerToDelete(farmer)
    setIsDeleteDialogOpen(true)
    setError(null)
  }

  return (
    <div className="container max-w-screen-xl mx-auto py-8 space-y-8">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-2xl font-semibold">Farmers</h1>
        {isOffline && (
          <p className="flex justify-center gap-2 text-xs text-muted-foreground p-2 bg-yellow-50 rounded-md border border-yellow-200">
            <TriangleAlert />
            You are in offline mode. Delete farmers is not available.
          </p>
        )}
      </div>

      {/* Filters */}
      <div className="flex flex-col lg:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search by name or id"
            className="pl-9"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Button
          variant="default"
          onClick={() => router.push("/farmers/create")}
        >
          <PlusCircle className="h-2 w-2 mr-2" />
          Add Farmer
        </Button>
      </div>

      {!isOffline ? (
        <FarmersListOnline
          isLoading={isLoading}
          farmers={farmers?.results ?? []}
          openDeleteDialog={openDeleteDialog}
        />
      ) : (
        <FarmersListOffline
          isLoading={isLoadingOffline}
          offlineFarmers={filteredFarmers}
        />
      )}

      <Dialog
        open={isDeleteDialogOpen}
        onOpenChange={(open) => {
          if (!isDeleting) setIsDeleteDialogOpen(open)
        }}
      >
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the farmer{" "}
              {farmerToDelete?.firstName + " " + farmerToDelete?.lastName} ?
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          {/* Error Message */}
          {errors && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{errors}</AlertDescription>
            </Alert>
          )}

          <DialogFooter className="flex gap-2 justify-end">
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteFarmer}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Loader className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
