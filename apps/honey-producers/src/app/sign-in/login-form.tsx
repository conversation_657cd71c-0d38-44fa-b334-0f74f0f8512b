"use client"

import type React from "react"
import { useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useState } from "react"
import { loginWithEmail } from "@/lib/firebase-auth"
import { loginSchema, LoginFormValues } from "@repo/validation/login"
import { Loader } from "lucide-react"
import { getAuth, sendPasswordResetEmail } from "firebase/auth"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

export async function resetPassword(email: string) {
  const auth = getAuth()
  return await sendPasswordResetEmail(auth, email)
}

export function LoginForm({
  className,
  ...props
}: React.ComponentPropsWithoutRef<"div">) {
  const [error, setError] = useState("")
  const [dialogOpen, setDialogOpen] = useState(false)
  const [resetEmail, setResetEmail] = useState("")
  const [resetStatus, setResetStatus] = useState<
    "idle" | "sending" | "sent" | "error"
  >("idle")
  const [resetError, setResetError] = useState("")

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<LoginFormValues>({ resolver: zodResolver(loginSchema) })

  const onSubmit = async (data: LoginFormValues) => {
    try {
      setError("")
      const tenantId = "NaturesNectar-82ft3"
      const { user } = await loginWithEmail(tenantId, data.email, data.password)
    } catch (err: any) {
      setError("Login failed: " + err.message)
    }
  }

  const handleResetPassword = async () => {
    setResetStatus("sending")
    setResetError("")
    try {
      await resetPassword(resetEmail)
      setResetStatus("sent")
    } catch (err: any) {
      setResetError(err.message)
      setResetStatus("error")
    }
  }

  useEffect(() => {
    const storedError = localStorage.getItem("loginError")
    if (storedError) {
      setError(storedError)
      localStorage.removeItem("loginError")
    }
  }, [])

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <Card>
        <CardHeader className="text-center">
          <CardTitle className="text-xl">Welcome back</CardTitle>
          <CardDescription>Login with your account</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="grid gap-6">
              <div className="grid gap-6">
                <div className="grid gap-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter your Email Adress"
                    {...register("email")}
                  />
                  {errors.email && (
                    <p className="text-sm text-destructive">
                      {errors.email.message}
                    </p>
                  )}
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="email">Password</Label>
                  <Input
                    id="password"
                    type="password"
                    placeholder="Enter your Password"
                    {...register("password")}
                  />
                  {errors.password && (
                    <p className="text-sm text-destructive">
                      {errors.password.message}
                    </p>
                  )}
                </div>
                {error && <p className="text-sm text-destructive">{error}</p>}
                <div className="flex flex-col gap-2">
                  <Button
                    type="submit"
                    className="w-full"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader className="h-6 w-6 animate-spin text-secondary" />
                        <span className="ml-2">Logging in...</span>
                      </>
                    ) : (
                      "Login"
                    )}
                  </Button>
                  <Button onClick={() => setDialogOpen(true)} variant="ghost">
                    Forgot your password?
                  </Button>
                </div>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>
      {/* 🔐 Dialog Forgot Password */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reset your password</DialogTitle>
            <DialogDescription>
              Enter your email address and we'll send you a link to reset your
              password.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-2">
            <Input
              type="email"
              placeholder="Enter your email"
              value={resetEmail}
              onChange={(e) => setResetEmail(e.target.value)}
            />
            {resetError && (
              <p className="text-sm text-destructive">{resetError}</p>
            )}
            {resetStatus === "sent" && (
              <p className="text-sm text-green-600">
                An email has been sent to reset your password.
              </p>
            )}
          </div>
          <DialogFooter>
            <Button
              onClick={handleResetPassword}
              disabled={resetStatus === "sending"}
            >
              {resetStatus === "sending" ? "Sending..." : "Send reset link"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
