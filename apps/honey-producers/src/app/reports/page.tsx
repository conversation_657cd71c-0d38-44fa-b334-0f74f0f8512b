"use client"

import React from "react"
import { Loader } from "lucide-react"
import api from "@/lib/api"

const ReportsPage: React.FC = () => {
  const { data, error, isLoading } = api.useQuery(
    "get",
    "/dashboard-links/{id}",
    {
      params: {
        path: {
          id: 10,
        },
      },
    }
  )

  return (
    <div className="w-full">
      <h1 className="text-center text-2xl font-semibold mb-4">Reports</h1>
      {isLoading && (
        <div className="flex justify-center items-center mt-6">
          <Loader className="h-6 w-6 animate-spin text-primary" />
          <span className="ml-2">Loading reports...</span>
        </div>
      )}
      {error && <div className="text-destructive">Error: {error.message}</div>}
      {data && (
        <iframe
          title="reports"
          src={`${data.url}#bordered=true&titled=true`}
          style={{
            width: "100%",
            height: "90dvh",
            borderWidth: 0,
          }}
        />
      )}
    </div>
  )
}

export default ReportsPage
