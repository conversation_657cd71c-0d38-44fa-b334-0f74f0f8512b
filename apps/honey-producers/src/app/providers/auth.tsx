"use client"

import React, { useEffect, useState, createContext, useContext } from "react"
import { useRouter, usePathname } from "next/navigation"
import { onAuthStateChanged, signOut, type User } from "firebase/auth"
import { auth } from "@/lib/auth-client"
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar"
import { AppSidebar } from "@/components/app-sidebar"

type AuthContextType = {
  user: User | null
  logout: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}

const publicRoutes = ["/sign-in", "/reset-password"]

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  const pathname = usePathname()
  const router = useRouter()

  const isPublicRoute = publicRoutes.some((route) => pathname.startsWith(route))

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (firebaseUser) => {
      setUser(firebaseUser)
      setLoading(false)
    })

    return () => unsubscribe()
  }, [])

  useEffect(() => {
    if (loading) return

    if (user && isPublicRoute) {
      router.push("/farmers")
    }

    if (!user && !isPublicRoute) {
      router.push("/sign-in")
    }
  }, [user, loading, isPublicRoute, router])

  const logout = async () => {
    await signOut(auth)
    setUser(null)
    router.push("/sign-in")
  }

  if (loading) {
    return (
      <div className="w-full h-screen flex items-center justify-center">
        <p>Loading...</p>
      </div>
    )
  }

  return (
    <AuthContext.Provider value={{ user, logout }}>
      {!isPublicRoute ? (
        <SidebarProvider>
          <AppSidebar />
          <main className="w-full">
            <SidebarTrigger className="fixed" />
            <div className="py-5 max-sm:px-4 px-10">{children}</div>
          </main>
        </SidebarProvider>
      ) : (
        children
      )}
    </AuthContext.Provider>
  )
}
