"use client"

import React, { useEffect, useState, createContext, useContext } from "react"
import { useRouter, usePathname } from "next/navigation"
import { authClient } from "@/lib/auth-client"
import { getUserSession, saveUserSession, clearUserSession } from "@/lib/idb"
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar"
import { AppSidebar } from "@/components/app-sidebar"

type AuthContextType = {
  logout: () => Promise<void>
  session: any | null
}

const AuthContext = createContext<AuthContextType>({
  logout: async () => {},
  session: null,
})

export const useAuth = () => useContext(AuthContext)

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const { data, isPending } = authClient.useSession()
  const router = useRouter()
  const pathname = usePathname()

  const [allowed, setAllowed] = useState(false)
  const [session, setSession] = useState<any>(null)

  const publicRoutes = ["/sign-in", "/reset-password"]
  const isPublicRoute = publicRoutes.some((route) => pathname.startsWith(route))

  useEffect(() => {
    const checkSession = async () => {
      if (isPending) return

      if (data) {
        setSession(data)
        await saveUserSession(data)
        if (isPublicRoute) {
          router.push("/reports")
        }
        setAllowed(true)
        return
      }

      if (!data && !isPublicRoute) {
        if (!navigator.onLine) {
          const localSession = await getUserSession()
          console.log("local session", localSession)
          if (localSession) {
            setSession({
              user: localSession.user,
              session: localSession.session,
            })
            setAllowed(true)
            return
          }
        }
        router.push("/sign-in")
        return
      }

      setAllowed(true)
    }

    checkSession()
  }, [data, isPending, pathname, router, isPublicRoute])

  const logout = async () => {
    authClient.signOut()
    clearUserSession()
    setSession(null)
    router.push("/sign-in")
  }

  if (isPending || !allowed) {
    return (
      <div className="w-full h-screen flex items-center justify-center">
        <p>Loading...</p>
      </div>
    )
  }

  return (
    <AuthContext.Provider value={{ logout, session }}>
      {session && !isPublicRoute ? (
        <SidebarProvider>
          <AppSidebar />
          <main className="w-full">
            <SidebarTrigger className="fixed" />
            <div className="py-5 max-sm:px-4 px-10">{children}</div>
          </main>
        </SidebarProvider>
      ) : (
        children
      )}
    </AuthContext.Provider>
  )
}
