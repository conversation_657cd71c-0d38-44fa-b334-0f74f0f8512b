"use client"
import { useState } from "react"
import { Check, ChevronsUpDown, X } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"

interface MultiSelectProps {
  options: string[]
  selected: string[]
  onChange: (selected: string[]) => void
  placeholder?: string
  emptyMessage?: string
  singleSelect?: boolean
  isDisabled?: boolean
}

export function MultiSelectDrums({
  options,
  selected,
  onChange,
  placeholder = "Select items",
  emptyMessage = "No items found.",
  singleSelect = false,
  isDisabled = false,
}: MultiSelectProps) {
  const [open, setOpen] = useState(false)

  // Ensure that selected is always an array
  const selectedValues = Array.isArray(selected) ? selected : []

  const handleSelect = (value: string) => {
    if (singleSelect) {
      if (selectedValues.includes(value)) {
        onChange([])
      } else {
        onChange([value])
      }
    } else {
      if (selectedValues.includes(value)) {
        onChange(selectedValues.filter((item) => item !== value))
      } else {
        onChange([...selectedValues, value])
      }
    }
  }

  const handleRemove = (value: string) => {
    onChange(selectedValues.filter((item) => item !== value))
  }

  return (
    <Popover open={open} onOpenChange={isDisabled ? () => {} : setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between bg-background"
          disabled={isDisabled}
        >
          {selectedValues.length > 0
            ? selectedValues.length === 1
              ? selectedValues[0]
              : `${selectedValues.length} drums selected`
            : placeholder}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[220px] p-0">
        {selectedValues.length > 0 && (
          <div className="p-2 border-b">
            <div className="flex flex-wrap gap-1 max-h-[100px] overflow-y-auto">
              {selectedValues.map((item) => (
                <Badge
                  key={item}
                  variant="secondary"
                  className="flex items-center gap-1 mb-1 max-w-full"
                >
                  <span className="truncate">{item}</span>
                  <button
                    className="ml-1 flex-shrink-0 ring-offset-background rounded-full outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        handleRemove(item)
                      }
                    }}
                    onMouseDown={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                    }}
                    onClick={() => handleRemove(item)}
                  >
                    <X className="h-3 w-3 text-muted-foreground hover:text-foreground" />
                  </button>
                </Badge>
              ))}
            </div>
          </div>
        )}
        <Command>
          <CommandInput placeholder="Search drums..." />
          <CommandList>
            <CommandEmpty>{emptyMessage}</CommandEmpty>
            <CommandGroup className="max-h-[200px] overflow-auto">
              {options.map((option) => (
                <CommandItem
                  key={option}
                  value={option}
                  onSelect={() => {
                    if (!isDisabled) {
                      handleSelect(option)
                      setOpen(!singleSelect)
                    }
                  }}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      selectedValues.includes(option)
                        ? "opacity-100"
                        : "opacity-0"
                    )}
                  />
                  {option}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
