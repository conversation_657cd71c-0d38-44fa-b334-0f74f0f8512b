"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON>earchPara<PERSON>, usePathname } from "next/navigation"
import { Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { BatchCode } from "@repo/dummy/batch-management"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Share2,
  MapPin,
  Calendar,
  ExternalLink,
  Check,
  Copy,
  Layers,
  ChevronDown,
  ChevronUp,
  ArrowLeft,
  EyeOff,
  Eye,
  Link,
} from "lucide-react"
import { Input } from "@/components/ui/input"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@radix-ui/react-tooltip"
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import type { BatchDetail } from "@repo/dummy/batch-management"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { cn } from "@/lib/utils"
import { QRCodeCanvas } from "qrcode.react"
import { useRef } from "react"
import api from "@/lib/api"
import { format } from "date-fns"
import { Switch } from "@/components/ui/switch"

interface Props {
  batchCode: string
  batchCodeData: BatchCode & { details?: BatchDetail[] }
}

// Helper function to get status color
const getStatusColor = (status: string): string => {
  switch (status) {
    case "In Progress":
      return "text-blue-600 bg-blue-500/40"
    case "Finalized":
      return "text-green-600 bg-green-500/40"
    default:
      return "text-gray-500"
  }
}

export const BatchCodeDetailsContainer: React.FC<Props> = ({
  batchCode,
  batchCodeData,
}) => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const pathname = usePathname()
  const [showAllFarmers, setShowAllFarmers] = useState(false)
  const [showAllBeehives, setShowAllBeehives] = useState(false)
  const [showAllBuckets, setShowAllBuckets] = useState(false)
  const [allBeehiveImages, setAllBeehiveImages] = useState<string[]>([])
  const [showAllDetails, setShowAllDetails] = useState(false)
  const [showSensitiveInfo, setShowSensitiveInfo] = useState(false)
  const isFinalized = batchCodeData.status === "Finalised"
  const [shareDialogOpen, setShareDialogOpen] = useState(false)
  const [shareType, setShareType] = useState("redacted")
  const [copied, setCopied] = useState(false)
  const [copiedShareLink, setCopiedShareLink] = useState(false)
  const [copiedEmbedCode, setCopiedEmbedCode] = useState(false)
  const [shareableLink, setShareableLink] = useState("")
  const [embedCode, setEmbedCode] = useState("")
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const baseUrl =
      typeof window !== "undefined"
        ? `${window.location.origin}${pathname}`
        : pathname

    const params = new URLSearchParams(searchParams.toString())

    if (shareType === "original") {
      params.set("view", "original")
    } else {
      params.set("view", "redacted")
    }

    const newShareableLink = `${baseUrl}?${params.toString()}`

    setShareableLink(newShareableLink)
    setEmbedCode(
      `<iframe width="560" height="315" src="${newShareableLink}" title="Traceability information" class="iframe-border"></iframe>`
    )
  }, [shareType, pathname, searchParams])

  const { data: batchData } = api.useQuery("get", "/batches/{batchCode}", {
    params: { path: { batchCode } },
  })

  const toggleFarmersDisplay = () => {
    setShowAllFarmers((prev) => !prev)
  }

  const toggleBeehivesDisplay = () => {
    setShowAllBeehives((prev) => !prev)
  }

  const toggleBucketsDisplay = () => {
    setShowAllBuckets((prev) => !prev)
  }

  const toggleDetailsDisplay = () => {
    setShowAllDetails((prev) => !prev)
  }

  useEffect(() => {
    if (batchCodeData) {
      const images = batchCodeData.beehives.flatMap((beehive) =>
        beehive.images.map((image) => image.url)
      )
      setAllBeehiveImages(images)
    }
  }, [batchCodeData])

  const navigateToFarmerProfile = (farmerId: string) => {
    router.push(`/farmers/farmer#${farmerId}`)
  }

  const copyHarvestingTransactionCode = async () => {
    try {
      await navigator.clipboard.writeText(
        batchCodeData.harvestingTransactionCode
      )
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error("Failed to copy transaction code:", error)
      setError("Failed to copy transaction code. Please try again.")
      setTimeout(() => setError(null), 3000)
    }
  }

  const copyProcessingTransactionCode = async () => {
    try {
      await navigator.clipboard.writeText(
        batchCodeData.processingTransactionCode
      )
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error("Failed to copy transaction code:", error)
      setError("Failed to copy transaction code. Please try again.")
      setTimeout(() => setError(null), 3000)
    }
  }

  const copyShareableLink = async () => {
    try {
      await navigator.clipboard.writeText(shareableLink)
      setCopiedShareLink(true)
      setTimeout(() => setCopiedShareLink(false), 2000)
    } catch (error) {
      console.error("Failed to copy shareable link:", error)
      setError("Failed to copy shareable link. Please try again.")
      setTimeout(() => setError(null), 3000)
    }
  }

  const copyEmbedCode = async () => {
    try {
      await navigator.clipboard.writeText(embedCode)
      setCopiedEmbedCode(true)
      setTimeout(() => setCopiedEmbedCode(false), 2000)
    } catch (error) {
      console.error("Failed to copy embed code:", error)
      setError("Failed to copy embed code. Please try again.")
      setTimeout(() => setError(null), 3000)
    }
  }

  const qrRef = useRef<HTMLCanvasElement>(null)

  const handleShare = async () => {
    const canvas = qrRef.current
    if (!canvas || !navigator.canShare || !navigator.share) {
      alert("Sharing is not supported on this browser")
      return
    }

    canvas.toBlob(async (blob) => {
      if (!blob) return
      const file = new File([blob], "qrcode.png", { type: "image/png" })

      if (navigator.canShare({ files: [file] })) {
        try {
          await navigator.share({
            title: "QR Code",
            text: "Here is the QR code!",
            files: [file],
          })
        } catch (err) {
          console.error("Share failed:", err)
        }
      } else {
        alert("Your device doesn't support sharing files")
      }
    })
  }

  return (
    <div>
      <CardHeader className="text-center space-y-4">
        <div className="flex justify-between items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            className="font-semibold"
            onClick={() => router.push("/forms/batch-management")}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>

          <div className="flex items-center gap-2">
            <div className="bg-background flex items-center gap-2 p-2 rounded-lg shadow">
              <Switch
                onCheckedChange={() => setShowSensitiveInfo(!showSensitiveInfo)}
              />
              <Label htmlFor="offline-mode" className="text-sm font-semibold">
                Business View
              </Label>
            </div>
            {isFinalized && (
              <Button
                variant="default"
                size="sm"
                className="font-semibold"
                onClick={() => setShareDialogOpen(true)}
              >
                <Share2 className="h-4 w-4 mr-1" /> Share
              </Button>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <CardTitle className="text-2xl">Batch {batchCode}</CardTitle>
          <Badge
            variant="outline"
            className={`px-3 py-2 border-none rounded-full ${getStatusColor(batchData?.isProcessed ? "Finalized" : "In Progress")}`}
          >
            {batchData?.isProcessed ? "Finalized" : "In Progress"}
          </Badge>
        </div>
      </CardHeader>

      <CardContent>
        <Accordion
          type="multiple"
          defaultValue={["sourced", "buckets", "processing"]}
          className="space-y-4"
        >
          {/* Sourced Section */}
          <AccordionItem
            value="sourced"
            className="border rounded-lg px-4 bg-background"
          >
            <AccordionTrigger className="py-4">
              <h2 className="text-lg font-bold flex items-center">
                <span className="h-2 w-2 rounded-full bg-blue-500 mr-2"></span>
                Sourced
              </h2>
            </AccordionTrigger>
            <AccordionContent className="pb-4 space-y-8">
              <div className="mb-10 space-y-8">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <div className="flex mb-2 gap-2 items-center">
                      <h3 className="text-sm text-gray-500">FARMERS</h3>
                      <div>
                        {batchData?.sources.farmers &&
                          batchData.sources.farmers.length > 6 && (
                            <div
                              onClick={toggleFarmersDisplay}
                              className="text-blue-600 hover:bg-transparent hover:text-blue-800 flex text-xs cursor-pointer"
                            >
                              {showAllFarmers ? "Show Less" : "Show All"}
                            </div>
                          )}
                      </div>
                    </div>

                    <div className="flex flex-col gap-2">
                      <div className="flex flex-wrap -space-x-2">
                        <TooltipProvider>
                          {(showAllFarmers
                            ? batchData?.sources.farmers || []
                            : (batchData?.sources.farmers || []).slice(0, 6)
                          ).map((farmer) => (
                            <Tooltip key={farmer.id}>
                              <TooltipTrigger asChild>
                                <div
                                  className="h-10 w-10 rounded-full bg-blue-500 border border-white flex items-center justify-center text-white text-md cursor-pointer hover:bg-blue-600 relative z-10"
                                  onClick={() =>
                                    navigateToFarmerProfile(farmer.id)
                                  }
                                >
                                  {farmer.firstName.charAt(0)}
                                  {farmer.lastName?.charAt(0)}
                                </div>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p className="text-sm">
                                  {farmer.firstName} {farmer.lastName}
                                </p>
                              </TooltipContent>
                            </Tooltip>
                          ))}
                          {!showAllFarmers &&
                            batchData?.sources.farmers &&
                            batchData.sources.farmers.length > 6 && (
                              <div
                                className="h-10 w-10 rounded-full bg-blue-500 border border-white flex items-center justify-center text-white text-md cursor-pointer hover:bg-blue-600 relative z-10"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  toggleFarmersDisplay()
                                }}
                                title="Show all farmers"
                              >
                                +{batchData.sources.farmers.length - 6}
                              </div>
                            )}
                        </TooltipProvider>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm text-gray-500 mb-2">
                      COUNTRY OF ORIGIN
                    </h3>
                    <div className="flex items-center">
                      <MapPin className="h-5 w-5 text-blue-500 mr-2" />
                      <p className="font-medium">
                        {batchData?.sources.country}
                      </p>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm text-gray-500 mb-2">ZONES</h3>
                    <div className="flex items-center">
                      <MapPin className="h-5 w-5 text-blue-500 mr-2" />
                      <p className="font-medium">{batchData?.sources.zone}</p>
                    </div>
                  </div>
                </div>

                {/**Blockchain Section */}
                {/* TODO: Implement when backend is ready */}
                <div>
                  <h3 className="text-sm text-gray-500 mb-2">
                    BLOCKCHAIN RECORDS
                  </h3>

                  <div className="flex max-sm:flex-col gap-2">
                    <div className="relative w-full">
                      <Input
                        value={batchCodeData.harvestingTransactionCode}
                        readOnly
                        className="bg-primary-foreground pr-10 text-sm text-gray-600 font-mono"
                      />
                      <TooltipProvider>
                        <Tooltip open={copied}>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="absolute right-0 top-0 h-full px-3 py-2 text-gray-500 hover:text-gray-700"
                              onClick={copyHarvestingTransactionCode}
                            >
                              {copied ? (
                                <Check className="h-4 w-4 text-green-500" />
                              ) : (
                                <Copy className="h-4 w-4" />
                              )}
                            </Button>
                          </TooltipTrigger>
                        </Tooltip>
                      </TooltipProvider>
                    </div>

                    <Button
                      variant="default"
                      onClick={() =>
                        window.open(
                          `https://cardanoscan.io/transaction/${batchCodeData.harvestingTransactionCode}`
                        )
                      }
                    >
                      <ExternalLink className="h-4 w-4" />
                      View
                    </Button>
                  </div>
                </div>

                {/* Beehives Section */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-sm text-gray-500">BEEHIVES</h3>
                    {batchData && batchData.sources.beehives.length > 6 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={toggleBeehivesDisplay}
                        className="text-blue-600 hover:bg-transparent hover:text-blue-800"
                      >
                        {showAllBeehives ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </Button>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {batchData &&
                      (showAllBeehives
                        ? batchData.sources.beehives
                        : batchData.sources.beehives.slice(
                            0,
                            batchData.sources.beehives.length > 6 ? 5 : 6
                          )
                      ).map((beehive) => (
                        <div
                          key={beehive.id}
                          className="flex bg-primary-foreground border border-gray-200 rounded-md p-3 gap-4"
                        >
                          <div className="h-10 w-10 max-sm:h-full max-sm:w-full bg-amber-100 rounded-md flex items-center justify-center">
                            <Layers className="h-5 w-5 text-amber-600" />
                          </div>
                          <div className="flex flex-col justify-center">
                            <h4 className="text-base font-semibold">
                              {beehive.name}
                            </h4>
                            {showSensitiveInfo && (
                              <div className="flex items-center text-xs text-gray-500">
                                <MapPin className="h-4 w-4 mr-1" />
                                <p>
                                  Lat/Long : {beehive.latitude.toFixed(6)},{" "}
                                  {beehive.longitude.toFixed(6)}
                                </p>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}

                    {!showAllBeehives &&
                      batchData &&
                      batchData.sources.beehives.length > 6 && (
                        <div
                          className="flex bg-primary-foreground border border-gray-200 rounded-md p-3 items-center justify-center cursor-pointer hover:bg-primary-foreground"
                          onClick={toggleBeehivesDisplay}
                        >
                          <div className="text-center">
                            <p className="text-2xl font-bold text-blue-600">
                              +{batchData.sources.beehives.length - 5}
                            </p>
                          </div>
                        </div>
                      )}
                  </div>
                </div>

                {/* Images Section */}
                {/* TODO: Bring back when images are implemented */}
                {/* <div>
                  <h3 className="text-sm text-gray-500 mb-2">IMAGES</h3>
                  <ImageCarousel images={allBeehiveImages} />
                </div> */}
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* Buckets Section */}
          <AccordionItem
            value="buckets"
            className="border rounded-lg px-4 bg-background"
          >
            <AccordionTrigger className="py-4">
              <h2 className="text-lg font-bold flex items-center">
                <span className="h-2 w-2 rounded-full bg-pink-500 mr-2"></span>
                Buckets
              </h2>
            </AccordionTrigger>
            <AccordionContent className="space-y-8">
              <div className="relative space-y-8">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <h3 className="text-sm text-gray-500 mb-2">TOTAL WEIGHT</h3>
                    <div className="flex items-center">
                      <p className="font-medium">
                        {batchData?.buckets.totalWeight} KGs
                      </p>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm text-gray-500 mb-2">
                      SALE DATE RECORDED
                    </h3>
                    <p className="font-medium">
                      {batchData?.buckets.firstSaleDate &&
                        format(batchData?.buckets.firstSaleDate, "do MMM yyyy")}
                    </p>
                  </div>

                  <div>
                    <h3 className="text-sm text-gray-500 mb-2">
                      TOTAL NUMBER OF BUCKETS
                    </h3>
                    <p className="font-medium">
                      {batchData?.buckets.list.length} Buckets
                    </p>
                  </div>
                </div>
                <div>
                  <div className="absolute top-10 right-0 flex justify-end mb-0">
                    {batchData && batchData.buckets.list.length > 6 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={toggleBucketsDisplay}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        {showAllBuckets ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </Button>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {batchData &&
                      (showAllBuckets
                        ? batchData.buckets.list
                        : batchData.buckets.list.slice(
                            0,
                            batchData.buckets.list.length > 6 ? 5 : 6
                          )
                      ).map((bucket) => (
                        <div
                          key={bucket.id}
                          className="flex flex-col gap-2 justify-between items-center bg-primary-foreground border rounded-lg p-3"
                        >
                          <div className="flex justify-between gap-2 ">
                            {showSensitiveInfo && (
                              <p className="bg-blue-500/50 p-1 rounded-lg text-xs font-bold">
                                #{bucket.bucketId}
                              </p>
                            )}

                            <div>
                              <Badge className="bg-yellow-500">HONEYCOMB</Badge>
                            </div>
                          </div>

                          <p className="text-base font-bold mb-1">
                            {bucket.weight} Kg
                          </p>

                          <div className="flex items-center text-xs text-gray-500">
                            <Calendar className="h-4 w-4 mr-1" />
                            <span>
                              Harvested:{" "}
                              {format(bucket.harvestDate, "d-MMM-yyyy")}
                            </span>
                          </div>
                        </div>
                      ))}

                    {!showAllBuckets &&
                      batchData &&
                      batchData.buckets.list.length > 6 && (
                        <div
                          className="flex bg-primary-foreground border rounded-lg p-3 items-center justify-center cursor-pointer hover:bg-primary-foreground"
                          onClick={toggleBucketsDisplay}
                        >
                          <div className="text-center">
                            <p className="text-2xl font-bold text-blue-600">
                              +{batchData.buckets.list.length - 5}
                            </p>
                          </div>
                        </div>
                      )}
                  </div>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          {/*Processing Section*/}
          <AccordionItem
            value="processing"
            className="border rounded-lg px-4 bg-background"
          >
            <AccordionTrigger className="py-4">
              <h2 className="text-lg font-bold flex items-center">
                <span className="h-2 w-2 rounded-full bg-teal-500 mr-2"></span>
                Processing
              </h2>
            </AccordionTrigger>
            <AccordionContent className="pb-4 space-y-8">
              <div className="space-y-8 mb-6">
                <div>
                  <h3 className="text-sm text-gray-500">FACTORY LOCATION</h3>
                  <p>{batchData?.processing.factoryLocation}</p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  <div>
                    <h3 className="text-sm text-gray-500">PROCESS TYPE</h3>
                    <p>{batchData?.processing.processType}</p>
                  </div>
                  <div>
                    <h3 className="text-sm text-gray-500">
                      START PROCESS DATE
                    </h3>
                    <p>
                      {batchData?.processing.startProcessDate &&
                        format(
                          batchData?.processing.startProcessDate,
                          "do MMM yyyy"
                        )}
                    </p>
                  </div>
                  <div>
                    <h3 className="text-sm text-gray-500">END PROCESS DATE</h3>
                    <p>
                      {batchData?.processing.endProcessDate &&
                        format(
                          batchData?.processing.endProcessDate,
                          "do MMM yyyy"
                        )}
                    </p>
                  </div>
                  <div>
                    <h3 className="text-sm text-gray-500">
                      FINAL WEIGHT ESTIMATE
                    </h3>
                    <p>{batchData?.processing.finalWeightOfHoney} KGs</p>
                  </div>

                  {showSensitiveInfo && (
                    <div>
                      <h3 className="text-sm text-gray-500">
                        FINAL WEIGHT OF WAX
                      </h3>
                      <p>{batchData?.processing.finalWeightOfWax} KGs</p>
                    </div>
                  )}
                </div>

                <div>
                  <h3 className="text-sm text-gray-500 mb-2">
                    BLOCKCHAIN RECORDS
                  </h3>

                  <div className="flex max-sm:flex-col gap-2">
                    <div className="relative w-full">
                      <Input
                        value={batchCodeData.processingTransactionCode}
                        readOnly
                        className="bg-primary-foreground pr-10 text-sm text-gray-600 font-mono"
                      />
                      <TooltipProvider>
                        <Tooltip open={copied}>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="absolute right-0 top-0 h-full px-3 py-2 text-gray-500 hover:text-gray-700"
                              onClick={copyProcessingTransactionCode}
                            >
                              {copied ? (
                                <Check className="h-4 w-4 text-green-500" />
                              ) : (
                                <Copy className="h-4 w-4" />
                              )}
                            </Button>
                          </TooltipTrigger>
                        </Tooltip>
                      </TooltipProvider>
                    </div>

                    <Button
                      variant="default"
                      onClick={() =>
                        window.open(
                          `https://cardanoscan.io/transaction/${batchCodeData.processingTransactionCode}`
                        )
                      }
                    >
                      <ExternalLink className="h-4 w-4" />
                      View
                    </Button>
                  </div>
                </div>

                <div>
                  {batchCodeData &&
                    batchCodeData.details &&
                    batchCodeData.details.length > 0 && (
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="text-sm text-gray-500">
                            PROCESSING DETAILS
                          </h3>
                          {batchCodeData.details.length > 3 && (
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={toggleDetailsDisplay}
                              className="h-6 w-6 text-blue-600 hover:bg-transparent hover:text-blue-800"
                            >
                              {showAllDetails ? (
                                <ChevronUp className="h-4 w-4" />
                              ) : (
                                <ChevronDown className="h-4 w-4" />
                              )}
                            </Button>
                          )}
                        </div>

                        <div className="overflow-x-auto">
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead className="text-center">
                                  Day
                                </TableHead>
                                <TableHead className="text-center">
                                  Date
                                </TableHead>
                                <TableHead className="text-center">
                                  Week Number
                                </TableHead>
                                <TableHead className="text-center">
                                  Number of Buckets
                                </TableHead>
                                <TableHead className="text-center">
                                  Bucket Weight
                                </TableHead>
                                <TableHead className="text-center">
                                  Wax
                                </TableHead>
                                <TableHead className="text-center">
                                  Estimated Weight
                                </TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {(showAllDetails
                                ? batchData?.processing.dailySummaries || []
                                : (
                                    batchData?.processing.dailySummaries || []
                                  ).slice(0, 3)
                              ).map((dailySummary, index) => (
                                <TableRow key={index}>
                                  <TableCell className="text-center">
                                    {format(new Date(dailySummary.date), "EE")}
                                  </TableCell>
                                  <TableCell className="text-center">
                                    {format(dailySummary.date, "d-MMM")}
                                  </TableCell>
                                  <TableCell className="text-center">
                                    {format(new Date(dailySummary.date), "w")}
                                  </TableCell>
                                  <TableCell className="text-center">
                                    {dailySummary.numberOfBuckets}
                                  </TableCell>
                                  <TableCell className="text-center">
                                    {dailySummary.totalWeight}
                                  </TableCell>
                                  <TableCell className="text-center">
                                    {dailySummary.waxProduced}
                                  </TableCell>
                                  <TableCell className="text-center">
                                    {dailySummary.estimatedHoneyWeight}
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </div>

                        {!showAllDetails &&
                          batchData?.processing.dailySummaries &&
                          batchData?.processing.dailySummaries.length > 3 && (
                            <div className="mt-2 text-center">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={toggleDetailsDisplay}
                                className="text-blue-600 hover:text-blue-800"
                              >
                                Show{" "}
                                {batchData?.processing.dailySummaries.length -
                                  3}{" "}
                                more days
                                <ChevronDown className="h-4 w-4 ml-1" />
                              </Button>
                            </div>
                          )}
                      </div>
                    )}
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </CardContent>

      {/*Share Dialog*/}
      <Dialog open={shareDialogOpen} onOpenChange={setShareDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-xl">
              <span className="h-2 w-2 rounded-full bg-blue-500"></span>
              Share Batch information
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            <div className="space-y-2">
              <h3 className="text-sm text-gray-500">Share type</h3>

              <RadioGroup
                value={shareType}
                onValueChange={setShareType}
                className="flex"
              >
                <div
                  className={cn(
                    "flex items-center justify-center bg-white border shadow rounded-md px-4 py-2 gap-2 cursor-pointer"
                  )}
                >
                  <RadioGroupItem value="original" id="original" />
                  <Label htmlFor="original" className="font-medium">
                    Original
                  </Label>
                </div>
                <div
                  className={cn(
                    "flex items-center justify-center bg-white border shadow rounded-md px-4 py-2 gap-2 cursor-pointer"
                  )}
                >
                  <RadioGroupItem value="redacted" id="redacted" />
                  <Label htmlFor="redacted" className="font-medium">
                    Redacted Customer View
                  </Label>
                </div>
              </RadioGroup>

              <p className="text-xs text-gray-500 mt-2">
                Original will include all details, farmer names, beehives
                locations, etc. Redacted will hide sensitive information like
                farmer names, beehives locations, etc.
              </p>
            </div>

            <div className="space-y-2">
              <h3 className="text-sm text-gray-500">Shareable Link</h3>
              <div className="flex items-center space-x-2">
                <div className="relative flex-1">
                  <Input
                    value={shareableLink}
                    readOnly
                    className="pr-10 font-mono text-sm"
                  />
                  <Button
                    size="sm"
                    variant="ghost"
                    className="absolute right-0 top-0 h-full px-3"
                    onClick={copyShareableLink}
                  >
                    {copiedShareLink ? (
                      <Check className="h-4 w-4" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="text-sm text-gray-500">Embed Code</h3>
              <div className="flex items-center space-x-2">
                <div className="relative flex-1">
                  <Input
                    value={embedCode}
                    readOnly
                    className="pr-10 font-mono text-sm"
                  />
                  <Button
                    size="sm"
                    variant="ghost"
                    className="absolute right-0 top-0 h-full px-3"
                    onClick={copyEmbedCode}
                  >
                    {copiedEmbedCode ? (
                      <Check className="h-4 w-4" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="text-sm text-gray-500">QR Code</h3>
              <div className="flex flex-col items-center space-y-4">
                <div className="border border-gray-200 p-4 rounded-lg">
                  <QRCodeCanvas value={shareableLink} size={128} />
                </div>
                <Button
                  variant="default"
                  size="sm"
                  onClick={handleShare}
                  className="flex sm:hidden"
                >
                  <Link className="h-4 w-4 mr-1" /> Share QR Code
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
      {error && (
        <div className="fixed bottom-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded shadow-md z-50 max-w-md">
          <div className="flex items-center">
            <svg
              className="h-5 w-5 mr-2"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
            <p>{error}</p>
          </div>
        </div>
      )}
    </div>
  )
}
