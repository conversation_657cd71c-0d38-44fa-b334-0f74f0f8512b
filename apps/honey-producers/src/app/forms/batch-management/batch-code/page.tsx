// app/batch-code/page.tsx
"use client"

import { useEffect, useState } from "react"
import { BatchCodeDetailsContainer } from "./batch-code-details-container"
import { batchCodeTraceability, BatchCode } from "@repo/dummy/batch-management"
import { Loader } from "lucide-react"

// TODO: Remove eslint-disable after adding proper API call
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const getBatchCodeData = async (batchCode: string): Promise<BatchCode> =>
  new Promise((resolve) => {
    setTimeout(() => {
      // Find the batch code with the matching ID
      const foundBatchCode =
        batchCodeTraceability.find((f) => f.batchCode === batchCode) ||
        batchCodeTraceability[0]
      resolve(foundBatchCode)
    }, 500) // Simulated API delay
  })

export default function BatchCodeTraceabilityPage() {
  const [batchCodeData, setBatchCodeData] = useState<BatchCode | null>(null)
  const [batchCode, setBatchCode] = useState<string | null>(null)

  useEffect(() => {
    const hash = window.location.hash.replace("#", "")
    if (!hash) return

    setBatchCode(hash)

    getBatchCodeData(hash).then((data) => setBatchCodeData(data))
  }, [])

  if (!batchCodeData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader className="h-6 w-6 animate-spin text-primary mr-2" />
        <p>Loading...</p>
      </div>
    )
  }

  return (
    <div className="container bg-primary-foreground border shadow rounded-lg mx-auto max-w-screen-xl pt-6 pb-2 space-y-8">
      <BatchCodeDetailsContainer
        batchCodeData={batchCodeData}
        batchCode={batchCode ?? ""}
      />
    </div>
  )
}
