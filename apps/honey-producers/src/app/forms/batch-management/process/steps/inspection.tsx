import { FC } from "react"
import { NextButton } from "@/components/form/next-button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { FormValues } from "@repo/validation/batch-management"
import { useFormContext } from "react-hook-form"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { Input } from "@/components/ui/input"
import { ArrowLeft, Save } from "lucide-react"
export interface InspectionStepFormData {
  moistureContent: number
  smellInspection: string
  bubbles: string
  foreignParticles: string
  anythingElse: string
  drumIds: string[]
  smellComments?: string
  additionalComments?: string
}

interface Props {
  handleNext: () => void
  handlePrevious: () => void

  isLast: boolean
  formPath?: string
  formData?: InspectionStepFormData
  isLoading: boolean
}
export const Inspection: FC<Props> = ({
  handleNext,
  handlePrevious,
  isLast,
  formPath,
  isLoading,
}) => {
  const router = useRouter()
  const { control } = useFormContext<FormValues>()

  return (
    <div className="space-y-6">
      <Card className="bg-primary-foreground border flex flex-col gap-2">
        <CardHeader>
          <CardTitle>Inspection</CardTitle>
        </CardHeader>

        <CardContent>
          <FormField
            control={control}
            name="moistureContent"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-semibold">
                  What is the Moisture Content?
                </FormLabel>
                <div className="relative flex flex-row">
                  <FormControl className="w-52 bg-background">
                    <Input
                      type="number"
                      step="0.01"
                      placeholder="Enter moisture content"
                      className="pr-8"
                      {...field}
                      onChange={(e) => {
                        const value =
                          e.target.value === ""
                            ? undefined
                            : Number(e.target.value)
                        field.onChange(value)
                      }}
                      value={field.value !== undefined ? field.value : ""}
                    />
                  </FormControl>
                  <div className="inset-y-0 -ml-6 flex items-center pr-3 pointer-events-none">
                    <span className="text-sm text-muted-foreground">%</span>
                  </div>
                </div>
                <FormDescription className="text-xs">
                  Enter a value between 0% and 100%
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>

        <CardContent>
          <FormField
            control={control}
            name="smellInspection"
            render={({ field }) => (
              <FormItem className="space-y-3 pt-2">
                <FormLabel className="font-semibold">
                  Smell: Does this pass the smell inspection?
                </FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className="flex space-x-4"
                  >
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="yes" />
                      </FormControl>
                      <FormLabel className="font-normal">Yes</FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="no" />
                      </FormControl>
                      <FormLabel className="font-normal">No</FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>

        <CardContent>
          <FormField
            control={control}
            name="smellComments"
            render={({ field }) => (
              <FormItem className="pt-2">
                <FormLabel className="font-semibold">
                  Comments on smell
                </FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Type here..."
                    className="resize-none bg-background"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>

        <CardContent>
          <FormField
            control={control}
            name="bubbles"
            render={({ field }) => (
              <FormItem className="space-y-3 pt-2">
                <FormLabel className="font-semibold">
                  Visual: Do you see any bubbles?
                </FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className="flex space-x-4"
                  >
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="yes" />
                      </FormControl>
                      <FormLabel className="font-normal">Yes</FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="no" />
                      </FormControl>
                      <FormLabel className="font-normal">No</FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>

        <CardContent>
          <FormField
            control={control}
            name="foreignParticles"
            render={({ field }) => (
              <FormItem className="space-y-3 pt-2">
                <FormLabel className="font-semibold">
                  Visual: Do you see any foreign particles or foam?
                </FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className="flex space-x-4"
                  >
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="yes" />
                      </FormControl>
                      <FormLabel className="font-normal">Yes</FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="no" />
                      </FormControl>
                      <FormLabel className="font-normal">No</FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
        <CardContent>
          <FormField
            control={control}
            name="anythingElse"
            render={({ field }) => (
              <FormItem className="space-y-3 pt-2">
                <FormLabel className="font-semibold">
                  Visual: Anything else to report?
                </FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className="flex space-x-4"
                  >
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="yes" />
                      </FormControl>
                      <FormLabel className="font-normal">Yes</FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="no" />
                      </FormControl>
                      <FormLabel className="font-normal">No</FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
        <CardContent>
          <FormField
            control={control}
            name="additionalComments"
            render={({ field }) => (
              <FormItem className="pt-2 mb-0">
                <FormLabel className="font-semibold">
                  Additional comments
                </FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Type here..."
                    className="resize-none bg-background"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
      </Card>

      <div className="flex gap-2 items-center justify-center md:justify-between">
        <Button className="mt-4" variant="outline" onClick={handlePrevious}>
          <ArrowLeft className="w-4 h-4" />
        </Button>
        <div className="flex items-center gap-2">
          <Button
            className="mt-4"
            variant="outline"
            onClick={() => router.push("/forms/batch-management")}
          >
            <Save className="w-5 h-5" />
            Save and Exit
          </Button>

          <NextButton
            isLast={isLast}
            handleNext={handleNext}
            formPath={formPath}
            isLoading={isLoading}
          />
        </div>
      </div>
    </div>
  )
}
