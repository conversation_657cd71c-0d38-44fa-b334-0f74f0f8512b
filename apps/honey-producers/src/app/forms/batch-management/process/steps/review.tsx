import { FC, useState } from "react"
import { Card, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { useFormContext } from "react-hook-form"
import { useSearchParams } from "next/navigation"
import { FormValues } from "@repo/validation/batch-management"
import { ArrowLeft, Pencil } from "lucide-react"
import { format } from "date-fns"
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { DetailsValue } from "@/components/details-value"

interface Props {
  handlePrevious: () => void
  handleNext: () => void
  handleEdit: (sectionId: number) => void

  formData?: any
  isLoading: boolean
}

export const Review: FC<Props> = ({
  handleNext,
  handlePrevious,
  handleEdit,
}) => {
  const searchParams = useSearchParams()
  const { getValues } = useFormContext<FormValues>()

  const values = getValues()
  const selectedBatchCodes =
    searchParams.get("selectedBatchCodes")?.split(",") ?? []

  return (
    <div className="my-4">
      <Card className="bg-primary-foreground border flex flex-col gap-2">
        <CardHeader>
          <CardTitle>Review</CardTitle>
        </CardHeader>
        <CardContent>
          <Accordion
            type="multiple"
            defaultValue={["honeyCombInputs", "processing"]}
            className="w-full"
          >
            <AccordionItem value="honeyCombInputs">
              <AccordionTrigger className="group py-2">
                <div className="w-full flex justify-between">
                  <div className="flex items-center gap-2">
                    <span className="h-2 w-2 rounded-full bg-pink-500" />
                    <span className="font-semibold">QA Checks</span>
                  </div>

                  <div>
                    <Pencil
                      className="w-4 h-4 mr-3 "
                      onClick={() => {
                        handleEdit(1)
                      }}
                    ></Pencil>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent className="space-y-4 pt-4">
                <div className="bg-background border shadow rounded-lg p-6 flex flex-col gap-6">
                  <DetailsValue
                    label="Testing date"
                    value={format(values.testingDate, "MMMM d yyyy")}
                  ></DetailsValue>

                  <DetailsValue
                    label="Name of qa tester"
                    value={values.testerName}
                  ></DetailsValue>

                  <DetailsValue
                    label="Moisture content"
                    value={values.moistureContent}
                  />
                  <DetailsValue
                    className="capitalize"
                    label="Smell: Does this pass the smell inspection?"
                    value={values.smellInspection}
                  />
                  <DetailsValue
                    label="Comments on smell"
                    value={values.smellComments || "No comment"}
                  />
                  <DetailsValue
                    className="capitalize"
                    label="Visual: Do you see any bubbles?"
                    value={values.bubbles}
                  />
                  <DetailsValue
                    className="capitalize"
                    label=" Visual: Do you see any foreign particles of foam?"
                    value={values.foreignParticles}
                  />
                  <DetailsValue
                    className="capitalize"
                    label="Visual: Anything else to report?"
                    value={values.anythingElse}
                  />
                  <DetailsValue
                    label="Additional comments"
                    value={values.additionalComments || "No Comment"}
                  />
                </div>
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="processing">
              <AccordionTrigger className="group">
                <div className="w-full flex justify-between">
                  <div className="flex items-center gap-2">
                    <span className="h-2 w-2 rounded-full bg-teal-500" />
                    <span className="font-semibold">Drums</span>
                  </div>
                  <Pencil
                    className="w-4 h-4 mr-3"
                    onClick={() => {
                      handleEdit(3)
                    }}
                  ></Pencil>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                {values.drums.map((drum) => (
                  <div
                    key={drum.drumId}
                    className="flex justify-around items-center max-sm:flex-col bg-background border shadow rounded-lg p-4 space-y-4"
                  >
                    <DetailsValue
                      className="capitalize"
                      label="ID"
                      value={`#${drum.drumId}`}
                    />
                    <DetailsValue
                      className="capitalize"
                      label="Seal Status"
                      value={`${drum.sealStatus}`}
                    />
                    <DetailsValue
                      className="capitalize"
                      label="Weight"
                      value={`${drum.weight}`}
                    />
                    <DetailsValue
                      className="capitalize"
                      label="Poured Timestamp"
                      value={`${drum.pouredTimestamp}`}
                    />
                    <DetailsValue
                      className="capitalize"
                      label="Sealed Timestamp"
                      value={`${drum.sealedTimestamp}`}
                    />
                  </div>
                ))}
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </CardContent>
      </Card>
      <div className="flex flex-row gap-3 justify-between">
        <div>
          <Button variant="outline" onClick={handlePrevious} className="mt-4">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
        </div>
        <div className="flex gap-3">
          <Button onClick={handleNext} className="w-full mt-4" type="submit">
            {selectedBatchCodes.length === 1 ? "Finalize" : "Combine"}
          </Button>
        </div>
      </div>
    </div>
  )
}
