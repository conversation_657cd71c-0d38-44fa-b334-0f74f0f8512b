"use client"

import { FC } from "react"
import { Card, CardContent, CardTitle } from "@/components/ui/card"
import { Weight, Disc, CheckCircle, Disc2 } from "lucide-react"
import { useRouter, useSearchParams } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { format } from "date-fns"
import { useFormContext } from "react-hook-form"
import { FormValues } from "@repo/validation/batch-management"

export interface CompleteStepFormData {
  selectedBatchCodes: string
  maxWeight: number
}

interface Props {
  handleNext: () => void
  isLast: boolean
  formPath?: string
  formData?: CompleteStepFormData
  isLoading?: boolean
}

export const CompleteStep: FC<Props> = () => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { getValues } = useFormContext<FormValues>()
  const selectedBatchCodes = searchParams.get("selectedBatchCodes")
  const maxWeight = searchParams.get("maxWeight")
  const values = getValues()
  const totalDrums = values.drums.length || 0
  const totalDrumsWeight = values.drums.reduce((acc, drum) => {
    const weight = Number(drum.weight) || 0
    return acc + weight
  }, 0)

  const routeToBatchManagement = () => {
    const BatchManagementPage = "/forms/batch-management"
    router.push(BatchManagementPage)
  }
  return (
    <div className="w-full mx-auto flex flex-col gap-6 items-center justify-center min-h-[calc(100vh-14rem)]">
      <Card className="w-full mx-auto bg-primary-foreground">
        <CardContent className="text-center">
          <div className="flex flex-col items-center justify-center pt-6 mb-6">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <CheckCircle className="w-10 h-10 text-green-600" />
            </div>
            <CardTitle className="text-2xl font-semibold mb-2">
              Batches successfully{" "}
              {(selectedBatchCodes?.split(",")?.length || 0) > 1
                ? "combined"
                : "finalized"}{" "}
              and added to drums!
            </CardTitle>
            <p className="text-gray-500 mb-6 w-10/12">
              The inspection and drumming process was successfully completed on{" "}
              {format(new Date(), "yyyy-MM-dd (HH:mm)")} and recorded in the
              system as a finalized batch under Batch Management.
            </p>

            <div className="bg-blue-500/10 rounded-lg p-4 w-full text-center">
              <h3 className="font-medium text-blue-800 mb-2">
                Batch Code:{" "}
                <span className="font-bold">{selectedBatchCodes}</span>
              </h3>
              <p className="text-gray-500 ">
                This Batch Code can be used to identify the process in the
                system.
              </p>
            </div>
          </div>

          <div className="flex justify-around max-sm:flex-col space-y-4 items-center">
            <div className="flex justify-center gap-2">
              <Weight className="h-5 w-5 text-muted-foreground" />
              <div className="flex flex-col space-y-1">
                <p className="text-muted-foreground text-sm">Max Weight</p>
                <p className="font-medium">{maxWeight} KGs</p>
              </div>
            </div>
            <div className="flex justify-center gap-2">
              <Weight className="h-5 w-5 text-muted-foreground" />
              <div className="flex flex-col space-y-1">
                <p className="text-muted-foreground text-sm">
                  Total Drums Weight
                </p>
                <p className="font-medium">{totalDrumsWeight} KGs</p>
              </div>
            </div>
            <div className="flex justify-center gap-2">
              <Disc2 className="h-5 w-5 text-muted-foreground" />
              <div className="flex flex-col space-y-1">
                <p className="text-muted-foreground text-sm">Drums</p>
                <p className="font-medium">{totalDrums} </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="w-full">
        <Button
          onClick={routeToBatchManagement}
          className="w-full mt-4"
          type="submit"
        >
          Go back to Batch Management
        </Button>
      </div>
    </div>
  )
}
