import { NextButton } from "@/components/form/next-button"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { FormValues } from "@repo/validation/batch-management"
import { CalendarIcon } from "lucide-react"
import { FC } from "react"
import { useFormContext } from "react-hook-form"
import { format, isAfter } from "date-fns"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { qaTesters } from "@repo/dummy/batch-management"

export interface TesterStepFormData {
  testerDate: Date
  testerName: string
}

interface Props {
  handleNext: () => void
  handlePrevious: () => void
  handleEdit: (sectionId: number) => void

  isLast: boolean
  formPath?: string
  formData?: TesterStepFormData
  isLoading: boolean
}

export const Tester: FC<Props> = ({
  handleNext,
  isLast,
  formPath,
  isLoading,
}) => {
  const { control } = useFormContext<FormValues>()

  return (
    <div className="space-y-6">
      <Card className="bg-primary-foreground border flex flex-col gap-2">
        <CardHeader>
          <CardTitle>Tester</CardTitle>
        </CardHeader>

        <CardContent>
          <FormField
            control={control}
            name="testingDate"
            render={({ field }) => (
              <FormItem className="flex flex-col mt-2">
                <FormLabel className="font-semibold">Testing Date</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className="pl-3 text-left font-normal w-36"
                      >
                        {field.value ? (
                          format(field.value, "d MMM yyyy")
                        ) : (
                          <span>Select a date</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      initialFocus
                      disabled={(date) => isAfter(date, new Date())}
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>

        <CardContent>
          <FormField
            control={control}
            name="testerName"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel className="font-semibold ">
                  Name of QA Tester
                </FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl className="w-44 bg-background ">
                    <SelectTrigger>
                      <SelectValue placeholder="Select name" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {qaTesters.map((tester) => (
                      <SelectItem key={tester} value={tester}>
                        {tester}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
      </Card>

      <NextButton
        isLast={isLast}
        handleNext={handleNext}
        formPath={formPath}
        isLoading={isLoading}
      />
    </div>
  )
}
