"use client"

import { FC, useState } from "react"
import {
  Controller,
  useFieldArray,
  useFormContext,
  useWatch,
} from "react-hook-form"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { FormLabel } from "@/components/ui/form"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { NextButton } from "@/components/form/next-button"
import {
  ArrowLeft,
  CheckIcon,
  Disc2,
  Plus,
  Save,
  Weight,
  X,
} from "lucide-react"
import { FormValues } from "@repo/validation/batch-management"
import { useRouter } from "next/navigation"
import { SealStatus } from "@/constants/seal-status"
import { format } from "date-fns"
import { useSearchParams } from "next/navigation"
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog"

interface Props {
  handleNext: () => void
  handlePrevious: () => void
  isLast: boolean
  formPath?: string
  isLoading: boolean
}

export const Drum: FC<Props> = ({
  handleNext,
  handlePrevious,
  isLast,
  formPath,
  isLoading,
}) => {
  const router = useRouter()
  const { control, trigger, getValues } = useFormContext<FormValues>()
  const { fields, append, remove, update } = useFieldArray({
    control,
    name: "drums",
  })

  const drums = useWatch({
    control,
    name: "drums",
    defaultValue: [],
  })
  const searchParams = useSearchParams()
  const maxWeight = Number(searchParams.get("maxWeight"))
  const [tempDrumId, setTempDrumId] = useState("")
  const [tempWeight, setTempWeight] = useState<number | undefined>(undefined)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [selectedDrumIndex, setSelectedDrumIndex] = useState<number>(0)
  const totalWeight = drums.reduce((acc, d) => acc + (d.weight || 0), 0)
  const totalDrums = drums.length
  const [invalidDrumIndex, setInvalidDrumIndex] = useState<number | null>(null)

  const isDrumIdDuplicate = (indexToCheck?: number): number | null => {
    const seen = new Set()
    for (let i = 0; i < drums.length; i++) {
      const drumId = drums[i].drumId
      if (seen.has(drumId)) {
        if (indexToCheck === undefined || i === indexToCheck) {
          return i
        }
      }
      seen.add(drumId)
    }
    return null
  }

  const handleLockDrum = (drumIndex: number) => {
    setDialogOpen(true)
    setSelectedDrumIndex(drumIndex)
  }

  const confirmLockDrum = () => {
    setDialogOpen(false)
    lockItem(selectedDrumIndex)
  }

  const handleNextStep = async () => {
    const isValid = await trigger("drums")
    const isAllLocked = drums.every((d) => d.isLocked)
    const duplicateIndex = isDrumIdDuplicate()

    if (duplicateIndex !== null) {
      setInvalidDrumIndex(duplicateIndex)
      return
    }

    setInvalidDrumIndex(null)

    if (isValid && isAllLocked) {
      handleNext()
    }
  }

  const addDrum = () => {
    if (!tempDrumId || tempWeight === undefined) return

    const currentDrums = getValues("drums") ?? []
    if (currentDrums.some((d) => d.drumId === tempDrumId)) {
      setInvalidDrumIndex(
        currentDrums.findIndex((d) => d.drumId === tempDrumId)
      )
      return
    }

    append({
      drumId: tempDrumId,
      weight: tempWeight,
      sealStatus: SealStatus.Unsealed,
      isLocked: false,
      sealedTimestamp: format(new Date(), "yyyy-MM-dd HH:mm:ss"),
      pouredTimestamp: format(new Date(), "yyyy-MM-dd HH:mm:ss"),
    })

    setTempDrumId("")
    setTempWeight(undefined)
    setInvalidDrumIndex(null)
  }

  const removeDrum = (index: number) => {
    remove(index)
  }

  const lockItem = async (index: number) => {
    const isLocked = getValues(`drums.${index}.isLocked`) ?? false
    const isValid = await trigger(`drums.${index}`)
    const duplicateIndex = isDrumIdDuplicate(index)
    if (duplicateIndex !== null) {
      setInvalidDrumIndex(duplicateIndex)
      return
    }
    setInvalidDrumIndex(null)
    if (!isValid && !isLocked) return
    const updated = {
      ...getValues(`drums.${index}`),
      isLocked: !isLocked,
      sealStatus: isLocked ? SealStatus.Unsealed : SealStatus.Sealed,
      sealedTimestamp: format(new Date(), "yyyy-MM-dd HH:mm:ss"),
    }
    update(index, updated)
  }

  return (
    <div className="space-y-6">
      <Card className="bg-primary-foreground border">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Drums</CardTitle>
        </CardHeader>

        <CardContent className="space-y-6 pb-3 px-3">
          <Card className="max-sm:flex-col p-3">
            <div className="flex flex-col gap-4">
              <div className="flex max-sm:flex-col gap-4">
                <div className="flex-1">
                  <FormLabel>Drum ID *</FormLabel>
                  <Input
                    placeholder="Enter Drum ID"
                    value={tempDrumId}
                    onChange={(e) => setTempDrumId(e.target.value)}
                  />
                  {invalidDrumIndex !== null &&
                    drums[invalidDrumIndex]?.drumId === tempDrumId && (
                      <p className="text-red-500 text-sm mt-1">
                        Drum ID already exists
                      </p>
                    )}
                </div>
                <div className="flex-1">
                  <FormLabel>Drum Weight *</FormLabel>
                  <Input
                    type="number"
                    placeholder="Enter weight (kg)"
                    value={tempWeight ?? ""}
                    onChange={(e) =>
                      setTempWeight(
                        e.target.value === "" ? undefined : +e.target.value
                      )
                    }
                  />
                </div>
              </div>
              <div className="flex-1">
                <Button
                  variant="default"
                  type="button"
                  onClick={() => {
                    addDrum()
                  }}
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Drum
                </Button>
              </div>
            </div>
          </Card>

          {fields.length > 0 && (
            <div className="flex flex-col gap-4">
              {fields.map((field, index) => {
                const drum = getValues("drums")[index]
                return (
                  <div
                    key={field.id}
                    className={`${drum?.isLocked ? "bg-blue-50" : "bg-background"} shadow rounded-lg p-3 flex gap-8 max-sm:gap-2 items-center`}
                  >
                    <div
                      className={`${drum.isLocked ? "w-full" : " w-10/12 max-sm:w-3/4"} flex justify-around max-sm:flex-wrap flex-cols-3 gap-2 text-center items-center `}
                    >
                      <div>
                        <div className="text-sm text-gray-400">Drum ID</div>
                        <div className="">#{drum?.drumId}</div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-400">Weight</div>
                        <div className="">{drum?.weight} KG</div>
                      </div>
                      <div>
                        <div className="w-fit p-2 text-xs font-semibold rounded-lg bg-blue-100 text-gray-600">
                          {drum?.sealStatus === SealStatus.Unsealed
                            ? SealStatus.Unsealed
                            : SealStatus.Sealed}
                        </div>
                      </div>
                    </div>
                    {drum?.isLocked ? null : (
                      <div className="flex max-sm:flex-col gap-2">
                        <Button
                          type="button"
                          variant="outline"
                          size="icon"
                          onClick={() => removeDrum(index)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                        <Button
                          type="button"
                          size="icon"
                          variant="default"
                          disabled={totalWeight > maxWeight}
                          onClick={() => handleLockDrum(index)}
                        >
                          <CheckIcon className="h-3 w-3" />
                          <Controller
                            name={`drums.${index}.isLocked`}
                            control={control}
                            render={({ field }) => (
                              <input
                                type="hidden"
                                {...field}
                                value={field.value.toString()}
                              />
                            )}
                            defaultValue={false}
                          />
                        </Button>
                      </div>
                    )}
                  </div>
                )
              })}
            </div>
          )}

          <div className="flex md:justify-around max-sm:flex-wrap-reverse gap-4">
            <div className="flex justify-center gap-2">
              <Weight className="h-5 w-5 text-muted-foreground" />
              <div className="flex flex-col space-y-1">
                <p className="text-muted-foreground text-sm">Total Weight</p>
                <p className="font-medium">{totalWeight} KGs</p>
                <p className="text-red-600 text-sm">
                  {totalWeight > maxWeight &&
                    "* Total weight exceeds max weight"}
                </p>
              </div>
            </div>
            <div className="flex justify-center gap-2">
              <Weight className="h-5 w-5 text-muted-foreground" />
              <div className="flex flex-col space-y-1">
                <p className="text-muted-foreground text-sm">Max Weight</p>
                <p className="font-medium">{maxWeight} KGs</p>
              </div>
            </div>
            <div className="flex justify-center gap-2">
              <Disc2 className="h-5 w-5 text-muted-foreground" />
              <div className="flex flex-col space-y-1">
                <p className="text-muted-foreground text-sm">Drums</p>
                <p className="font-medium">{totalDrums}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-2 items-center justify-center md:justify-between">
        <Button className="mt-4" variant="outline" onClick={handlePrevious}>
          <ArrowLeft className="w-4 h-4" />
        </Button>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => router.push("/forms/batch-management")}
            className="mt-4"
          >
            <Save className="w-5 h-5" />
            Save and Exit
          </Button>

          <NextButton
            isLast={isLast}
            handleNext={handleNextStep}
            formPath={formPath}
            isLoading={isLoading}
            disabled={
              !drums.every((d) => d.isLocked) || totalWeight > maxWeight
            }
          />
        </div>
      </div>

      <AlertDialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Sealing</AlertDialogTitle>
            <AlertDialogDescription>
              Sealing this drum is permanent and{" "}
              <span className="font-semibold text-red-500">
                cannot be undone.
              </span>{" "}
              Please confirm that you wish to proceed.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmLockDrum}>
              Seal
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
