"use client"
import { DateRang<PERSON> } from "react-day-picker"
import { zodResolver } from "@hookform/resolvers/zod"
import {
  FormFieldNames,
  formSchema,
  type FormValues,
} from "@repo/validation/batch-management"
import { use<PERSON>orm, SubmitHandler, FormProvider } from "react-hook-form"
import { useState } from "react"
import { useQuery } from "@tanstack/react-query"
import { fetchRecords } from "./components/temporary-fetch-records"
import { Step, Stepper } from "@/components/form/stepper"
import { Tester, TesterStepFormData } from "./steps/tester"
import { Inspection } from "./steps/inspection"
import { useRouter } from "next/navigation"
import { Drum } from "./steps/drum"
import { Review } from "./steps/review"
import { useSearchParams } from "next/navigation"
import { CompleteStep } from "./steps/complete"

export default function BatchProcessingPage() {
  const searchParams = useSearchParams()
  const [step, setStep] = useState(1)
  const router = useRouter()
  const backManagementUrlPath = "/forms/batch-management"
  const [formData] = useState<TesterStepFormData>()
  const [searchTerm] = useState("")
  const [dateRange] = useState<DateRange>()
  const [sortOrder] = useState("date-desc")
  const selectedBatchCodes =
    searchParams.get("selectedBatchCodes")?.split(",") ?? []
  const maxWeight = Number(searchParams.get("maxWeight")) || 0
  const { isLoading } = useQuery({
    queryKey: ["records", searchTerm, dateRange, sortOrder],
    queryFn: () => fetchRecords(searchTerm, dateRange, sortOrder),
    staleTime: 60000,
  })

  const methods = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      testingDate: new Date(),
      testerName: "",
      moistureContent: undefined,
      smellInspection: undefined,
      smellComments: "",
      bubbles: undefined,
      foreignParticles: undefined,
      anythingElse: undefined,
      additionalComments: "",
      drums: [],
    },
  })

  const stepComponents = [Tester, Inspection, Drum, Review, CompleteStep]
  const stepSections = [
    { id: 1, label: "Tester", range: [1, 1] },
    { id: 2, label: "Inspection", range: [2, 2] },
    { id: 3, label: "Drum", range: [3, 3] },
    { id: 4, label: "Review", range: [4, 4] },
    { id: 5, label: "Finish", range: [5, 5] },
  ]

  const stepperState: Step[] = stepSections.map(({ id, label, range }) => {
    const [start, end] = range
    return {
      id,
      label,
      status:
        step > end
          ? "completed"
          : step >= start && step <= end
            ? "current"
            : "upcoming",
    }
  })

  const moveToPreviousStep = () => {
    step === 1 ? router.push(backManagementUrlPath) : setStep(step - 1)
  }

  const moveToNextStep = async () => {
    const isValid = await methods.trigger(getCurrentStepFieldNames(step))

    if (isValid) {
      setStep(step + 1)
    }
  }
  const handleEdit = (sectionId: number) => {
    const stepId = stepSections.find((section) => section.id === sectionId)
      ?.range[0]

    if (!stepId) return

    setStep(stepId)
  }

  const renderStep = () => {
    const CurrentStepComponent = stepComponents[step - 1]

    return (
      <CurrentStepComponent
        handleNext={moveToNextStep}
        handlePrevious={moveToPreviousStep}
        isLast={step === stepComponents.length}
        formPath={formData && `${backManagementUrlPath}/test`}
        handleEdit={handleEdit}
        isLoading={isLoading}
      />
    )
  }

  const getCurrentStepFieldNames = (step: number) => {
    const stepFields: Record<number, FormFieldNames[]> = {
      1: ["testerName", "testingDate"],
      2: [
        "moistureContent",
        "smellInspection",
        "bubbles",
        "foreignParticles",
        "anythingElse",
      ],
      3: ["drums"],
    }
    return stepFields[step] || []
  }

  const onSubmit: SubmitHandler<FormValues> = async (data) => {
    const isValid = await methods.trigger()

    if (!isValid) return

    if (isValid) {
      const totalWeightFormatted = maxWeight.toFixed(2)
      const submissionData = {
        qaData: data,
        batches: selectedBatchCodes,
        totalWeight: totalWeightFormatted,
        drums: data.drums,
        operation: selectedBatchCodes.length > 1 ? "combine" : "finalize",
        targetBatchCode: selectedBatchCodes,
      }

      console.log("Complete data submitted:", submissionData)
      methods.reset()
    }
  }

  return (
    <FormProvider {...methods}>
      <div className="flex items-center flex-col md:mx-4">
        <Stepper
          steps={stepperState}
          progress={(step / stepComponents.length) * 100}
          onBack={moveToPreviousStep}
          backDisabled={!!formData}
        />
        <div className="w-full max-w-[800px]">
          <form onSubmit={methods.handleSubmit(onSubmit)}>{renderStep()}</form>
        </div>
      </div>
    </FormProvider>
  )
}
