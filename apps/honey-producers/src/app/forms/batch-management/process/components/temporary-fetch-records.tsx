import { BatchManagementListItem, batchManagementList } from "@repo/dummy/batch-management"
import { isWithinInterval } from "date-fns"
import { DateRange } from "react-day-picker"

export const fetchRecords = async (
  search: string,
  dateRange: DateRange | undefined,
  sortOrder: string
): Promise<BatchManagementListItem[]> => {
  // TODO: Replace with actual API call
  return new Promise((resolve) => {
    setTimeout(() => {
      // Parse dates for filtering
      const parseDateString = (dateStr: string) => {
        const [day, month, year] = dateStr.split("-")
        const monthMap: { [key: string]: number } = {
          Jan: 0,
          Feb: 1,
          Mar: 2,
          Apr: 3,
          May: 4,
          Jun: 5,
          Jul: 6,
          Aug: 7,
          Sep: 8,
          Oct: 9,
          Nov: 10,
          Dec: 11,
        }
        return new Date(
          Number.parseInt(year),
          monthMap[month],
          Number.parseInt(day)
        )
      }

      // Filter by search term
      let filteredRecords = batchManagementList.filter((record) => {
        const searchLower = search.toLowerCase()
        return (
          record.id.toLowerCase().includes(searchLower) ||
          record.code.toLowerCase().includes(searchLower) ||
          record.status.toLowerCase().includes(searchLower) ||
          record.weight.includes(search) ||
          record.batchNumber.includes(search) ||
          record.zone.toLowerCase().includes(searchLower) ||
          record.dateGenerated.includes(search) ||
          record.isBlended.toLowerCase().includes(searchLower)
        )
      })

      // Filter by date range
      if (dateRange?.from && dateRange?.to) {
        filteredRecords = filteredRecords.filter((record) => {
          const recordDate = parseDateString(record.dateGenerated)
          return (
            dateRange?.from &&
            dateRange?.to &&
            isWithinInterval(recordDate, {
              start: dateRange.from,
              end: dateRange.to,
            })
          )
        })
      }

      // Sort records
      filteredRecords.sort((a, b) => {
        const dateA = parseDateString(a.dateGenerated)
        const dateB = parseDateString(b.dateGenerated)

        switch (sortOrder) {
          case "date-asc":
            return dateA.getTime() - dateB.getTime()
          case "date-desc":
            return dateB.getTime() - dateA.getTime()
          case "id-asc":
            return a.id.localeCompare(b.id)
          case "id-desc":
            return b.id.localeCompare(a.id)
          default:
            return dateB.getTime() - dateA.getTime()
        }
      })

      resolve(filteredRecords)
    }, 500) // Simulated API delay
  })
}
