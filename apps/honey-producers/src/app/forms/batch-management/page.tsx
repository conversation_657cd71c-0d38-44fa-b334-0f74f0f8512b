"use client"
import { useState } from "react"
import React from "react"
import { ChevronDown, ChevronUp, Search, Loader } from "lucide-react"
import { format } from "date-fns"
import type { DateRange } from "react-day-picker"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { zodResolver } from "@hookform/resolvers/zod"
import { FormProvider, useForm } from "react-hook-form"
import { formSchema } from "@repo/validation/batch-management"
import { availableBatchCodes } from "@repo/dummy/batch-management"
import { DatePickerWithRange } from "@/components/date-picker-with-range"
import { useRouter } from "next/navigation"
import api from "@/lib/api"

export type processStatus = "In Progress" | "Finalized"

const getStatusColor = (status: processStatus): string => {
  switch (status) {
    case "In Progress":
      return "text-blue-600 bg-blue-500/40"
    case "Finalized":
      return "text-green-600 bg-green-500/40"
    default:
      return "text-gray-500"
  }
}
export default function BatchManagement() {
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState("")
  const [dateRange, setDateRange] = useState<DateRange>()
  const [sortOrder, setSortOrder] = useState<"newestFirst" | "oldestFirst">(
    "newestFirst"
  )
  const [selectedBatches, setSelectedBatches] = useState<string[]>([])
  const [expandedBatch, setExpandedBatch] = useState<string | null>(null)
  const [selectedBatchCode, setSelectedBatchCode] = useState(
    availableBatchCodes[0]
  )
  const [isTrackLoading, setIsTrackLoading] = useState<Record<string, boolean>>(
    {}
  )
  const [isFinalizeLoading, setIsFinalizeLoading] = useState(false)
  const [isCombineLoading, setIsCombineLoading] = useState(false)
  const [isApplyLoading, setIsApplyLoading] = useState(false)

  const { data: records, isLoading } = api.useQuery("get", "/batches", {
    params: {
      query: {
        query: searchTerm,
        createdAtFrom: dateRange?.from?.toISOString(),
        createdAtTo: dateRange?.to?.toISOString(),
        sortBy: sortOrder,
      },
    },
  })

  // Initialize the form with new default values
  const methods = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      testingDate: new Date(),
      testerName: "",
      moistureContent: undefined,
      smellInspection: undefined,
      smellComments: "",
      bubbles: undefined,
      foreignParticles: undefined,
      anythingElse: undefined,
      additionalComments: "",
      drums: [],
    },
  })

  // Handle selecting/deselecting a batch
  const handleSelectBatch = (id: string) => {
    setSelectedBatches((prev) =>
      prev.includes(id)
        ? prev.filter((batchId) => batchId !== id)
        : [...prev, id]
    )
  }

  // Handle selecting/deselecting all batches
  const handleSelectAll = () => {
    if (records?.results) {
      if (selectedBatches.length === records.results.length) {
        setSelectedBatches([])
      } else {
        setSelectedBatches(records.results.map((batch) => batch.batchCode))
      }
    }
  }

  // Handle expanding/collapsing a batch
  const toggleExpand = (id: string) => {
    setExpandedBatch(expandedBatch === id ? null : id)
  }

  // Calculate the total weight of selected batches
  const totalWeight = records?.results.length
    ? records.results
        .filter((batch) => selectedBatches.includes(batch.batchCode))
        .reduce((sum, batch) => sum + batch.totalWeight, 0)
    : 0

  const openProcessPage = () => {
    if (!selectedBatches.length) {
      console.log({
        title: "Batch selection required",
        description: "Please select a batch before proceeding",
        variant: "destructive",
      })
      return
    }

    setIsCombineLoading(isCombineLoading)
    setIsFinalizeLoading(true)

    router.push(
      `/forms/batch-management/process?selectedBatchCodes=${encodeURIComponent(
        JSON.stringify(selectedBatches)
      )}&maxWeight=${totalWeight.toFixed(2)}`
    )
  }

  // Function to handle batch code change for selected batches
  const handleSelectedBatchCodeChange = (newCode: string) => {
    setSelectedBatchCode(newCode)
  }

  // Function to apply the selected batch code to all selected batches
  const applySelectedBatchCode = () => {
    if (selectedBatches.length === 0) {
      console.log({
        title: "No batches selected",
        description: "Please select at least one batch to update the code",
        variant: "destructive",
      })
      return
    }
    setIsApplyLoading(true)

    console.log(
      `Batch codes update requested: ${selectedBatches.length} batch(es) to be updated to code ${selectedBatchCode}`
    )
    console.log({
      title: "Batch codes update logged",
      description: `${selectedBatches.length} batch(es) update to code ${selectedBatchCode} will be processed by backend`,
    })

    setIsApplyLoading(false)
  }

  const handleTrack = (code: string) => {
    setIsTrackLoading((prev) => ({ ...prev, [code]: true }))

    router.push(`/forms/batch-management/batch-code#${code}`)
    setIsTrackLoading((prev) => ({ ...prev, [code]: false }))
  }

  return (
    <FormProvider {...methods}>
      <div className="container max-w-screen-xl mx-auto py-8 space-y-8">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-2xl font-semibold">Batch Code Management</h1>
          <p className="text-sm text-muted-foreground">Create batches</p>
        </div>

        {/* Search bar and filters */}
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search by batch code."
              className="pl-9"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="flex flex-col sm:flex-row gap-4 lg:w-auto">
            <DatePickerWithRange date={dateRange} onSelect={setDateRange} />

            <Select
              defaultValue={sortOrder}
              onValueChange={(value: "newestFirst" | "oldestFirst") =>
                setSortOrder(value)
              }
            >
              <SelectTrigger className="w-full sm:w-[200px]">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="newestFirst">Date (Newest first)</SelectItem>
                <SelectItem value="oldestFirst">Date (Oldest first)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Table */}
        <div className="border shadow rounded-lg">
          <Table>
            <TableHeader className="bg-primary-foreground sticky top-0 z-10">
              <TableRow>
                <TableHead className="w-[40px] text-center">
                  <Checkbox
                    checked={
                      !!records?.results.length &&
                      selectedBatches.length === records.results.length
                    }
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
                <TableHead className="w-[100px] font-medium text-muted-foreground text-center">
                  Batch Code
                </TableHead>
                <TableHead className="w-[80px] font-medium text-muted-foreground text-center">
                  Status
                </TableHead>
                <TableHead className="w-[100px] font-medium text-muted-foreground text-center">
                  Weight
                </TableHead>
                <TableHead className="w-[120px] font-medium text-muted-foreground text-center">
                  Date Generated
                </TableHead>
                <TableHead className="w-[100px] font-medium text-muted-foreground text-center">
                  Blended
                </TableHead>
                <TableHead className="w-[40px] font-medium text-muted-foreground text-center">
                  Details
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={9} className="h-24 text-center">
                    <div className="flex justify-center items-center">
                      <Loader className="h-6 w-6 animate-spin text-primary" />
                      <span className="ml-2">Loading records...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : records?.results.length ? (
                records.results.map((record) => (
                  <React.Fragment key={record.batchCode}>
                    <TableRow className="cursor-pointer hover:bg-muted/50 text-center">
                      <TableCell>
                        <Checkbox
                          checked={selectedBatches.includes(record.batchCode)}
                          onCheckedChange={() =>
                            handleSelectBatch(record.batchCode)
                          }
                        />
                      </TableCell>
                      {/* <TableCell className="font-mono">
                        {record.batchNumber}
                      </TableCell> */}
                      <TableCell>{record.batchCode}</TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={`px-2 py-1 ${getStatusColor(record.isProcessed ? "Finalized" : "In Progress")}`}
                        >
                          {record.isProcessed ? "Finalized" : "In Progress"}
                        </Badge>
                      </TableCell>
                      <TableCell>{record.totalWeight} Kg</TableCell>
                      <TableCell>
                        {format(new Date(record.dateGenerated), "d MMM yyyy")}
                      </TableCell>
                      <TableCell>{record.isBlended ? "Yes" : "No"}</TableCell>
                      <TableCell className="flex items-center gap-2 justify-center">
                        <Button
                          variant="default"
                          size="sm"
                          className="font-semibold"
                          onClick={() => handleTrack(record.batchCode)}
                          disabled={isTrackLoading[record.batchCode]}
                        >
                          {isTrackLoading[record.batchCode] ? (
                            <>
                              <Loader className="h-4 w-4 animate-spin" />
                            </>
                          ) : (
                            "Track"
                          )}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => toggleExpand(record.batchCode)}
                        >
                          {expandedBatch === record.batchCode ? (
                            <ChevronUp />
                          ) : (
                            <ChevronDown />
                          )}
                        </Button>
                      </TableCell>
                    </TableRow>
                    {expandedBatch === record.batchCode &&
                      record.dailySummaries && (
                        <TableRow>
                          <TableCell colSpan={9} className="p-0">
                            <div className="p-4 bg-muted/30">
                              <Table>
                                <TableHeader className="bg-muted/50">
                                  <TableRow>
                                    <TableHead className="text-center">
                                      Day
                                    </TableHead>
                                    <TableHead className="text-center">
                                      Date
                                    </TableHead>
                                    <TableHead className="text-center">
                                      Week Number
                                    </TableHead>
                                    <TableHead className="text-center">
                                      Number of buckets
                                    </TableHead>
                                    <TableHead className="text-center">
                                      Bucket Weight
                                    </TableHead>
                                    <TableHead className="text-center">
                                      Wax produced
                                    </TableHead>
                                    <TableHead className="text-center">
                                      Estimated Weight
                                    </TableHead>
                                  </TableRow>
                                </TableHeader>
                                <TableBody>
                                  {record.dailySummaries.map(
                                    (dailySummary, index) => (
                                      <TableRow key={index}>
                                        <TableCell className="text-center">
                                          {format(
                                            new Date(dailySummary.date),
                                            "EE"
                                          )}
                                        </TableCell>
                                        <TableCell className="text-center">
                                          {format(dailySummary.date, "d-MMM")}
                                        </TableCell>
                                        <TableCell className="text-center">
                                          {format(
                                            new Date(dailySummary.date),
                                            "w"
                                          )}
                                        </TableCell>
                                        <TableCell className="text-center">
                                          {dailySummary.numberOfBuckets}
                                        </TableCell>
                                        <TableCell className="text-center">
                                          {dailySummary.totalWeight}
                                        </TableCell>
                                        <TableCell className="text-center">
                                          {dailySummary.waxProduced}
                                        </TableCell>
                                        <TableCell className="text-center">
                                          {dailySummary.estimatedHoneyWeight}
                                        </TableCell>
                                      </TableRow>
                                    )
                                  )}
                                </TableBody>
                              </Table>
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                  </React.Fragment>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={9} className="h-24 text-center">
                    No records found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        {/* Summary bar and action */}
        {selectedBatches.length > 0 && (
          <div className="bg-background border rounded-lg shadow p-5 mt-6">
            <div className="container mx-auto max-w-7xl flex flex-col sm:flex-row items-start sm:items-center justify-between gap-6 max-sm:gap-4 max-sm:items-center">
              <div className="flex flex-col gap-3">
                <div className="font-bold text-lg max-sm:text-center">
                  {selectedBatches.length}{" "}
                  {selectedBatches.length === 1 ? "Item" : "Items"} Selected
                </div>
                <div className="text-sm text-muted-foreground">
                  {selectedBatches.length > 0 && (
                    <>
                      <span className="font-medium">Batch Numbers: </span>
                      {selectedBatches
                        .slice(0, 3)
                        .map((id) => {
                          const batch = records?.results.find(
                            (b) => b.batchCode === id
                          )
                          return batch?.batchCode
                        })
                        .join(", ")}
                      {selectedBatches.length > 3 && "..."}
                    </>
                  )}
                </div>
              </div>

              <div className="flex max-sm:flex-col items-center gap-6 max-sm:gap-4 self-end max-sm:self-center">
                <div className="max-sm:flex max-sm:flex-row-reverse max-sm:gap-2 max-sm:items-center text-right">
                  <div className="font-medium text-lg">
                    {totalWeight.toFixed(2)} Kgs
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Total weight
                  </div>
                </div>

                {selectedBatches.length >= 2 && (
                  <div className="flex flex-col items-center">
                    <div className="flex items-center gap-2">
                      <Select
                        value={selectedBatchCode}
                        onValueChange={handleSelectedBatchCodeChange}
                      >
                        <SelectTrigger className="w-[140px] mx-auto">
                          <SelectValue>{selectedBatchCode}</SelectValue>
                        </SelectTrigger>
                        <SelectContent>
                          {availableBatchCodes.map((code) => (
                            <SelectItem key={code} value={code}>
                              {code}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={applySelectedBatchCode}
                        disabled={isApplyLoading}
                      >
                        {isApplyLoading ? (
                          <>
                            <Loader className="mr-2 h-4 w-4 animate-spin" />
                            Applying...
                          </>
                        ) : (
                          "Apply"
                        )}
                      </Button>
                    </div>
                    <div className="text-xs text-muted-foreground mt-1 text-center">
                      Assign batch code to selection
                    </div>
                  </div>
                )}

                <div className="flex gap-3">
                  <Button
                    onClick={openProcessPage}
                    className="px-6"
                    disabled={isFinalizeLoading}
                  >
                    {isFinalizeLoading || isCombineLoading ? (
                      <>
                        <Loader className="mr-2 h-4 w-4 animate-spin" />
                        Loading...
                      </>
                    ) : selectedBatches.length <= 1 ? (
                      "Finalize"
                    ) : (
                      "Combine"
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </FormProvider>
  )
}
