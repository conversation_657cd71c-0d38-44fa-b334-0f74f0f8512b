"use client"
import React from "react"
import { <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader } from "@/components/ui/card"
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { DetailsValue } from "@/components/details-value"
import api from "@/lib/api"
import { <PERSON><PERSON>, Pencil, ArrowLeft } from "lucide-react"
import { useRouter } from "next/navigation"
import { format } from "date-fns"
import { capitalize } from "@/lib/utils"
import Link from "next/link"
import { Button } from "@/components/ui/button"

interface Props {
  id: string
}

export const HoneyProcessingDetailsContainer: React.FC<Props> = ({ id }) => {
  const router = useRouter()
  const { data: honeyProcessingData, isLoading } = api.useQuery(
    "get",
    "/honey-processing/{id}",
    { params: { path: { id } } }
  )

  if (isLoading) {
    return (
      <div className="flex justify-center items-center">
        <Loader className="h-6 w-6 animate-spin text-primary" />
        <span className="ml-2">Loading honey harvesting data...</span>
      </div>
    )
  }

  if (!honeyProcessingData) {
    return (
      <div className="flex items-center justify-center">
        <CardContent className="pt-6 flex flex-col items-center text-center space-y-4">
          <p className="text-red-500 font-medium">
            An unknown error occurred while fetching the honey processing data.
          </p>
          <Button
            className="w-full"
            onClick={() => router.push(`/forms/honey-processing`)}
          >
            Back to Honey Processing List
          </Button>
        </CardContent>
      </div>
    )
  }

  const bucketEntries = Object.entries(honeyProcessingData.buckets)

  return (
    <div className="space-y-4 space-x-6 ">
      <CardHeader className="space-y-2">
        {/* Header */}
        <Button
          variant="outline"
          size="sm"
          className="font-semibold w-fit"
          onClick={() => router.push("/forms/honey-processing")}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        {!honeyProcessingData.processEndDate && (
          <div className="absolute right-12 top-6 font-semibold">
            <Link
              className="h-4 w-4 rounded-md hover:bg-accent hover:text-accent-foreground"
              href={`/forms/honey-processing/form/edit?formId=${id}`}
            >
              <Pencil className="h-4 w-4" />
            </Link>
          </div>
        )}

        <div className="space-y-6">
          <h1 className="text-2xl font-semibold text-center">
            Honey Processing
          </h1>

          <div className="flex max-sm:flex-col max-sm:gap-1 justify-between text-sm text-muted-foreground px-6">
            <p>
              {honeyProcessingData.processStartDate
                ? format(
                    new Date(honeyProcessingData.processStartDate),
                    "yyyy-MM-dd HH:mm:ss"
                  )
                : ""}
            </p>
            <p>ID: {id}</p>
          </div>
        </div>
      </CardHeader>
      <CardContent className="max-sm:p-2 space-y-6">
        <Accordion
          type="multiple"
          defaultValue={["honeyCombInputs", "processing"]}
          className="w-full"
        >
          {/* Honey Comb Inputs Section */}
          <AccordionItem value="honeyCombInputs">
            <AccordionTrigger className="group py-2">
              <div className="flex items-center gap-2">
                <span className="h-2 w-2 rounded-full bg-pink-500" />
                <span className="font-semibold">Honey Comb Inputs</span>
              </div>
            </AccordionTrigger>
            <AccordionContent className="space-y-4 pt-4">
              <div className="bg-background border shadow rounded-lg p-6 grid grid-cols-2 gap-4">
                <DetailsValue
                  label="Honey Type"
                  value={capitalize(honeyProcessingData.buckets[0]?.honeyType)}
                />
                <DetailsValue
                  label="Region"
                  value={honeyProcessingData.batchCode.split("-")[0]}
                />
                <DetailsValue
                  label="Zone"
                  value={honeyProcessingData.batchCode.split("-")[1]}
                />
                <DetailsValue
                  label="Number of buckets used"
                  value={honeyProcessingData.buckets.length}
                />
                <DetailsValue
                  label="Total HoneyComb Weight"
                  value={`${honeyProcessingData.waxKg + honeyProcessingData.honeyKg} kg`}
                />
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* Processing Section */}
          <AccordionItem value="processing">
            <AccordionTrigger className="group">
              <div className="flex items-center gap-2">
                <span className="h-2 w-2 rounded-full bg-teal-500" />
                <span className="font-semibold">Processing</span>
              </div>
            </AccordionTrigger>
            <AccordionContent className="space-y-4 pt-2">
              <div className="bg-background border shadow rounded-lg p-6 grid grid-cols-2 gap-4">
                <DetailsValue
                  label="Factory location"
                  value={honeyProcessingData.factory?.name}
                />
                <DetailsValue
                  label="Process type"
                  value={honeyProcessingData.processType?.name}
                />
                <DetailsValue
                  label="Inspecting Officer"
                  value={honeyProcessingData.factoryStaffLead?.name}
                />
                <DetailsValue
                  label="Batch Code"
                  value={honeyProcessingData.batchCode}
                />
                <DetailsValue
                  label="Process Date"
                  value={
                    honeyProcessingData.processEndDate
                      ? format(honeyProcessingData.processEndDate, "d-MMM-yyyy")
                      : ""
                  }
                />
                <DetailsValue
                  label="Speed of Centrifuge"
                  value={honeyProcessingData.centrifugeSpeed}
                />
                <DetailsValue
                  label="Temperature of Centrifuge"
                  value={honeyProcessingData.centrifugeTemperature}
                />
                <DetailsValue
                  label="Temperature of Honey"
                  value={honeyProcessingData.honeyTemperature}
                />
                <DetailsValue
                  label="Estimated Weight of Honey"
                  value={`${honeyProcessingData.honeyKg} kg`}
                />
                <DetailsValue
                  label="Pump Hopper"
                  value={honeyProcessingData.pumpHopper}
                />
                <DetailsValue
                  label="Weight of Wax"
                  value={`${honeyProcessingData.waxKg} kg`}
                />
                <DetailsValue
                  label="Size of the mesh"
                  value={`${honeyProcessingData.meshSize} microns`}
                />
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* List buckets used Section */}
          <AccordionItem value="buckets">
            <AccordionTrigger className="group">
              <div className="flex items-center gap-2">
                <span className="h-2 w-2 rounded-full bg-teal-500" />
                <span className="font-semibold">Buckets</span>
              </div>
            </AccordionTrigger>
            <AccordionContent className="space-y-4 pt-2">
              <div className="space-y-4 pt-2">
                {bucketEntries.map(([bucketId, bucket]) => (
                  <div
                    key={bucketId}
                    className="relative bg-background border shadow rounded-lg p-4 w-full"
                  >
                    <div className="grid max-sm:grid-cols-2 grid-cols-[auto_auto_auto_auto] gap-2 text-center items-center">
                      <DetailsValue label="ID" value={`#${bucket.id}`} />
                      <DetailsValue
                        label="Original Weight"
                        value={`${bucket.weight} Kgs`}
                      />
                      <DetailsValue
                        label="Verified Weight"
                        value={`${bucket.verifiedWeight} Kgs`}
                      />
                      <DetailsValue
                        className="capitalize"
                        label="Contamination Status"
                        value={`${bucket.contaminationStatus}`}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </CardContent>
    </div>
  )
}
