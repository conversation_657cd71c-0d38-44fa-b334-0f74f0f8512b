"use client"

import { useEffect, useState } from "react"
import { HoneyProcessingDetailsContainer } from "./honey-processing-details-container"

export default function HoneyProcessingPage() {
  const [id, setId] = useState<string | null>(null)

  useEffect(() => {
    const updateId = () => {
      const hash = window.location.hash.replace("#", "")
      setId(hash || null)
    }

    updateId()
    window.addEventListener("hashchange", updateId)
    return () => window.removeEventListener("hashchange", updateId)
  }, [])

  if (!id) {
    return <div>Loading...</div>
  }

  return (
    <div className="container bg-primary-foreground border shadow rounded-lg mx-auto max-w-screen-xl pt-6 pb-2 space-y-8">
      <HoneyProcessingDetailsContainer id={id} />
    </div>
  )
}
