"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { use<PERSON><PERSON>, SubmitHandler, FormProvider } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Step, Stepper } from "@/components/form/stepper"
import {
  FormFieldNames,
  formSchema,
  FormValues,
} from "@repo/validation/honey-processing"
import {
  Start,
  Sourcing,
  Weight,
  Crusher,
  Centrifuge,
  Filtration,
  ConfirmStep,
  CompleteStep,
} from "../../steps"
import { CompleteStepFormData } from "../../steps/complete"
import { authClient } from "@/lib/auth-client"
import { locations, ProcessTypes } from "@repo/dummy/honey-processing"
import api from "@/lib/api"
import { Loader } from "lucide-react"
import { useSearchParams } from "next/navigation"

const HoneyProcessingPage = () => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const formId = searchParams.get("formId")
  const [step, setStep] = useState(3)
  const [formData, setFormData] = useState<CompleteStepFormData>()
  const { data } = authClient.useSession()
  const user = data?.user

  useEffect(() => {
    if (!formId) {
      router.push("/404")
    }
  }, [formId, router])

  if (!formId) {
    return null
  }

  const defaultValues = {
    region: "",
    zone: "",
    batchCode: "",
    processType: ProcessTypes[0].id,
    factoryLocation: locations[0].id,
    buckets: { verifiedWeight: undefined, weight: undefined },
  }

  const methods = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      ...defaultValues,
    },
  })

  const { data: honeyProcessingData, isLoading } = api.useQuery(
    "get",
    "/honey-processing/{id}",
    { params: { path: { id: formId } } }
  )

  useEffect(() => {
    if (!honeyProcessingData) return

    methods.reset({
      ...defaultValues,
      factoryStaffLead: honeyProcessingData.factoryStaffLead,
      factoryLocation: honeyProcessingData.factory?.id,
      processType: honeyProcessingData.processType?.id,
      regionId: honeyProcessingData.batchCode.split("-")[0],
      chiefdomId: honeyProcessingData.chiefdomId,
      zoneId: honeyProcessingData.batchCode.split("-")[1],
      villageId: honeyProcessingData.villageId,
      wax: honeyProcessingData.waxKg,
      centrifugeTemperature: honeyProcessingData.centrifugeTemperature,
      honeyTemperature: honeyProcessingData.honeyTemperature,
      centrifugeSpeed: honeyProcessingData.centrifugeSpeed,
      pumpHopper: honeyProcessingData.pumpHopper,
      meshSize: honeyProcessingData.meshSize,
      originalTotalWeight: honeyProcessingData.buckets.reduce(
        (sum, bucket) => sum + (bucket.weight || 0),
        0
      ),
      buckets: honeyProcessingData.buckets.reduce(
        (acc, bucket) => {
          acc[bucket.id as string] = {
            weight: bucket.weight,
            verifiedWeight: bucket.verifiedWeight,
            contaminationStatus: bucket.contaminationStatus,
          }
          return acc
        },
        {} as Record<
          string,
          {
            weight: number
            verifiedWeight: number
            contaminationStatus: undefined | "clean" | "contaminated"
          }
        >
      ),
      formId: honeyProcessingData.id,
    })
  }, [honeyProcessingData, methods])

  const stepComponents = [
    Start,
    Sourcing,
    Weight,
    Crusher,
    Centrifuge,
    Filtration,
    ConfirmStep,
    CompleteStep,
  ]

  const stepSections = [
    { id: 1, label: "Start", range: [1, 1] },
    { id: 2, label: "Sourcing", range: [2, 2] },
    { id: 3, label: "Weight", range: [3, 3] },
    { id: 4, label: "Crusher", range: [4, 4] },
    { id: 5, label: "Centrifuge", range: [5, 5] },
    { id: 6, label: "Filtration", range: [6, 6] },
    { id: 7, label: "Review", range: [7, 7] },
    { id: 8, label: "Finish", range: [8, 8] },
  ]

  const stepperState: Step[] = stepSections.map(({ id, label, range }) => {
    const [start, end] = range
    return {
      id,
      label,
      status:
        step > end
          ? "completed"
          : step >= start && step <= end
            ? "current"
            : "upcoming",
    }
  })
  const getCurrentStepFieldNames = (step: number) => {
    // Define which fields belong to which step
    const stepFields: Record<number, FormFieldNames[]> = {
      3: ["buckets", "verifiedTotalWeight"],
      4: ["pumpHopper"],
      5: ["wax", "centrifugeSpeed"],
      6: ["meshSize"],
      7: ["centrifugeTemperature", "honeyTemperature"],
      8: ["estimatedHoneyWeight"],
    }

    return stepFields[step] || []
  }

  const moveToNextStep = async () => {
    methods.clearErrors()
    // Trigger validation before moving to the next step
    const isValid = await methods.trigger(getCurrentStepFieldNames(step))
    if (!isValid) return

    setStep(step + 1)
  }

  const mutation = api.useMutation("post", "/honey-processing/{id}", {
    onSuccess: (res) => {
      setFormData({
        title: "Honey Processing",
        createdAt: new Date().toISOString(),
        formId: methods.getValues("formId") || "",
        submitterName: user?.name || "",
        batchCode: res.batchCode,
        estimatedHoneyWeight: methods.getValues("estimatedHoneyWeight"),
        wax: methods.getValues("wax"),
        numberBuckets: Object.keys(methods.getValues("buckets")).length,
      })
      methods.reset()
      setStep(step + 1)
    },
    onError: (e) => {
      console.log(e.message)
    },
  })

  const onSubmit: SubmitHandler<FormValues> = (data) => {
    if (!data.formId) throw new Error("Form ID is required")

    mutation.mutate({
      params: { path: { id: data.formId } },
      body: {
        processEndDate: new Date().toISOString(),
        waxKg: data.wax,
        centrifugeTemperature: data.centrifugeTemperature,
        honeyTemperature: data.honeyTemperature,
        centrifugeSpeed: data.centrifugeSpeed,
        honeyKg: data.estimatedHoneyWeight,
        buckets: data.buckets,
        pumpHopper: data.pumpHopper,
        meshSize: data.meshSize,
      },
    })
  }

  const moveToPreviousStep = () =>
    step === 1 ? router.push("/forms/honey-processing") : setStep(step - 1)

  const renderStep = () => {
    const CurrentStepComponent = stepComponents[step - 1]

    return (
      <CurrentStepComponent
        handleNext={moveToNextStep}
        isLast={step === stepComponents.length - 1}
        formPath={formData && `/forms/honey-processing/form#${formData.formId}`}
        formData={formData}
      />
    )
  }

  // In case the form is already completed, redirect to the form details page
  if (honeyProcessingData?.processEndDate) {
    router.push(`/forms/honey-processing/form#${formId}`)
  }

  return (
    <FormProvider {...methods}>
      <div className="flex items-center flex-col md:mx-4">
        <Stepper
          steps={stepperState}
          progress={(step / stepComponents.length) * 100}
          onBack={moveToPreviousStep}
          backDisabled={!!formData || step === 3}
        />
        <div className="w-full max-w-[800px]">
          {isLoading ? (
            <div className="flex justify-center items-center">
              <Loader className="h-6 w-6 animate-spin text-primary" />
              <span className="ml-2">Loading honey processing data...</span>
            </div>
          ) : (
            <form onSubmit={methods.handleSubmit(onSubmit)}>
              {renderStep()}
            </form>
          )}
        </div>
      </div>
    </FormProvider>
  )
}

export default HoneyProcessingPage
