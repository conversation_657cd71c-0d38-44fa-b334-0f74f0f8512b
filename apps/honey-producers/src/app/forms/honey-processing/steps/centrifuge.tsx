"use client"

import { type FC, useState } from "react"
import { NextButton } from "@/components/form/next-button"
import { useFormContext } from "react-hook-form"
import type { FormValues } from "@repo/validation/honey-processing"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Loader } from "lucide-react"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import api from "@/lib/api"
import { useRouter } from "next/navigation"

interface Props {
  handleNext: () => void
  isLast: boolean
  formPath?: string
}

export const Centrifuge: FC<Props> = ({ handleNext, isLast, formPath }) => {
  const { control, getValues, reset } = useFormContext<FormValues>()
  const router = useRouter()
  const [saveLoading, setSaveLoading] = useState(false)
  const [nextLoading, setNextLoading] = useState(false)

  const mutationSave = api.useMutation("post", "/honey-processing/{id}", {
    onSuccess: () => {
      reset()
      router.push("/forms/honey-processing")
      setSaveLoading(false)
    },
    onError: (e) => {
      console.log(e.message)
      setSaveLoading(false)
    },
  })

  const mutationNext = api.useMutation("post", "/honey-processing/{id}", {
    onSuccess: () => {
      setNextLoading(false)

      handleNext()
    },
    onError: (e) => {
      console.log(e.message)

      setNextLoading(false)
    },
  })

  // Function to save data
  const handleSave = () => {
    const id = getValues("formId")
    if (!id) throw new Error("Form ID is required")

    setSaveLoading(true)

    mutationSave.mutate({
      params: { path: { id } },
      body: {
        waxKg: getValues("wax"),
        meshSize: getValues("meshSize"),
        buckets: getValues("buckets"),
        honeyKg: getValues("estimatedHoneyWeight"),
        centrifugeTemperature: getValues("centrifugeTemperature"),
        honeyTemperature: getValues("honeyTemperature"),
        centrifugeSpeed: getValues("centrifugeSpeed"),
        pumpHopper: getValues("pumpHopper"),
      },
    })
  }

  // Handle save on next press
  const handleSaveNext = () => {
    const id = getValues("formId")
    if (!id) throw new Error("Form ID is required")

    setNextLoading(true)

    mutationNext.mutate({
      params: { path: { id } },
      body: {
        waxKg: getValues("wax"),
        meshSize: getValues("meshSize"),
        buckets: getValues("buckets"),
        honeyKg: getValues("estimatedHoneyWeight"),
        centrifugeTemperature: getValues("centrifugeTemperature"),
        honeyTemperature: getValues("honeyTemperature"),
        centrifugeSpeed: getValues("centrifugeSpeed"),
        pumpHopper: getValues("pumpHopper"),
      },
    })
  }

  const isAnyLoading = saveLoading || nextLoading

  return (
    <div className="space-y-6">
      <Card className="bg-primary-foreground border">
        <CardHeader>
          <CardTitle className="font-semibold">Post Centrifuge</CardTitle>
          <CardDescription>
            Record the wax off-take post centrifuge process.
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          <div className="space-y-4 text-sm">
            <FormField
              control={control}
              name="wax"
              render={({ field }) => {
                const verifiedTotalWeight = getValues("verifiedTotalWeight")
                const waxValue = field.value || 0
                const isInvalid = waxValue > verifiedTotalWeight

                return (
                  <FormItem>
                    <FormLabel>Weight of Wax</FormLabel>
                    <FormControl>
                      <Input
                        step="0.01"
                        type="number"
                        inputMode="decimal"
                        placeholder="ex: 100"
                        className="bg-background"
                        {...field}
                        value={field.value === 0 ? "" : (field.value ?? "")}
                        onChange={(e) => {
                          const val = e.target.value
                          if (val === "" || /^[0-9]*\.?[0-9]*$/.test(val)) {
                            field.onChange(val === "" ? undefined : Number(val))
                          }
                        }}
                      />
                    </FormControl>
                    <>
                      <FormMessage />
                      {isInvalid && (
                        <p className="text-sm text-red-500">
                          Warning: Wax weight is exceeding total weight (
                          {verifiedTotalWeight}
                          kg).
                        </p>
                      )}
                    </>
                  </FormItem>
                )
              }}
            />
            <FormField
              control={control}
              name="centrifugeSpeed"
              render={({ field }) => {
                return (
                  <FormItem>
                    <FormLabel>Speed of the Centrifuge</FormLabel>
                    <FormControl>
                      <Input
                        step="0.01"
                        type="number"
                        inputMode="decimal"
                        placeholder="ex: 99"
                        className="bg-background"
                        {...field}
                        value={field.value === 0 ? "" : (field.value ?? "")}
                        onChange={(e) => {
                          const val = e.target.value
                          if (val === "" || /^[0-9]*\.?[0-9]*$/.test(val)) {
                            field.onChange(val === "" ? undefined : Number(val))
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )
              }}
            />
            <FormField
              control={control}
              name="centrifugeTemperature"
              render={({ field }) => {
                return (
                  <FormItem>
                    <FormLabel>Temperature of the Centrifuge</FormLabel>
                    <FormControl>
                      <Input
                        step="0.01"
                        type="number"
                        inputMode="decimal"
                        placeholder="ex: 100"
                        className="bg-background"
                        {...field}
                        value={field.value === 0 ? "" : (field.value ?? "")}
                        onChange={(e) => {
                          const val = e.target.value
                          if (val === "" || /^[0-9]*\.?[0-9]*$/.test(val)) {
                            field.onChange(val === "" ? undefined : Number(val))
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )
              }}
            />
            <FormField
              control={control}
              name="honeyTemperature"
              render={({ field }) => {
                return (
                  <FormItem>
                    <FormLabel>Temperature of the Honey</FormLabel>
                    <FormControl>
                      <Input
                        step="0.01"
                        type="number"
                        inputMode="decimal"
                        placeholder="ex: 100"
                        className="bg-background"
                        {...field}
                        value={field.value === 0 ? "" : (field.value ?? "")}
                        onChange={(e) => {
                          const val = e.target.value
                          if (val === "" || /^[0-9]*\.?[0-9]*$/.test(val)) {
                            field.onChange(val === "" ? undefined : Number(val))
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )
              }}
            />
          </div>
        </CardContent>
      </Card>
      <div className="flex flex-cols-2 gap-4">
        <Button
          variant="outline"
          onClick={handleSave}
          className="mt-4 w-full"
          type="button"
          disabled={isAnyLoading}
        >
          {saveLoading ? (
            <>
              <Loader className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            "Save & Exit"
          )}
        </Button>
        <NextButton
          isLast={isLast}
          handleNext={handleSaveNext}
          formPath={formPath}
          isLoading={nextLoading}
        />
      </div>
    </div>
  )
}
