"use client"

import { type FC, useMemo, useState, useEffect } from "react"
import { useFormContext, useWatch } from "react-hook-form"
import { NextButton } from "@/components/form/next-button"
import type { FormValues } from "@repo/validation/honey-processing"
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/components/ui/card"
import { Loader } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import api from "@/lib/api"
import { useRouter } from "next/navigation"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@/components/ui/select"
import { SelectValue } from "@radix-ui/react-select"

interface Props {
  handleNext: () => void
  isLast: boolean
  formPath?: string
}

export const Weight: FC<Props> = ({ handleNext, isLast, formPath }) => {
  const { getValues, control, watch, reset, setValue } =
    useFormContext<FormValues>()
  const router = useRouter()

  const bucketFormValues =
    useWatch({
      control,
      name: "buckets",
    }) || []

  const bucketEntries = Object.entries(bucketFormValues)

  const bucket = getValues("buckets")
  const originalTotalWeight = watch("originalTotalWeight") || 0

  const verifiedTotalWeight = useMemo(() => {
    return Object.values(bucketFormValues).reduce((sum, bucket) => {
      return sum + (bucket?.verifiedWeight || 0)
    }, 0)
  }, [bucketFormValues])

  useEffect(() => {
    const roundedTotalWeight = Math.round(verifiedTotalWeight * 100) / 100
    setValue("verifiedTotalWeight", roundedTotalWeight)
    bucketEntries.forEach(([bucketId]) => {
      const current = getValues(`buckets.${bucketId}.contaminationStatus`)
      if (current === undefined) {
        setValue(`buckets.${bucketId}.contaminationStatus`, "clean")
      }
    })
  }, [verifiedTotalWeight, setValue, bucketEntries])

  const weightDifference = useMemo(() => {
    return verifiedTotalWeight - originalTotalWeight
  }, [verifiedTotalWeight, originalTotalWeight])

  const percentageDifference = useMemo(() => {
    if (originalTotalWeight === 0) return 0
    return (weightDifference / originalTotalWeight) * 100
  }, [weightDifference, originalTotalWeight])

  const [saveLoading, setSaveLoading] = useState(false)
  const [nextLoading, setNextLoading] = useState(false)

  const mutationSave = api.useMutation("post", "/honey-processing/{id}", {
    onSuccess: () => {
      reset()
      router.push("/forms/honey-processing")
      setSaveLoading(false)
    },
    onError: (e) => {
      console.log(e.message)
      setSaveLoading(false)
    },
  })

  const mutationNext = api.useMutation("post", "/honey-processing/{id}", {
    onSuccess: () => {
      setNextLoading(false)

      handleNext()
    },
    onError: (e) => {
      console.log(e.message)

      setNextLoading(false)
    },
  })

  // Function to save data
  const handleSave = () => {
    const id = getValues("formId")
    if (!id) throw new Error("Form ID is required")

    setSaveLoading(true)

    mutationSave.mutate({
      params: { path: { id } },
      body: {
        waxKg: getValues("wax"),
        meshSize: getValues("meshSize"),
        buckets: getValues("buckets"),
        honeyKg: getValues("estimatedHoneyWeight"),
        centrifugeTemperature: getValues("centrifugeTemperature"),
        honeyTemperature: getValues("honeyTemperature"),
        centrifugeSpeed: getValues("centrifugeSpeed"),
        pumpHopper: getValues("pumpHopper"),
      },
    })
  }

  // Handle save on next press
  const handleSaveNext = () => {
    const id = getValues("formId")
    if (!id) throw new Error("Form ID is required")

    setNextLoading(true)

    mutationNext.mutate({
      params: { path: { id } },
      body: {
        waxKg: getValues("wax"),
        meshSize: getValues("meshSize"),
        buckets: getValues("buckets"),
        honeyKg: getValues("estimatedHoneyWeight"),
        centrifugeTemperature: getValues("centrifugeTemperature"),
        honeyTemperature: getValues("honeyTemperature"),
        centrifugeSpeed: getValues("centrifugeSpeed"),
        pumpHopper: getValues("pumpHopper"),
      },
    })
  }

  const isAnyLoading = saveLoading || nextLoading

  return (
    <div className="space-y-6">
      <Card className="bg-primary-foreground border">
        <CardHeader>
          <CardTitle className="font-semibold">
            Bucket Weight Verification
          </CardTitle>
          <CardDescription>
            Reweigh each bucket to ensure accuracy before placing it into the
            crusher.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <ul>
            {bucketEntries.length > 0 ? (
              bucketEntries.map(([bucketId]) => (
                <Card
                  key={bucketId}
                  className="border bg-background rounded-lg p-4 mb-4"
                >
                  <CardContent className="p-0">
                    <div className="flex max-sm:flex-col max-sm:items-start justify-around items-center">
                      <div className="sm:w-1/5">
                        <div className=" text-muted-foreground text-xs">
                          Bucket ID
                        </div>
                        <div className="font-medium">#{bucketId}</div>
                      </div>

                      <div className="flex items-end">
                        <div>
                          <div className="text-muted-foreground text-xs">
                            Original Weight
                          </div>
                          <div className="font-medium">
                            {bucket[bucketId]?.weight} Kgs
                          </div>
                        </div>
                      </div>

                      <div className="flex items-end gap-2">
                        <FormField
                          control={control}
                          name={`buckets.${bucketId}.verifiedWeight`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-muted-foreground text-xs">
                                Verified Weight
                              </FormLabel>
                              <FormControl>
                                <Input
                                  step="0.01"
                                  type="number"
                                  inputMode="decimal"
                                  placeholder="ex: 100"
                                  className="bg-background"
                                  {...field}
                                  value={
                                    field.value === 0 ? "" : (field.value ?? "")
                                  }
                                  onChange={(e) => {
                                    const val = e.target.value
                                    if (
                                      val === "" ||
                                      /^[0-9]*\.?[0-9]*$/.test(val)
                                    ) {
                                      field.onChange(
                                        val === "" ? undefined : Number(val)
                                      )
                                    }
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <div className="text-sm">Kgs</div>
                      </div>
                      <div className="sm:w-1/6">
                        <FormField
                          control={control}
                          name={`buckets.${bucketId}.contaminationStatus`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-muted-foreground text-xs">
                                Contamination Status
                              </FormLabel>
                              <Select
                                onValueChange={field.onChange}
                                value={field.value || "clean"}
                              >
                                <SelectTrigger className="border-gray-200 bg-background">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="clean">Clean</SelectItem>
                                  <SelectItem value="contaminated">
                                    Contaminated
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                No buckets selected in the previous step.
              </div>
            )}
          </ul>

          {/* Totals */}
          <div className="grid grid-cols-1 gap-4 pt-4 border-t">
            <div className="flex max-sm:flex-col max-sm:items-start justify-around items-center">
              <div>
                <div className="text-sm text-muted-foreground">
                  NUMBER OF BUCKETS
                </div>
                <div className="text-lg font-semibold">
                  {bucketEntries.length} Buckets
                </div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">
                  ORIGINAL TOTAL
                </div>
                <div className="text-lg font-semibold">
                  {originalTotalWeight.toFixed(2)} KGs
                </div>
              </div>

              <div>
                <div className="text-sm text-muted-foreground">
                  VERIFIED TOTAL
                </div>
                <div className="text-lg font-semibold">
                  {verifiedTotalWeight.toFixed(2)} KGs
                </div>
              </div>

              <div>
                <div className="text-sm text-muted-foreground">DIFFERENCE</div>
                <div
                  className={`text-lg font-semibold ${
                    weightDifference > 0
                      ? "text-green-600"
                      : weightDifference < 0
                        ? "text-red-600"
                        : ""
                  }`}
                >
                  {weightDifference > 0 ? "+" : ""}
                  {weightDifference.toFixed(2)} KGs
                  <span className="text-xs ml-1">
                    ({percentageDifference > 0 ? "+" : ""}
                    {percentageDifference.toFixed(1)}%)
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      <div className="flex flex-cols-2 gap-4">
        <Button
          variant="outline"
          onClick={handleSave}
          className="mt-4 w-full"
          type="button"
          disabled={isAnyLoading}
        >
          {saveLoading ? (
            <>
              <Loader className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            "Save & Exit"
          )}
        </Button>
        <NextButton
          isLast={isLast}
          handleNext={handleSaveNext}
          formPath={formPath}
          isLoading={nextLoading}
        />
      </div>
    </div>
  )
}
