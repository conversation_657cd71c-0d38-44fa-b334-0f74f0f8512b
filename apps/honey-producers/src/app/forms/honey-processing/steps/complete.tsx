"use client"

import { FC } from "react"
import { Card, CardContent, CardTitle } from "@/components/ui/card"
import { NextButton } from "@/components/form/next-button"
import { Weight, Box, CheckCircle } from "lucide-react"
import { format } from "date-fns"

export interface CompleteStepFormData {
  title: string
  createdAt: string
  formId: string
  submitterName: string
  batchCode: string
  wax: number
  numberBuckets: number
  estimatedHoneyWeight: number
}

interface Props {
  handleNext: () => void
  isLast: boolean
  formPath?: string
  formData?: CompleteStepFormData
  isLoading?: boolean
}

export const CompleteStep: FC<Props> = ({
  handleNext,
  isLast,
  formPath,
  formData,
  isLoading,
}) => {
  return (
    <div className="w-full mx-auto flex flex-col gap-6 items-center justify-center min-h-[calc(100vh-14rem)]">
      <Card className="w-full mx-auto bg-primary-foreground">
        <CardContent className="text-center">
          <div className="flex flex-col items-center justify-center pt-6 mb-2">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <CheckCircle className="w-10 h-10 text-green-600" />
            </div>
            <CardTitle className="text-2xl font-semibold mb-2">
              Process Added Successfully!
            </CardTitle>
            <p className="text-gray-500 mb-6">
              {formData?.submitterName} added the batch {formData?.batchCode} to
              the system on{" "}
              {formData?.createdAt &&
                format(new Date(formData.createdAt), "do MMM yyyy")}{" "}
              as Honey Processing.
            </p>

            <div className="bg-blue-500/10 rounded-lg p-4 w-full">
              <h3 className="font-medium text-blue-800 mb-2">
                Batch Code:{" "}
                <span className="font-bold">{formData?.batchCode}</span>
              </h3>
              <p className="text-sm text-blue-700">
                This code can be used to identify the batch in the system.
              </p>
            </div>
          </div>

          <div className="p-4 max-x-md justify-center">
            <h2 className="text-left text-xl font-bold mb-6">
              {formData?.title}
            </h2>
            <div className="space-y-3">
              <div className="grid md:grid-cols-3 gap-4">
                <div className="flex justify-center gap-2">
                  <Weight className="h-5 w-5 text-muted-foreground" />
                  <div className="flex flex-col space-y-1">
                    <p className="text-muted-foreground text-sm">
                      Estimated Honey Weight
                    </p>
                    <p className="font-medium">
                      {formData?.estimatedHoneyWeight} KGs
                    </p>
                  </div>
                </div>
                <div className="flex justify-center gap-2">
                  <Weight className="h-5 w-5 text-muted-foreground" />
                  <div className="flex flex-col space-y-1">
                    <p className="text-muted-foreground text-sm">Wax Weight</p>
                    <p className="font-medium">{formData?.wax} KGs</p>
                  </div>
                </div>
                <div className="flex justify-center gap-2">
                  <Box className="h-5 w-5 text-muted-foreground" />
                  <div className="space-y-1">
                    <p className="text-muted-foreground text-sm">
                      Total Number Of Buckets
                    </p>
                    <p className="font-medium">
                      {formData?.numberBuckets} Buckets
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="w-full">
        <NextButton
          isLast={isLast}
          handleNext={handleNext}
          formPath={formPath}
          isLoading={isLoading}
        />
      </div>
    </div>
  )
}
