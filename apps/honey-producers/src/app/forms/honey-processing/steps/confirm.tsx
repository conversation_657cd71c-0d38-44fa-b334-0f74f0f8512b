"use client"

import { type FC, useEffect } from "react"
import { useFormContext } from "react-hook-form"
import type { FormValues } from "@repo/validation/honey-processing"
import { Card, CardHeader, CardTitle } from "@/components/ui/card"
import { NextButton } from "@/components/form/next-button"
import { CardContent } from "@/components/ui/card"
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { DetailsValue } from "@/components/details-value"
import { locations, ProcessTypes } from "@repo/dummy/honey-processing"

interface Props {
  handleNext: () => void
  isLast: boolean
  formPath?: string
  isLoading?: boolean
}

export const ConfirmStep: FC<Props> = ({
  handleNext,
  isLast,
  formPath,
  isLoading,
}) => {
  const { getValues, setValue } = useFormContext<FormValues>()

  //TODO: to remove when the backend is implemented
  const formData = getValues()
  const estimatedHoneyWeight = formData.verifiedTotalWeight - formData.wax

  useEffect(() => {
    const roundedTotalWeight = Math.round(estimatedHoneyWeight * 100) / 100
    setValue("estimatedHoneyWeight", roundedTotalWeight)
  }, [estimatedHoneyWeight, setValue])

  const bucketEntries = Object.entries(formData.buckets).map(
    ([bucketId, bucket]) => ({
      bucketId,
      bucket,
    })
  )

  return (
    <div className="space-y-4">
      <Card className="bg-primary-foreground border">
        <CardHeader>
          <CardTitle>Confirm your data</CardTitle>
        </CardHeader>
        <CardContent className="max-sm:p-2 space-y-6">
          <Accordion
            type="multiple"
            defaultValue={["honeyCombInputs", "processing"]}
            className="w-full"
          >
            {/* Honey Comb Inputs Section */}
            <AccordionItem value="honeyCombInputs">
              <AccordionTrigger className="group py-2">
                <div className="flex items-center gap-2">
                  <span className="h-2 w-2 rounded-full bg-pink-500" />
                  <span className="font-semibold">Honey Comb Inputs</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="space-y-4 pt-4">
                <div className="bg-background border shadow rounded-lg p-6 grid grid-cols-2 gap-4">
                  <DetailsValue label="Region ID" value={formData.regionId} />
                  <DetailsValue
                    label="Chiefdom ID"
                    value={formData.chiefdomId}
                  />
                  <DetailsValue label="Zone ID" value={formData.zoneId} />
                  <DetailsValue label="Village ID" value={formData.villageId} />
                  <DetailsValue
                    label="Number of buckets used"
                    value={Object.entries(formData.buckets).length}
                  />
                  <DetailsValue
                    label="Total Original HoneyComb Weight"
                    value={`${formData.originalTotalWeight} kg`}
                  />
                  <DetailsValue
                    label="Total Verified HoneyComb Weight"
                    value={`${formData.verifiedTotalWeight} kg`}
                  />
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Processing Section */}
            <AccordionItem value="processing">
              <AccordionTrigger className="group">
                <div className="flex items-center gap-2">
                  <span className="h-2 w-2 rounded-full bg-teal-500" />
                  <span className="font-semibold">Processing</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="space-y-4 pt-2">
                <div className="bg-background border shadow rounded-lg p-6 grid grid-cols-2 gap-4">
                  <DetailsValue
                    label="Factory location"
                    value={
                      locations.find(
                        (location) => location.id === formData.factoryLocation
                      )?.name
                    }
                  />
                  <DetailsValue
                    label="Process type"
                    value={
                      ProcessTypes.find(
                        (processType) => processType.id === formData.processType
                      )?.name
                    }
                  />
                  <DetailsValue
                    label="Inspecting Officer"
                    value={formData.factoryStaffLead.name}
                  />
                  <DetailsValue
                    label="Temperature of Centrifuge"
                    value={formData.centrifugeTemperature}
                  />
                  <DetailsValue
                    label="Temperature of Honey"
                    value={formData.honeyTemperature}
                  />
                  <DetailsValue
                    label="Speed of Centrifuge"
                    value={formData.centrifugeSpeed}
                  />
                  <DetailsValue
                    label="Pump Hopper"
                    value={formData.pumpHopper}
                  />
                  <DetailsValue
                    label="Estimated Weight of Honey"
                    value={`${estimatedHoneyWeight} kg`}
                  />
                  <DetailsValue
                    label="Weight of Wax"
                    value={`${formData.wax} kg`}
                  />
                  <DetailsValue
                    label="Mesh Size"
                    value={`${formData.meshSize} microns`}
                  />
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* List buckets used Section */}
            <AccordionItem value="processing">
              <AccordionTrigger className="group">
                <div className="flex items-center gap-2">
                  <span className="h-2 w-2 rounded-full bg-teal-500" />
                  <span className="font-semibold">Buckets</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="space-y-4 pt-2">
                <div className="space-y-4 pt-2">
                  {bucketEntries.map((bucket) => (
                    <div
                      key={bucket.bucketId}
                      className="relative bg-background border shadow rounded-lg p-4 w-full"
                    >
                      <div className="grid max-sm:grid-cols-2 grid-cols-[auto_auto_auto_auto] gap-2 text-center items-center">
                        <DetailsValue
                          label="ID"
                          value={`#${bucket.bucketId}`}
                        />
                        <DetailsValue
                          label="Original Weight"
                          value={`${formData.buckets[bucket.bucketId]?.weight} Kgs`}
                        />
                        <DetailsValue
                          label="Verified Weight"
                          value={`${formData.buckets[bucket.bucketId]?.verifiedWeight} Kgs`}
                        />
                        <DetailsValue
                          className="capitalize"
                          label="Contamination Status"
                          value={`${formData.buckets[bucket.bucketId]?.contaminationStatus}`}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </CardContent>
      </Card>

      <NextButton
        isLast={isLast}
        handleNext={handleNext}
        formPath={formPath}
        isLoading={isLoading}
      />
    </div>
  )
}
