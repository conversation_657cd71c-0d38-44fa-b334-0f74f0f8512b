"use client"

import { type FC } from "react"
import { useFormContext } from "react-hook-form"
import type { FormValues } from "@repo/validation/honey-processing"
import { NextButton } from "@/components/form/next-button"
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
  CardContent,
} from "@/components/ui/card"
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectTrigger,
  SelectValue,
  SelectItem,
} from "@/components/ui/select"
import api from "@/lib/api"
import { AlertCircle, Loader } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { LocationSelectorOnline } from "@/components/form/location-select-online"

interface Props {
  handleNext: () => void
  isLast: boolean
  formPath?: string
  isLoading?: boolean
}

export const Start: FC<Props> = ({
  handleNext,
  isLast,
  formPath,
  isLoading,
}) => {
  // State to track if a location has been selected (for UI rendering)

  // Get the form control methods
  const { control, watch } = useFormContext<FormValues>()

  // Watch factoryLocation for conditional rendering
  const factoryLocation = watch("factoryLocation")

  const role: string[] = ["owner", "factory_staff"]

  const { data: users, isLoading: usersLoading } = api.useQuery(
    "get",
    "/users",
    {
      params: {
        query: {
          roles: role,
        },
      },
    }
  )

  return (
    <div className="space-y-6">
      <Card className="bg-primary-foreground border">
        <CardHeader>
          <CardTitle className="font-semibold">Create a new record</CardTitle>
          <CardDescription>
            <Alert variant="destructive">
              <AlertCircle className="h-5 w-5" />
              <AlertTitle>Important Notice Before Proceeding</AlertTitle>
              <AlertDescription>
                Before initiating the honey processing, please be aware that
                this process is <strong>irreversible</strong> once started.
                Ensure that all necessary preparations have been made, including
                verifying batch details, equipment readiness, and personnel
                availability.
              </AlertDescription>
              <AlertDescription>
                Once initiated, modifications or cancellations will not be
                possible. Please review all information carefully and proceed
                only when you are certain that all conditions have been met.
              </AlertDescription>
              <AlertDescription>
                If you need to make adjustments, please do so{" "}
                <strong>before</strong> proceeding. Otherwise, click
                <strong> "Proceed"</strong> to continue with the honey
                processing.
              </AlertDescription>
            </Alert>
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          <div className="space-y-4 text-sm">
            <FormField
              control={control}
              name="factoryLocation"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Factory Location</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger
                        id="factory-location"
                        className="bg-background"
                      >
                        <SelectValue placeholder="Select a factory location" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem key="1" value="1">
                        No : 02, Industrial Complex, Katuwana Road, Homagama,
                        Zambia.
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name="processType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Process Type</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value?.toString()}
                  >
                    <FormControl>
                      <SelectTrigger className="bg-background">
                        <SelectValue placeholder="Select Factory Location" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem key="1" value="1">
                        Heating, Centrifuge, First and Second Filtrations
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            {/* Only render Factory Staff Lead when a location is selected */}
            {factoryLocation && (
              <div className="animate-fadeIn">
                <FormField
                  control={control}
                  name="factoryStaffLead"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Factory Staff Lead</FormLabel>
                      {usersLoading ? (
                        <div className="flex justify-center items-center gap-2 pt-1">
                          <Loader className="h-6 w-6 animate-spin text-primary" />
                          <span>Loading users...</span>
                        </div>
                      ) : (
                        <Select
                          onValueChange={(value) =>
                            field.onChange(
                              users?.results?.find(
                                (user) => user.id === value
                              ) ?? undefined
                            )
                          }
                          value={field.value?.id}
                        >
                          <FormControl>
                            <SelectTrigger className="bg-background">
                              <SelectValue placeholder="Select Factory Staff Lead" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {users?.results?.map((user) => (
                              <SelectItem key={user.id} value={user.id}>
                                {user.name} - {user.roleName}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}
          </div>

          <LocationSelectorOnline />
        </CardContent>
      </Card>
      <NextButton
        isLast={isLast}
        handleNext={handleNext}
        formPath={formPath}
        isLoading={isLoading}
      />
    </div>
  )
}
