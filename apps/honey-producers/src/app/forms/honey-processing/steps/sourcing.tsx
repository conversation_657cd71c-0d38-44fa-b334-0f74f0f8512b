"use client"

import { type FC, useState, useEffect, useMemo } from "react"
import { useFormContext } from "react-hook-form"
import { NextButton } from "@/components/form/next-button"
import type { FormValues } from "@repo/validation/honey-processing"
import { BucketSearch } from "@/components/form/bucket-search"
import { Button } from "@/components/ui/button"
import { Loader } from "lucide-react"
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/components/ui/card"
import api from "@/lib/api"
import { paths } from "@repo/api-specs"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface Props {
  handleNext: () => void
  isLast: boolean
  formPath?: string
  isLoading?: boolean
}

export type Bucket =
  paths["/buckets"]["get"]["responses"]["200"]["content"]["application/json"][0]

type HoneyType = "light" | "dark" | undefined

export const Sourcing: FC<Props> = ({
  handleNext,
  isLast,
  formPath,
  isLoading,
}) => {
  const { setValue, watch, getValues } = useFormContext<FormValues>()
  const [selectedBuckets, setSelectedBuckets] = useState<Bucket[]>([])
  const [selectAll, setSelectAll] = useState(false)
  const [searchInput, setSearchInput] = useState("")
  const region = watch("regionId")
  const chiefdom = watch("chiefdomId")
  const zone = watch("zoneId")
  const village = watch("villageId")
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false)
  const [localLoading, setLocalLoading] = useState(false)
  const [honeyType, setHoneyType] = useState<HoneyType>(undefined)

  const { data: buckets, isLoading: bucketsLoading } = api.useQuery(
    "get",
    "/buckets",
    {
      params: {
        query: {
          region,
          chiefdom,
          zone,
          village,
          query: searchInput,
          honeyType,
        },
      },
    },
    { enabled: !!region && !!zone }
  )

  const mutation = api.useMutation("post", "/honey-processing", {
    onSuccess: (response) => {
      setValue("formId", response.id)

      setLocalLoading(false)

      handleNext()
    },
    onError: (e) => {
      console.log(e.message)

      setLocalLoading(false)
    },
  })

  const selectedBucketIds = useMemo(() => {
    return selectedBuckets.map((bucket) => bucket.id)
  }, [selectedBuckets])

  useEffect(() => {
    const updatedBuckets = selectedBuckets.reduce(
      (acc, bucket) => {
        acc[bucket.id] = {
          weight: bucket.weight || 0,
          verifiedWeight:
            bucket.verifiedWeight !== undefined
              ? bucket.verifiedWeight
              : undefined,
          contaminationStatus:
            bucket.contaminationStatus === "clean" ||
            bucket.contaminationStatus === "contaminated"
              ? bucket.contaminationStatus
              : undefined,
        } as {
          weight: number
          verifiedWeight?: number
          contaminationStatus: undefined | "clean" | "contaminated"
        }
        return acc
      },
      {} as Record<
        string,
        {
          weight: number
          verifiedWeight?: number
          contaminationStatus: undefined | "clean" | "contaminated"
        }
      >
    )

    setValue("buckets", updatedBuckets)
  }, [selectedBuckets, setValue])

  // Calculate the total weight
  const originalTotalWeight = useMemo(() => {
    return selectedBuckets.reduce((sum, bucket) => {
      return sum + (bucket?.weight || 0)
    }, 0)
  }, [selectedBuckets])

  // Update the value in the form
  useEffect(() => {
    const roundedTotalWeight = Math.round(originalTotalWeight * 100) / 100
    setValue("originalTotalWeight", roundedTotalWeight)
  }, [originalTotalWeight, setValue])

  const handleBucketToggle = (bucket: Bucket) => {
    const newSelectedBuckets = selectedBucketIds.includes(bucket.id)
      ? selectedBuckets.filter(
          (selectedBucket) => selectedBucket.id !== bucket.id
        )
      : [...selectedBuckets, bucket]

    setSelectedBuckets(newSelectedBuckets)
    setValue(
      "buckets",
      Object.fromEntries(
        newSelectedBuckets.map((bucket) => [bucket.id, bucket.weight, {}])
      )
    )
  }

  const hasMultipleTypes = useMemo(
    () =>
      Array.from(new Set(selectedBuckets.map((bucket) => bucket.honeyType)))
        .length > 1,
    [selectedBuckets]
  )

  const handleSelectAll = () => {
    if (!buckets?.length) return

    if (selectAll) {
      // Deselect all
      setSelectedBuckets([])
      setSelectAll(false)
    } else {
      // Select all
      setSelectedBuckets(buckets)
      setSelectAll(true)
    }

    // Update the form value
    setValue(
      "buckets",
      Object.fromEntries(
        selectAll ? [] : buckets.map((bucket) => [bucket.id, bucket.weight, {}])
      )
    )
  }

  // Handle save on next press
  const handleSaveNext = () => {
    setLocalLoading(true)
    const buckets = getValues("buckets")

    mutation.mutate({
      body: {
        factorId: getValues("factoryLocation"),
        processTypeId: getValues("processType"),
        factoryStaffLeadId: getValues("factoryStaffLead.id"),
        processStartDate: new Date().toISOString(),
        waxKg: getValues("wax"),
        buckets: Object.keys(buckets),
      },
    })
  }

  const handleHoneyTypeChange = (value: string) => {
    if (value === "all") {
      setHoneyType(undefined)
    } else if (value === "light" || value === "dark") {
      setHoneyType(value)
    }
  }

  return (
    <div className="space-y-6">
      <Card className="bg-primary-foreground border">
        <CardHeader className="max-sm:flex max-sm:gap-2 max-sm:mb-0 mb-4 relative p-4">
          <CardTitle className="font-semibold">Select your sources</CardTitle>
          <CardDescription>
            Select the honeycomb buckets that will be processed today
          </CardDescription>
          <div className="max-sm:flex gap-2 justify-center">
            <Badge
              variant="outline"
              className="md:absolute max-sm:w-fit top-3 right-6 bg-background text-sm"
            >
              {zone}
            </Badge>

            {/* Filter by type of honey*/}
            <div className="md:absolute top-12 right-6 bg-background text-sm">
              <div>
                <Select
                  defaultValue={honeyType}
                  onValueChange={handleHoneyTypeChange}
                >
                  <SelectTrigger className="bg-background">
                    <SelectValue placeholder="Filter by honey type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Type</SelectItem>
                    <SelectItem value="light">Light</SelectItem>
                    <SelectItem value="dark">Dark</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* List of Buckets */}
          <BucketSearch
            buckets={buckets || []}
            selectedBuckets={selectedBuckets}
            selectedBucketIds={selectedBucketIds}
            onSelectBucket={handleBucketToggle}
            onSelectAll={handleSelectAll}
            selectAll={selectAll}
            searchInput={searchInput}
            onSearchChange={setSearchInput}
            isLoading={bucketsLoading}
            hasMultipleTypes={hasMultipleTypes}
          />

          {/*Totals*/}
          <div className="max-sm:flex-col flex flex-cols-2 justify-around gap-4 pt-4 border-t">
            <div>
              <div className="text-sm text-muted-foreground">TOTAL WEIGHT</div>
              <div className="text-lg font-semibold">
                {selectedBuckets.length > 0
                  ? originalTotalWeight.toFixed(2)
                  : "0"}{" "}
                KGs
              </div>
            </div>
            <div>
              <div className="text-sm text-muted-foreground">
                TOTAL NUMBER OF BUCKETS
              </div>
              <div className="text-lg font-semibold">
                {selectedBuckets.length} Buckets
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      <NextButton
        isLast={isLast}
        handleNext={() => setIsConfirmDialogOpen(true)}
        formPath={formPath}
        isLoading={isLoading}
        disabled={hasMultipleTypes}
      />
      <Dialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
        <DialogContent className="sm:max-w-sm">
          <DialogHeader>
            <DialogTitle>Confirm Process Initiation</DialogTitle>
            <DialogDescription>
              Are you sure you want to start the honey processing? Once
              initiated, the process cannot be canceled.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setIsConfirmDialogOpen(false)}
              disabled={localLoading}
            >
              Cancel
            </Button>
            <Button onClick={handleSaveNext} disabled={localLoading}>
              {localLoading ? (
                <>
                  <Loader className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                "Proceed"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
