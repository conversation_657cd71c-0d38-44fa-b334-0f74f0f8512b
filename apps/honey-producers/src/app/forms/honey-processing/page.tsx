"use client"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Search, Loader, PlusCircle } from "lucide-react"
import type { DateRange } from "react-day-picker"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useRouter } from "next/navigation"
import { DatePickerWithRange } from "@/components/date-picker-with-range"
import api from "@/lib/api"
import { format } from "date-fns"
import { Button } from "@/components/ui/button"

type SortOrder = "newestFirst" | "oldestFirst"

export default function RecordsPage() {
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState("")
  const [dateRange, setDateRange] = useState<DateRange>()
  const [sortOrder, setSortOrder] = useState<SortOrder>("newestFirst")

  const { data: records, isLoading } = api.useQuery(
    "get",
    "/honey-processing",
    {
      params: {
        query: {
          query: searchTerm,
          createdAtFrom: dateRange?.from?.toISOString(),
          createdAtTo: dateRange?.to?.toISOString(),
          sortBy: sortOrder,
        },
      },
    }
  )

  return (
    <div className="container max-w-screen-xl mx-auto py-8 space-y-8">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-2xl font-semibold">Existing Records</h1>
        <p className="text-sm text-muted-foreground">
          View and search for any existing record on Honey Processing.
        </p>
      </div>

      {/* Filters */}
      <div className="flex flex-col lg:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search by id, batch code"
            className="pl-9"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="flex flex-col sm:flex-row gap-4 lg:w-auto">
          <DatePickerWithRange date={dateRange} onSelect={setDateRange} />

          <Select
            defaultValue={sortOrder}
            onValueChange={(value: SortOrder) => setSortOrder(value)}
          >
            <SelectTrigger className="w-full sm:w-[200px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="newestFirst">Date (Newest first)</SelectItem>
              <SelectItem value="oldestFirst">Date (Oldest first)</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <Button
          variant="default"
          onClick={() => router.push("/forms/honey-processing/create")}
        >
          <PlusCircle className="h-2 w-2 mr-2" />
          Add Process
        </Button>
      </div>

      {/* Table */}
      <div className="border shadow rounded-lg">
        <Table>
          <TableHeader className="bg-primary-foreground">
            <TableRow>
              <TableHead className="w-[60px] text-center font-medium text-muted-foreground">
                ID
              </TableHead>
              <TableHead className="w-[140px] text-center font-medium text-muted-foreground">
                Status
              </TableHead>
              <TableHead className="w-[240px] text-center md:w-[100px] font-medium text-muted-foreground">
                Started at
              </TableHead>
              <TableHead className="w-[120px] text-center font-medium text-muted-foreground">
                Number of buckets
              </TableHead>
              <TableHead className="w-[120px] text-center font-medium text-muted-foreground">
                Honey Produced
              </TableHead>
              <TableHead className="w-[120px] text-center font-medium text-muted-foreground">
                Wax Produced
              </TableHead>
              <TableHead className="w-[160px] text-center font-medium text-muted-foreground">
                Batch Code
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={9} className="h-24 text-center">
                  <div className="flex justify-center items-center">
                    <Loader className="h-6 w-6 animate-spin text-primary" />
                    <span className="ml-2">Loading records...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : records?.results && records.results.length > 0 ? (
              records.results.map((record) => (
                <TableRow
                  key={record.id}
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() =>
                    router.push(`/forms/honey-processing/form#${record.id}`)
                  }
                >
                  <TableCell className="text-center">{record.id}</TableCell>
                  <TableCell className="text-center whitespace-nowrap">
                    <span
                      className={`inline-flex items-center justify-center px-2.5 py-0.5 rounded-lg ${
                        record.processEndDate
                          ? "bg-green-500/40 text-green-700"
                          : "bg-teal-400/40 text-teal-700"
                      }`}
                    >
                      {record.processEndDate ? "Completed" : "In Process"}
                    </span>
                  </TableCell>
                  <TableCell className="text-center">
                    {format(
                      new Date(record.processStartDate),
                      "yyyy-MM-dd HH:mm:ss"
                    )}
                  </TableCell>

                  <TableCell className="text-center">
                    {record.buckets.length}
                  </TableCell>
                  <TableCell className="text-center">
                    {record.honeyKg} kg
                  </TableCell>
                  <TableCell className="text-center">
                    {record.waxKg} kg
                  </TableCell>
                  <TableCell className="text-center font-mono">
                    {record.batchCode}
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={9} className="h-24 text-center">
                  No records found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
