"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { use<PERSON><PERSON>, SubmitHand<PERSON>, FormProvider } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Step, Stepper } from "@/components/form/stepper"
import {
  FormFieldNames,
  formSchema,
  FormValues,
} from "@repo/validation/honey-processing"
import {
  Start,
  Sourcing,
  Weight,
  Crusher,
  Centrifuge,
  Filtration,
  ConfirmStep,
  CompleteStep,
} from "../steps"
import { CompleteStepFormData } from "../steps/complete"
import { authClient } from "@/lib/auth-client"
import { locations, ProcessTypes } from "@repo/dummy/honey-processing"
import api from "@/lib/api"

const HoneyProcessingPage = () => {
  const router = useRouter()
  const [step, setStep] = useState(1)
  const [formData, setFormData] = useState<CompleteStepFormData>()
  const { data } = authClient.useSession()
  const user = data?.user
  const [isLoading, setIsLoading] = useState(false)

  const methods = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      processType: ProcessTypes[0].id,
      factoryLocation: locations[0].id,
      buckets: { verifiedWeight: undefined, weight: undefined },
    },
  })

  const stepComponents = [
    Start,
    Sourcing,
    Weight,
    Crusher,
    Centrifuge,
    Filtration,
    ConfirmStep,
    CompleteStep,
  ]

  const stepSections = [
    { id: 1, label: "Start", range: [1, 1] },
    { id: 2, label: "Sourcing", range: [2, 2] },
    { id: 3, label: "Weight", range: [3, 3] },
    { id: 4, label: "Crusher", range: [4, 4] },
    { id: 5, label: "Centrifuge", range: [5, 5] },
    { id: 6, label: "Filtration", range: [6, 6] },
    { id: 7, label: "Review", range: [7, 7] },
    { id: 8, label: "Finish", range: [8, 8] },
  ]

  const stepperState: Step[] = stepSections.map(({ id, label, range }) => {
    const [start, end] = range
    return {
      id,
      label,
      status:
        step > end
          ? "completed"
          : step >= start && step <= end
            ? "current"
            : "upcoming",
    }
  })
  const getCurrentStepFieldNames = (step: number) => {
    // Define which fields belong to which step
    const stepFields: Record<number, FormFieldNames[]> = {
      1: [
        "factoryStaffLead",
        "factoryLocation",
        "processType",
        "regionId",
        "chiefdomId",
        "zoneId",
        "villageId",
      ],
      2: ["buckets", "originalTotalWeight"],
      3: ["buckets", "verifiedTotalWeight"],
      4: ["pumpHopper"],
      5: ["wax", "centrifugeSpeed"],
      6: ["meshSize"],
      7: ["honeyTemperature", "centrifugeTemperature"],
      8: ["estimatedHoneyWeight"],
    }

    return stepFields[step] || []
  }

  const moveToNextStep = async () => {
    methods.clearErrors()
    // Trigger validation before moving to the next step
    const isValid = await methods.trigger(getCurrentStepFieldNames(step))
    if (!isValid) return

    setIsLoading(true)

    setStep(step + 1)

    setIsLoading(false)
  }

  const mutation = api.useMutation("post", "/honey-processing/{id}", {
    onSuccess: (res) => {
      setFormData({
        title: "Honey Processing",
        createdAt: new Date().toISOString(),
        formId: methods.getValues("formId") || "",
        submitterName: user?.name || "",
        batchCode: res.batchCode,
        estimatedHoneyWeight: methods.getValues("estimatedHoneyWeight"),
        wax: methods.getValues("wax"),
        numberBuckets: Object.keys(methods.getValues("buckets")).length,
      })
      methods.reset()
      setStep(step + 1)

      setIsLoading(false)
    },
    onError: (e) => {
      console.log(e.message)

      setIsLoading(false)
    },
  })

  const onSubmit: SubmitHandler<FormValues> = (data) => {
    setIsLoading(true)
    if (!data.formId) throw new Error("Form ID is required")

    mutation.mutate({
      params: { path: { id: data.formId } },
      body: {
        processEndDate: new Date().toISOString(),
        waxKg: data.wax,
        honeyKg: data.estimatedHoneyWeight,
        buckets: data.buckets,
        centrifugeTemperature: data.centrifugeTemperature,
        honeyTemperature: data.honeyTemperature,
        centrifugeSpeed: data.centrifugeSpeed,
        pumpHopper: data.pumpHopper,
        meshSize: data.meshSize,
      },
    })
  }

  const moveToPreviousStep = () =>
    step === 1 ? router.push("/forms/honey-processing") : setStep(step - 1)

  const renderStep = () => {
    const CurrentStepComponent = stepComponents[step - 1]

    return (
      <CurrentStepComponent
        handleNext={moveToNextStep}
        isLast={step === stepComponents.length - 1}
        formPath={formData && `/forms/honey-processing/form#${formData.formId}`}
        formData={formData}
        isLoading={isLoading}
      />
    )
  }

  return (
    <FormProvider {...methods}>
      <div className="flex items-center flex-col md:mx-4">
        <Stepper
          steps={stepperState}
          progress={(step / stepComponents.length) * 100}
          onBack={moveToPreviousStep}
          backDisabled={!!formData || step === 3}
        />
        <div className="w-full max-w-[800px]">
          <form onSubmit={methods.handleSubmit(onSubmit)}>{renderStep()}</form>
        </div>
      </div>
    </FormProvider>
  )
}

export default HoneyProcessingPage
