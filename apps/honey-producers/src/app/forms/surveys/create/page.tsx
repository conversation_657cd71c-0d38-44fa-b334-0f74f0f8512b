"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { useF<PERSON>, SubmitHandler, FormProvider } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Step, Stepper } from "@/components/form/stepper"
import {
  FormFieldNames,
  formSchema,
  FormValues,
} from "@repo/validation/surveys"
import {
  FarmerSelect,
  Beehive1,
  Beehive2,
  Beehive3,
  ManagerInputs1,
  ManagerInputs2,
  ConfirmStep,
  CompleteStep,
} from "./steps/"
import { CompleteStepFormData } from "./steps/complete"
import api from "@/lib/api"
import { authClient } from "@/lib/auth-client"

const FormCompliancePage = () => {
  const router = useRouter()
  const [step, setStep] = useState(1)
  const [formData, setFormData] = useState<CompleteStepFormData>()
  const { data } = authClient.useSession()
  const user = data?.user
  const [isLoading, setIsLoading] = useState(false)

  const methods = useForm({ resolver: zodResolver(formSchema) })
  const farmer = methods.watch("farmer")

  // Reset beehive selections when farmer changes
  useEffect(() => {
    if (!farmer) return
    methods.reset({
      ...methods.getValues(),
      beehives: {
        numberPresent: "",
        visitedMonthly: undefined,
        visitedRecently: [],
        areAnyOccupied: undefined,
        awayFromWater: undefined,
        awayFromCrops: undefined,
        adequateFlowering: undefined,
        readyForHarvest: "",
        rebaitMaterial: "",
        enoughHoneyForWinter: undefined,
        beesWereMutilated: undefined,
        organicWaxUsed: undefined,
        provideFoodForBees: undefined,
        hasInfestation: undefined,
        infestationDetails: "",
      },
    })
  }, [farmer?.id])

  const mutation = api.useMutation("post", "/forms/compliance-submissions", {
    onSuccess: (response) => {
      const farmerId = methods.getValues("farmer.id")
      const farmerName = methods.getValues("farmer.name")

      setFormData({
        title: "Surveys",
        createdAt: new Date().toISOString(),
        formId: response.id || "",
        submitterName: user?.name || "",
        farmerId,
        farmerName,
      })
      methods.reset()
      setStep(step + 1)

      setIsLoading(false)
    },
    onError: (e) => {
      console.log(e.message)

      setIsLoading(false)
    },
  })

  const stepComponents = [
    FarmerSelect,
    Beehive1,
    Beehive2,
    Beehive3,
    ManagerInputs1,
    ManagerInputs2,
    ConfirmStep,
    CompleteStep,
  ]

  const stepSections = [
    { id: 1, label: "Farmer Select", range: [1, 1] },
    { id: 2, label: "Beehive Details", range: [2, 4] },
    { id: 3, label: "Manager Inputs", range: [5, 6] },
    { id: 4, label: "Submit", range: [7, 7] },
  ]

  const stepperState: Step[] = stepSections.map(({ id, label, range }) => {
    const [start, end] = range
    return {
      id,
      label,
      status:
        step > end
          ? "completed"
          : step >= start && step <= end
            ? "current"
            : "upcoming",
    }
  })

  const getCurrentStepFieldNames = (step: number) => {
    // Define which fields belong to which step
    const stepFields: Record<number, FormFieldNames[]> = {
      1: ["farmer"],
      2: [
        "beehives.numberPresent",
        "beehives.visitedMonthly",
        "beehives.visitedRecently",
        "beehives.areAnyOccupied",
      ],
      3: [
        "beehives.awayFromWater",
        "beehives.awayFromCrops",
        "beehives.adequateFlowering",
        "beehives.readyForHarvest",
        "beehives.rebaitMaterial",
      ],
      4: [
        "beehives.enoughHoneyForWinter",
        "beehives.beesWereMutilated",
        "beehives.organicWaxUsed",
        "beehives.provideFoodForBees",
        "beehives.hasInfestation",
        "beehives.infestationDetails",
      ],
      5: ["managerInputs.additionalComments"],
      6: [
        "managerInputs.isCompliant",
        "managerInputs.complianceReason",
        "managerInputs.nonComplianceReason",
      ],
    }

    return stepFields[step] || []
  }

  const moveToNextStep = async () => {
    methods.clearErrors()
    // Trigger validation before moving to the next step
    const isValid = await methods.trigger(getCurrentStepFieldNames(step))
    if (!isValid) return

    setIsLoading(true)

    setStep(step + 1)

    setIsLoading(false)
  }

  const onSubmit: SubmitHandler<FormValues> = (formValues) => {
    setIsLoading(true)
    const farmerId = methods.getValues("farmer.id")

    mutation.mutate({
      body: {
        farmerId,
        beehives: formValues.beehives,
        managerInputs: formValues.managerInputs,
      },
    })
  }

  const handleEdit = (sectionId: number) => {
    const stepId = stepSections.find((section) => section.id === sectionId)
      ?.range[0]

    if (!stepId) return

    setStep(stepId)
  }

  const moveToPreviousStep = () =>
    step === 1 ? router.push("/forms/surveys") : setStep(step - 1)

  const renderStep = () => {
    const CurrentStepComponent = stepComponents[step - 1]

    return (
      <CurrentStepComponent
        handleNext={moveToNextStep}
        isLast={step === stepComponents.length - 1}
        formPath={formData && `/forms/surveys/form#${formData.formId}`}
        handleEdit={handleEdit}
        formData={formData}
        isLoading={isLoading}
      />
    )
  }

  return (
    <FormProvider {...methods}>
      <div className="flex items-center flex-col md:mx-4">
        <Stepper
          steps={stepperState}
          progress={(step / stepComponents.length) * 100}
          onBack={moveToPreviousStep}
          backDisabled={!!formData}
        />
        <div className="w-full max-w-[800px]">
          <form onSubmit={methods.handleSubmit(onSubmit)}>{renderStep()}</form>
        </div>
      </div>
    </FormProvider>
  )
}

export default FormCompliancePage
