"use client"

import { type FC } from "react"
import { useFormContext } from "react-hook-form"
import { NextButton } from "@/components/form/next-button"
import type { FormValues } from "@repo/validation/surveys"
import { Separator } from "@/components/ui/separator"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { BeehiveSelect } from "@/components/form/beehive-select"

interface Props {
  handleNext: () => void
  isLast: boolean
  formPath?: string
  isLoading?: boolean
}

export const Beehive1: FC<Props> = ({
  handleNext,
  isLast,
  formPath,
  isLoading,
}) => {
  const { control, watch } = useFormContext<FormValues>()

  const farmerId = watch("farmer.id")
  const visitedMonthly = watch("beehives.visitedMonthly")

  return (
    <div className="space-y-6">
      <Card className="bg-primary-foreground border">
        <CardHeader>
          <CardTitle>Input your records</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Number of Beehives */}
          <FormField
            control={control}
            name="beehives.numberPresent"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Number of beehives</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value?.toString()}
                >
                  <FormControl>
                    <SelectTrigger className="bg-background">
                      <SelectValue placeholder="Select number of beehives" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((num) => (
                      <SelectItem key={num} value={num.toString()}>
                        {num}
                      </SelectItem>
                    ))}
                    <SelectItem value="11">More than 10</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <Separator />

          {/* Do you visit your hives every month? */}
          <FormField
            control={control}
            name="beehives.visitedMonthly"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>Do you visit your hives every month?</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={(value) => field.onChange(value === "true")}
                    value={field.value?.toString()}
                    className="flex flex-col space-y-1 md:flex-row md:space-y-0 md:space-x-4"
                  >
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="true" />
                      </FormControl>
                      <FormLabel className="font-normal">Yes</FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="false" />
                      </FormControl>
                      <FormLabel className="font-normal">No</FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Separator />

          {visitedMonthly && (
            <>
              {/* Which numbers did you recently visit? */}
              <FormField
                control={control}
                name="beehives.visitedRecently"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormLabel>Which numbers did you recently visit?</FormLabel>
                    <FormControl>
                      <BeehiveSelect
                        farmerId={farmerId}
                        onChange={field.onChange}
                        defaultValue={field.value}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <Separator />

              {/* Are any of the hives occupied */}
              <FormField
                control={control}
                name="beehives.areAnyOccupied"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormLabel>Are any of the hives occupied?</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={(value) =>
                          field.onChange(value === "true")
                        }
                        value={field.value?.toString()}
                        className="flex flex-col space-y-1 md:flex-row md:space-y-0 md:space-x-4"
                      >
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="true" />
                          </FormControl>
                          <FormLabel className="font-normal">Yes</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="false" />
                          </FormControl>
                          <FormLabel className="font-normal">No</FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </>
          )}
        </CardContent>
      </Card>

      <NextButton
        isLast={isLast}
        handleNext={handleNext}
        formPath={formPath}
        isLoading={isLoading}
      />
    </div>
  )
}
