"use client"

import type { <PERSON> } from "react"
import { useFormContext } from "react-hook-form"
import { NextButton } from "@/components/form/next-button"
import type { FormValues } from "@repo/validation/surveys"
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"

interface Props {
  handleNext: () => void
  isLast: boolean
  formPath?: string
  isLoading?: boolean
}

export const ManagerInputs1: FC<Props> = ({
  handleNext,
  isLast,
  formPath,
  isLoading,
}) => {
  const { control } = useFormContext<FormValues>()

  return (
    <div className="space-y-6">
      <Card className="bg-primary-foreground border">
        <CardHeader>
          <CardTitle>Manager inputs</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <FormField
            control={control}
            name="managerInputs.additionalComments"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Do you have any other comments you would like us to know?
                </FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Type here..."
                    className="bg-background resize-none"
                    maxLength={250}
                    {...field}
                  />
                </FormControl>
                <div className="text-xs text-muted-foreground text-right">
                  {field.value?.length || 0}/250
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
      </Card>

      <NextButton
        isLast={isLast}
        handleNext={handleNext}
        formPath={formPath}
        isLoading={isLoading}
      />
    </div>
  )
}
