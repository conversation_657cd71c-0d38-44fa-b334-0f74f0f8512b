"use client"

import { type FC } from "react"
import { useFormContext } from "react-hook-form"
import { NextButton } from "@/components/form/next-button"
import { FarmerSearch } from "@/components/form/farmer-search"
import type { FormValues } from "@repo/validation/surveys"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { FormField, FormItem, FormLabel } from "@/components/ui/form"

interface Props {
  handleNext: () => void
  isLast: boolean
  formPath?: string
  isLoading?: boolean
}

export const FarmerSelect: FC<Props> = ({
  handleNext,
  isLast,
  formPath,
  isLoading,
}) => {
  const { control } = useFormContext<FormValues>()

  return (
    <div className="space-y-6">
      <Card className="bg-primary-foreground border">
        <CardHeader>
          <CardTitle>Select your farmer</CardTitle>
        </CardHeader>
        <CardContent>
          <FormField
            control={control}
            name="farmer"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Search by farmer ID or name</FormLabel>
                <FarmerSearch
                  selectedFarmerId={field.value?.id}
                  onSelect={(data) =>
                    field.onChange({
                      id: data.id,
                      name: `${data.firstName} ${data.lastName}`,
                    })
                  }
                />
              </FormItem>
            )}
          />
        </CardContent>
      </Card>

      <NextButton
        isLast={isLast}
        handleNext={handleNext}
        formPath={formPath}
        isLoading={isLoading}
      />
    </div>
  )
}
