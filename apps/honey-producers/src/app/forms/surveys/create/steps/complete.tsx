"use client"

import type { FC } from "react"
import { Card, CardContent, CardTitle } from "@/components/ui/card"
import { NextButton } from "@/components/form/next-button"
import { User, Hash, CheckCircle } from "lucide-react"
import { format } from "date-fns"

export interface CompleteStepFormData {
  title: string
  createdAt: string
  formId: string
  submitterName: string
  farmerName: string
  farmerId: string
}

interface Props {
  handleNext: () => void
  isLast: boolean
  formPath?: string
  formData?: CompleteStepFormData
  isLoading?: boolean
}

export const CompleteStep: FC<Props> = ({
  handleNext,
  isLast,
  formPath,
  formData,
  isLoading,
}) => {
  return (
    <div className="w-full mx-auto flex flex-col gap-6 items-center justify-center min-h-[calc(100vh-14rem)]">
      <Card className="w-full mx-auto bg-primary-foreground">
        <CardContent className="text-center">
          <div className="flex flex-col items-center justify-center pt-6 mb-2">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <CheckCircle className="w-10 h-10 text-green-600" />
            </div>

            <CardTitle className="text-2xl font-semibold mb-2">
              Surveys added Successfully!
            </CardTitle>
            <p className="text-gray-500 mb-6">
              {formData?.submitterName} added a form to the system on{" "}
              {formData?.createdAt &&
                format(new Date(formData.createdAt), "do MMM yyyy")}{" "}
              as Surveys.
            </p>

            <div className="bg-blue-500/10 rounded-lg p-4 w-full">
              <h3 className="font-medium text-blue-800 mb-2">
                Form ID: <span className="font-bold">#{formData?.formId}</span>
              </h3>
              <p className="text-sm text-blue-700">
                This ID can be used to identify the surveys in the system.
              </p>
            </div>
          </div>
          <div className="p-4 max-x-md justify-center">
            <h2 className="text-left text-xl font-bold mb-6">
              {formData?.title}
            </h2>
            <div className="space-y-3">
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <User className="h-5 w-5 text-muted-foreground" />
                  <div className="space-y-1">
                    <p className="text-muted-foreground text-sm">Farmer Name</p>
                    <p className="font-medium">{formData?.farmerName}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Hash className="h-5 w-5 text-muted-foreground" />
                  <div className="space-y-1">
                    <p className="text-muted-foreground text-sm">Farmer ID</p>
                    <p className="font-medium">{formData?.farmerId}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="w-full">
        <NextButton
          isLast={isLast}
          handleNext={handleNext}
          formPath={formPath}
          isLoading={isLoading}
        />
      </div>
    </div>
  )
}
