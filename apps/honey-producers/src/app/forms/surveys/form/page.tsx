"use client"

import { useEffect, useState } from "react"
import type React from "react"
import { FormDetailsContainer } from "./form-details-container"

export default function FormCompliancePage() {
  const [formId, setFormId] = useState<string | null>(null)

  useEffect(() => {
    const handleHashChange = () => {
      const hash = window.location.hash.replace("#", "")
      if (hash) setFormId(hash)
    }

    handleHashChange()

    window.addEventListener("hashchange", handleHashChange)
    return () => window.removeEventListener("hashchange", handleHashChange)
  }, [])

  if (!formId) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <p>Loading...</p>
      </div>
    )
  }

  return (
    <div className="container bg-primary-foreground border shadow rounded-lg mx-auto max-w-screen-xl pt-6 pb-2 space-y-8">
      <FormDetailsContainer formId={formId} />
    </div>
  )
}
