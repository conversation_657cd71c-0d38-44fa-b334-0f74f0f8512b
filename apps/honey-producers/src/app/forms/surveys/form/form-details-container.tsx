"use client"
import React from "react"
import FormDetails from "../components/form-details"
import api from "@/lib/api"
import { Loader, ArrowLeft } from "lucide-react"
import { CardContent, CardHeader } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { format } from "date-fns"
import { useRouter } from "next/navigation"

interface Props {
  formId: string
}

export const FormDetailsContainer: React.FC<Props> = ({ formId }) => {
  const router = useRouter()
  const { data: complianceData, isLoading } = api.useQuery(
    "get",
    "/forms/compliance-submissions/{id}",
    {
      params: {
        path: {
          id: formId,
        },
      },
    }
  )

  if (isLoading) {
    return (
      <div className="flex justify-center items-center">
        <Loader className="h-6 w-6 animate-spin text-primary" />
        <span className="ml-2">Loading surveys data...</span>
      </div>
    )
  }

  if (!complianceData) {
    return (
      <div className="flex items-center justify-center">
        <CardContent className="pt-6 flex flex-col items-center text-center space-y-4">
          <p className="text-red-500 font-medium">
            An unknown error occurred while fetching the form data.
          </p>
          <Button
            className="w-full"
            onClick={() => router.push(`/forms/surveys`)}
          >
            Back to Surveys List
          </Button>
        </CardContent>
      </div>
    )
  }

  return (
    <div>
      <CardHeader className="space-y-2">
        {/* Header */}
        <Button
          variant="outline"
          size="sm"
          className="font-semibold w-fit"
          onClick={() => router.push("/forms/surveys")}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>

        <div className="space-y-6">
          <h1 className="text-2xl font-semibold text-center">
            Organic farmer Questionnaire
          </h1>
          <div className="flex max-sm:flex-col max-sm:gap-1 justify-between text-sm text-muted-foreground px-6">
            <p>By: {complianceData.createdBy.name}</p>
            <p>
              {format(
                new Date(complianceData.createdAt),
                "yyyy-MM-dd HH:mm:ss"
              )}
            </p>
            <p>ID: {formId}</p>
          </div>
        </div>
      </CardHeader>
      <FormDetails formData={complianceData} />
    </div>
  )
}
