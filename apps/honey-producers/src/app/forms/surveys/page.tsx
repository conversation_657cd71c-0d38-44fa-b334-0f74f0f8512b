"use client"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Search, Loader, PlusCircle } from "lucide-react"
import type { DateRange } from "react-day-picker"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { useRouter } from "next/navigation"
import { DatePickerWithRange } from "@/components/date-picker-with-range"
import api from "@/lib/api"
import { format } from "date-fns"
import { Button } from "@/components/ui/button"

type SortOrder = "newestFirst" | "oldestFirst"

export default function RecordsPage() {
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState("")
  const [dateRange, setDateRange] = useState<DateRange>()
  const [sortOrder, setSortOrder] = useState<SortOrder>("newestFirst")

  const { data: records, isLoading } = api.useQuery(
    "get",
    "/forms/compliance-submissions",
    {
      params: {
        query: {
          query: searchTerm,
          createdAtFrom: dateRange?.from?.toISOString(),
          createdAtTo: dateRange?.to?.toISOString(),
          sortBy: sortOrder,
        },
      },
    }
  )

  return (
    <div className="container max-w-screen-xl mx-auto py-8 space-y-8">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-2xl font-semibold">Existing Records</h1>
        <p className="text-sm text-muted-foreground">
          View and search for any existing record on Organic Farmer
          Questionnaire.
        </p>
      </div>

      {/* Filters */}
      <div className="flex flex-col lg:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search by form id, farmer name, farmer id or farm name"
            className="pl-9"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="flex flex-col sm:flex-row gap-4 lg:w-auto">
          <DatePickerWithRange date={dateRange} onSelect={setDateRange} />

          <Select
            defaultValue={sortOrder}
            onValueChange={(value: SortOrder) => setSortOrder(value)}
          >
            <SelectTrigger className="w-full sm:w-[200px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="newestFirst">Date (Newest first)</SelectItem>
              <SelectItem value="oldestFirst">Date (Oldest first)</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <Button
          variant="default"
          onClick={() => router.push("/forms/surveys/create")}
        >
          <PlusCircle className="h-2 w-2 mr-2" />
          Add Surveys
        </Button>
      </div>

      {/* Table */}
      <div className="border shadow rounded-lg">
        <Table>
          <TableHeader className="bg-primary-foreground">
            <TableRow>
              <TableHead className="w-[220px] font-medium text-muted-foreground">
                Form ID
              </TableHead>
              <TableHead className="w-[80px] font-medium text-muted-foreground">
                Farmer ID
              </TableHead>
              <TableHead className="w-[180px] font-medium text-muted-foreground">
                Zone
              </TableHead>
              <TableHead className="w-[180px] font-medium text-muted-foreground">
                Submitter name
              </TableHead>
              <TableHead className="w-[120px] font-medium text-muted-foreground">
                Date
              </TableHead>
              <TableHead className="w-[120px] text-right font-medium text-muted-foreground">
                Status
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={6} className="h-24 text-center">
                  <div className="flex justify-center items-center">
                    <Loader className="h-6 w-6 animate-spin text-primary" />
                    <span className="ml-2">Loading records...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : records?.results && records.results.length > 0 ? (
              records.results.map((record) => (
                <TableRow
                  key={record.id}
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() =>
                    router.push(`/forms/surveys/form#${record.id}`)
                  }
                >
                  <TableCell className="font-mono">{record.id}</TableCell>
                  <TableCell>{record.farmer.id}</TableCell>
                  <TableCell>{record.farmer.zoneId}</TableCell>
                  <TableCell>{record.createdBy.name}</TableCell>
                  <TableCell className="whitespace-nowrap">
                    {format(new Date(record.createdAt), "yyyy-MM-dd HH:mm:ss")}
                  </TableCell>
                  <TableCell className="text-right">
                    <Badge className="uppercase text-xs">
                      {/* TODO: Reflect real status */}
                      SUBMITTED
                    </Badge>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} className="h-24 text-center">
                  No records found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
