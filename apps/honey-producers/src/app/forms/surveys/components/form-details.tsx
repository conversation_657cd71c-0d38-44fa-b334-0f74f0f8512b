"use client"

import React from "react"
import { Pencil } from "lucide-react"
import { CardContent } from "@/components/ui/card"
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { DetailsValue } from "@/components/details-value"
import { FormValues } from "@repo/validation/surveys"
import { paths } from "@repo/api-specs"

interface FormDetailsProps {
  formData:
    | FormValues
    | paths["/forms/compliance-submissions/{id}"]["get"]["responses"]["200"]["content"]["application/json"]
  handleEdit?: (section: number) => void
}

const FormDetails: React.FC<FormDetailsProps> = ({ formData, handleEdit }) => {
  return (
    <CardContent className="max-sm:p-2 space-y-6">
      <Accordion
        type="multiple"
        defaultValue={["general", "beehive", "manager"]}
        className="w-full"
      >
        {/* General Section */}
        <AccordionItem value="general">
          <AccordionTrigger className="group py-2">
            <div className="flex items-center gap-2">
              <span className="h-2 w-2 rounded-full bg-red-500" />
              <span className="font-semibold">General</span>
            </div>
            {handleEdit && (
              <div
                role="button"
                className="ml-auto h-7 w-7 rounded-md hover:bg-accent hover:text-accent-foreground p-1"
                onClick={(e) => {
                  e.stopPropagation()
                  handleEdit(1)
                }}
              >
                <Pencil className="h-4 w-4" />
              </div>
            )}
          </AccordionTrigger>
          <AccordionContent className="space-y-4 pt-4">
            <div className="bg-background border shadow rounded-lg p-6 grid grid-cols-2 gap-4">
              <DetailsValue label="Farmer ID" value={formData.farmer.id} />
              <DetailsValue label="Farmer Name" value={formData.farmer.name} />
            </div>
          </AccordionContent>
        </AccordionItem>

        {/* Beehive Details Section */}
        <AccordionItem value="beehive">
          <AccordionTrigger className="group">
            <div className="flex items-center gap-2">
              <span className="h-2 w-2 rounded-full bg-green-500" />
              <span className="font-semibold">Beehive Details</span>
            </div>
            {handleEdit && (
              <div
                role="button"
                className="ml-auto h-7 w-7 rounded-md hover:bg-accent hover:text-accent-foreground p-1"
                onClick={(e) => {
                  e.stopPropagation()
                  handleEdit(2)
                }}
              >
                <Pencil className="h-4 w-4" />
              </div>
            )}
          </AccordionTrigger>
          <AccordionContent className="space-y-4 pt-2">
            <DetailsValue
              label="How many hives do you have in the field at present?"
              value={formData.beehives.numberPresent}
              className="bg-background border shadow rounded-lg p-4 pr-12 w-full"
            />
            <DetailsValue
              label="Do you visit your hives every month?"
              value={formData.beehives.visitedMonthly}
              className="bg-background border shadow rounded-lg p-4 pr-12 w-full"
            />
            {formData.beehives.visitedMonthly &&
              formData.beehives.visitedRecently.length > 0 && (
                <DetailsValue
                  label="Which hive numbers did you recently visit?"
                  value={formData.beehives.visitedRecently.join(", ")}
                  className="bg-background border shadow rounded-lg p-4 pr-12 w-full"
                />
              )}
            <DetailsValue
              label="Are any of the hives occupied?"
              value={formData.beehives.areAnyOccupied}
              className="bg-background border shadow rounded-lg p-4 pr-12 w-full"
            />
            <DetailsValue
              label="Are the hives at least 6km away from a water source?"
              value={formData.beehives.awayFromWater}
              className="bg-background border shadow rounded-lg p-4 pr-12 w-full"
            />
            <DetailsValue
              label="Are the hives at least 6km away from crops?"
              value={formData.beehives.awayFromCrops}
              className="bg-background border shadow rounded-lg p-4 pr-12 w-full"
            />
            <DetailsValue
              label="Is there adequate flowering close by?"
              value={formData.beehives.adequateFlowering}
              className="bg-background border shadow rounded-lg p-4 pr-12 w-full"
            />
            <DetailsValue
              label="How many hives were ready for harvest?"
              value={formData.beehives.readyForHarvest}
              className="bg-background border shadow rounded-lg p-4 pr-12 w-full"
            />
            <DetailsValue
              label="What was used to rebait the hives?"
              value={
                formData.beehives.rebaitMaterial === "wax_nn"
                  ? "Wax from NN"
                  : "Other"
              }
              className="bg-background border shadow rounded-lg p-4 pr-12 w-full"
            />
            <DetailsValue
              label="Was enough honey left for winter?"
              value={formData.beehives.enoughHoneyForWinter}
              className="bg-background border shadow rounded-lg p-4 pr-12 w-full"
            />
            <DetailsValue
              label="Were any bees mutilated?"
              value={formData.beehives.beesWereMutilated}
              className="bg-background border shadow rounded-lg p-4 pr-12 w-full"
            />
            <DetailsValue
              label="Was organic wax used?"
              value={formData.beehives.organicWaxUsed}
              className="bg-background border shadow rounded-lg p-4 pr-12 w-full"
            />
            <DetailsValue
              label="Do you provide food for bees?"
              value={formData.beehives.provideFoodForBees}
              className="bg-background border shadow rounded-lg p-4 pr-12 w-full"
            />
            <DetailsValue
              label="Are there any infestations?"
              value={formData.beehives.hasInfestation}
              className="bg-background border shadow rounded-lg p-4 pr-12 w-full"
            />
            {formData.beehives.hasInfestation &&
              formData.beehives.infestationDetails && (
                <DetailsValue
                  label="Infestation details"
                  value={formData.beehives.infestationDetails}
                  className="bg-background border shadow rounded-lg p-4 pr-12 w-full"
                />
              )}
          </AccordionContent>
        </AccordionItem>

        {/* Manager Inputs Section */}
        <AccordionItem value="manager">
          <AccordionTrigger className="group">
            <div className="flex items-center gap-2">
              <span className="h-2 w-2 rounded-full bg-yellow-500" />
              <span className="font-semibold">Manager Inputs</span>
            </div>
            {handleEdit && (
              <div
                role="button"
                className="ml-auto h-7 w-7 rounded-md hover:bg-accent hover:text-accent-foreground p-1"
                onClick={(e) => {
                  e.stopPropagation()
                  handleEdit(3)
                }}
              >
                <Pencil className="h-4 w-4" />
              </div>
            )}
          </AccordionTrigger>
          <AccordionContent className="space-y-4 pt-2">
            {formData.managerInputs.additionalComments && (
              <DetailsValue
                label="Additional Comments"
                value={formData.managerInputs.additionalComments}
                className="bg-background border shadow rounded-lg p-4 pr-12 w-full"
              />
            )}
            <DetailsValue
              label="Is the farmer compliant?"
              value={formData.managerInputs.isCompliant}
              className="bg-background border shadow rounded-lg p-4 pr-12 w-full"
            />
            {formData.managerInputs.isCompliant
              ? formData.managerInputs.complianceReason && (
                  <DetailsValue
                    label="Why is the farmer compliant?"
                    value={formData.managerInputs.complianceReason}
                    className="bg-background border shadow rounded-lg p-4 pr-12 w-full"
                  />
                )
              : formData.managerInputs.nonComplianceReason && (
                  <DetailsValue
                    label="Why is the farmer not compliant?"
                    value={formData.managerInputs.nonComplianceReason}
                    className="bg-background border shadow rounded-lg p-4 pr-12 w-full"
                  />
                )}
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </CardContent>
  )
}

export default FormDetails
