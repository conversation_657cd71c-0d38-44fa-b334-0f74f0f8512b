"use client"

import { useState, useEffect } from "react"
import { Input } from "@/components/ui/input"
import { Search, PlusCircle, TriangleAlert } from "lucide-react"
import type { DateRange } from "react-day-picker"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useRouter } from "next/navigation"
import { DatePickerWithRange } from "@/components/date-picker-with-range"
import api from "@/lib/api"
import { Button } from "@/components/ui/button"
import { useOfflineSyncManager } from "@/hooks/useSyncOfflineForms"
import { getAllFromIDB } from "@/lib/idb"
import { OfflineRecordsTable } from "./components/offline-records-table"
import { OnlineRecordsTable } from "./components/online-records-table"
import { useOffline } from "@/hooks/use-offline"

type SortOrder = "newestFirst" | "oldestFirst"

export type RecordType = {
  id: string
  farmerId: string
  beehives: string[]
  saleDate: string
  pricePerKg: number
  honeyType: "light" | "dark"
  buckets: {
    bucketId: string
    harvestDate: string
    moistureContent: number
    weight: number
  }[]
  createdBy: string
  createdAt: string
}

export default function RecordsPage() {
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState("")
  const [dateRange, setDateRange] = useState<DateRange>()
  const [sortOrder, setSortOrder] = useState<SortOrder>("newestFirst")
  const [offlineRecords, setOfflineRecords] = useState<RecordType[]>([])
  const { isOffline } = useOffline()

  const {
    data: records,
    isLoading,
    refetch,
  } = api.useQuery("get", "/honey-harvesting", {
    params: {
      query: {
        query: searchTerm,
        createdAtFrom: dateRange?.from?.toISOString(),
        createdAtTo: dateRange?.to?.toISOString(),
        sortBy: sortOrder,
      },
    },
  })

  const { hasJustSynced, setHasJustSynced } = useOfflineSyncManager()
  useEffect(() => {
    const fetchOfflineData = async () => {
      const localData = await getAllFromIDB<RecordType>("honey-harvesting")
      setOfflineRecords(localData)
    }
    fetchOfflineData()
  }, [hasJustSynced])

  useEffect(() => {
    if (hasJustSynced) {
      refetch()
      setHasJustSynced(false)
    }
  }, [hasJustSynced, refetch, setHasJustSynced])

  return (
    <div className="container max-w-screen-xl mx-auto py-8 space-y-8">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-2xl font-semibold">Existing Records</h1>
        {isOffline ? (
          <p className="flex justify-center gap-2 text-xs text-muted-foreground p-2 bg-yellow-50 rounded-md border border-yellow-200">
            <TriangleAlert />
            You are in offline mode. Search and database Honey Harvesting are
            unavailable. You can only view or create offline Honey Harvesting.
          </p>
        ) : (
          <p className="text-sm text-muted-foreground">
            View and search for any existing record on Honey Harvesting.
          </p>
        )}
      </div>

      {/* Filters */}
      <div className="flex flex-col lg:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search by ID (3 characters or more)"
            className="pl-9"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            disabled={isOffline}
          />
        </div>

        <div className="flex flex-col sm:flex-row gap-4 lg:w-auto">
          <DatePickerWithRange
            date={dateRange}
            onSelect={setDateRange}
            disabled={isOffline}
          />

          <Select
            defaultValue={sortOrder}
            onValueChange={(value: SortOrder) => setSortOrder(value)}
            disabled={isOffline}
          >
            <SelectTrigger className="w-full sm:w-[200px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="newestFirst">Date (Newest first)</SelectItem>
              <SelectItem value="oldestFirst">Date (Oldest first)</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <Button
          variant="default"
          onClick={() => router.push("/forms/honey-harvesting/create")}
        >
          <PlusCircle className="h-2 w-2 mr-2" />
          Add Harvest
        </Button>
      </div>

      {!isOffline ? (
        <OnlineRecordsTable
          isLoading={isLoading}
          records={records?.results ?? []}
        />
      ) : (
        <OfflineRecordsTable
          isLoading={isLoading}
          offlineRecords={offlineRecords}
        />
      )}
    </div>
  )
}
