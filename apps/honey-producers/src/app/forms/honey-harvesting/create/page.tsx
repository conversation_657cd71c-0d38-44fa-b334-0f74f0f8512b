"use client"
import { useState } from "react"
import { Sourcing, ConfirmStep, CompleteStep } from "./steps/"
import { Step, Stepper } from "@/components/form/stepper"
import { useRouter } from "next/navigation"
import { useForm, type SubmitHand<PERSON>, FormProvider } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { formSchema, type FormValues } from "@repo/validation/honey-harvesting"
import api from "@/lib/api"
import { saveToIDB } from "@/lib/idb"
import { useMutation } from "@tanstack/react-query"
import { CompleteStepFormData } from "../../honey-harvesting/create/steps/complete"
import { authClient } from "@/lib/auth-client"
import { useAuth } from "@/app/providers/auth"
import { useOffline } from "@/hooks/use-offline"
import { generateUUID } from "@/lib/utils"

type Inputs = FormValues

const HoneyHarvestingPage = () => {
  const router = useRouter()
  const [step, setStep] = useState(1)
  const [formData, setFormData] = useState<CompleteStepFormData>()
  const { data: onlineSession } = authClient.useSession()
  const { session: offlineSession } = useAuth()
  const [isLoading, setIsLoading] = useState(false)
  const { isOffline } = useOffline()

  const userId = !isOffline ? onlineSession?.user.id : offlineSession.user.id

  const methods = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      beehives: [],
      saleDate: undefined,
      pricePerKg: undefined,
      honeyType: "light",
    },
  }) // Centralizing useForm

  const mutation = api.useMutation("post", "/honey-harvesting", {
    onSuccess: (response) => {
      const buckets = methods.getValues("buckets")
      const pricePerKg = methods.getValues("pricePerKg")

      const totalWeight = buckets.reduce(
        (sum, bucket) => sum + Number(bucket.weight),
        0
      )

      const totalAmount = totalWeight * pricePerKg

      setFormData({
        transactionId: response.id || "",
        totalWeight,
        totalAmount,
        totalBuckets: buckets.length,
      })
      methods.reset()
      setStep(step + 1)

      setIsLoading(false)
    },
    onError: (e) => {
      console.log(e.message)

      setIsLoading(false)
    },
  })

  const offlineMutation = useMutation({
    mutationFn: async (payload: any) => {
      const harvestingId = generateUUID()
      const offlinePayload = {
        id: harvestingId,
        ...payload,
      }

      await saveToIDB("honey-harvesting", offlinePayload)
      return offlinePayload
    },
    onSuccess: (offlinePayload) => {
      const harvestingId = offlinePayload.id
      const buckets = methods.getValues("buckets")
      const pricePerKg = methods.getValues("pricePerKg")

      const totalWeight = buckets.reduce(
        (sum, bucket) => sum + Number(bucket.weight),
        0
      )

      const totalAmount = totalWeight * pricePerKg

      setFormData({
        transactionId: harvestingId,
        totalWeight,
        totalAmount,
        totalBuckets: buckets.length,
      })
      methods.reset()
      setStep(step + 1)

      setIsLoading(false)
    },
    onError: (error) => {
      console.error("Local save error:", error)
      setIsLoading(false)
    },
  })

  const stepComponents = [Sourcing, ConfirmStep, CompleteStep]

  const stepSections = [
    { id: 1, label: "Sourcing", range: [1, 1] },
    { id: 2, label: "Review", range: [2, 2] },
    { id: 3, label: "Finish", range: [3, 3] },
  ]

  const stepperState: Step[] = stepSections.map(({ id, label, range }) => {
    const [start, end] = range
    return {
      id,
      label,
      status:
        step > end
          ? "completed"
          : step >= start && step <= end
            ? "current"
            : "upcoming",
    }
  })

  const moveToNextStep = async () => {
    // Trigger validation before moving to the next step
    const isValid = await methods.trigger([
      "regionId",
      "chiefdomId",
      "zoneId",
      "villageId",
      "farmer",
      "beehives",
      "saleDate",
      "pricePerKg",
      "honeyType",
      "buckets",
    ])

    if (!isValid) return

    methods.resetField("tempInput")

    setIsLoading(true)

    setStep(step + 1)

    setIsLoading(false)
  }

  const onSubmit: SubmitHandler<Inputs> = (data) => {
    setIsLoading(true)
    const payload = {
      farmerId: data.farmer.id,
      beehives: data.beehives,
      saleDate: data.saleDate.toISOString(),
      pricePerKg: data.pricePerKg,
      buckets: data.buckets.map((bucket) => ({
        ...bucket,
        harvestDate: bucket.harvestDate.toISOString(),
        honeyType: data.honeyType,
      })),
      createdBy: userId,
      regionId: data.regionId,
      chiefdomId: data.chiefdomId,
      zoneId: data.zoneId,
      villageId: data.villageId,
    }

    if (!isOffline) {
      mutation.mutate({ body: payload })
    } else {
      offlineMutation.mutate(payload)
    }
  }

  const handleEdit = (sectionId: number) => {
    const stepId = stepSections.find((section) => section.id === sectionId)
      ?.range[0]

    if (!stepId) return

    setStep(stepId)
  }

  const moveToPreviousStep = () =>
    step === 1 ? router.push("/forms/honey-harvesting") : setStep(step - 1)

  const renderStep = () => {
    const CurrentStepComponent = stepComponents[step - 1]

    return (
      <CurrentStepComponent
        handleNext={moveToNextStep}
        handlePrevious={moveToPreviousStep}
        isLast={step === stepComponents.length - 1}
        formPath={
          formData && `/forms/honey-harvesting/form#${formData.transactionId}`
        }
        handleEdit={handleEdit}
        formData={formData}
        isLoading={isLoading}
      />
    )
  }

  return (
    <FormProvider {...methods}>
      <div className="flex items-center flex-col md:mx-4">
        <Stepper
          steps={stepperState}
          progress={(step / stepComponents.length) * 100}
          onBack={moveToPreviousStep}
          backDisabled={!!formData}
        />
        <div className="w-full max-w-[800px]">
          <form onSubmit={methods.handleSubmit(onSubmit)}>{renderStep()}</form>
        </div>
      </div>
    </FormProvider>
  )
}

export default HoneyHarvestingPage
