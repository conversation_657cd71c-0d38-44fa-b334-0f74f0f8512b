"use client"

import { type FC, use<PERSON><PERSON>back, useState } from "react"
import { NextButton } from "@/components/form/next-button"
import { useFormContext } from "react-hook-form"
import { Calendar, X } from "lucide-react"
import { format, isAfter } from "date-fns"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { cn } from "@/lib/utils"
import type { FormValues, Bucket } from "@repo/validation/honey-harvesting"
import { FarmerSearch } from "@/components/form/farmer-search"
import { BeehiveSelect } from "@/components/form/beehive-select"
import { LocationSelectorOnline } from "@/components/form/location-select-online"
import { LocationSelectorOffline } from "@/components/form/location-select-offline"
import { FarmerSearchOffline } from "@/components/form/farmer-search-offline"
import { BeehiveSelectOffline } from "@/components/form/beehive-select-offline"
import { useOffline } from "@/hooks/use-offline"

interface Props {
  handleNext: () => void
  isLast: boolean
  formPath?: string
  isLoading: boolean
}

export const Sourcing: FC<Props> = ({
  handleNext,
  isLast,
  formPath,
  isLoading,
}) => {
  const {
    control,
    getValues,
    setValue,
    watch,
    trigger,
    reset,
    resetField,
    formState: { errors },
  } = useFormContext<FormValues>()
  const buckets = watch("buckets") || []
  const [bucketIdError, setBucketIdError] = useState<string | null>(null)
  const farmerId = watch("farmer.id")
  const { isOffline } = useOffline()

  const addBucket = useCallback(async () => {
    const isBucketValid = await trigger([
      "tempInput.bucketId",
      "tempInput.harvestDate",
      "tempInput.moistureContent",
      "tempInput.weight",
    ])

    const newBucket: Bucket = {
      bucketId: getValues("tempInput.bucketId") || "",
      harvestDate: getValues("tempInput.harvestDate") || new Date(),
      moistureContent: getValues("tempInput.moistureContent") || 0,
      weight: getValues("tempInput.weight") || 0,
    }

    //Check if the bucket ID already exists
    if (buckets.some((bucket) => bucket.bucketId === newBucket.bucketId)) {
      setBucketIdError("A Bucket already have this ID.")
      return
    }

    if (isBucketValid) {
      const updatedBuckets = [...buckets, newBucket]
      setValue("buckets", updatedBuckets)

      resetField("tempInput.bucketId")
      resetField("tempInput.harvestDate")
      resetField("tempInput.moistureContent")
      resetField("tempInput.weight")
      setBucketIdError(null)
    }

    if (!isBucketValid) {
      return
    }
  }, [buckets, getValues, setValue, trigger, reset])

  const deleteBucket = (bucketId: string) => {
    const updatedBuckets = buckets.filter(
      (bucket) => bucket.bucketId !== bucketId
    )
    setValue("buckets", updatedBuckets)
  }

  const calculateTotals = () => {
    const totalWeight = buckets.reduce(
      (sum, bucket) => sum + Number(bucket.weight),
      0
    )
    const totalAmount = totalWeight * (getValues("pricePerKg") || 0)
    return { totalWeight, totalAmount }
  }

  const { totalWeight, totalAmount } = calculateTotals()

  return (
    <div className="space-y-6">
      <div className="bg-primary-foreground border shadow rounded-lg p-6 space-y-6">
        <h1 className="font-semibold">Input your records</h1>
        <div className="space-y-4">
          {/*Farmer*/}
          <FormField
            control={control}
            name="farmer"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Search by farmer ID or name</FormLabel>
                {!isOffline ? (
                  <FarmerSearch
                    selectedFarmerId={field.value?.id}
                    onSelect={(data) => {
                      field.onChange({
                        id: data.id,
                        name: `${data.firstName} ${data.lastName}`,
                      })
                      // Pre-fill location fields if farmer has location data
                      if (data.regionId) setValue("regionId", data.regionId)
                      if (data.chiefdomId)
                        setValue("chiefdomId", data.chiefdomId)
                      if (data.zoneId) setValue("zoneId", data.zoneId)
                      if (data.villageId) setValue("villageId", data.villageId)
                    }}
                  />
                ) : (
                  <FarmerSearchOffline
                    selectedFarmerId={field.value?.id}
                    onSelect={(data) => {
                      field.onChange({
                        id: data.id,
                        name: `${data.firstName} ${data.lastName}`,
                      })
                      // Pre-fill location fields if farmer has location data
                      if (data.regionId) setValue("regionId", data.regionId)
                      if (data.chiefdomId)
                        setValue("chiefdomId", data.chiefdomId)
                      if (data.zoneId) setValue("zoneId", data.zoneId)
                      if (data.villageId) setValue("villageId", data.villageId)
                    }}
                  />
                )}
                <FormMessage />
              </FormItem>
            )}
          />

          {/*Beehives - Only show when farmer is selected*/}
          {farmerId && (
            <FormField
              control={control}
              name="beehives"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Beehives</FormLabel>
                  <FormControl>
                    {!isOffline ? (
                      <BeehiveSelect
                        farmerId={farmerId}
                        onChange={field.onChange}
                        defaultValue={field.value}
                      />
                    ) : (
                      <BeehiveSelectOffline
                        farmerId={farmerId}
                        onChange={field.onChange}
                        defaultValue={field.value || []}
                      />
                    )}
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          {/*Location - Only show when farmer is selected*/}
          {farmerId && (
            <div>
              {!isOffline ? (
                <LocationSelectorOnline />
              ) : (
                <LocationSelectorOffline />
              )}
            </div>
          )}

          {/*Sale Date*/}
          <FormField
            control={control}
            name="saleDate"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Sale Date</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value ? (
                          format(field.value, "PPP")
                        ) : (
                          <span>Select Date</span>
                        )}
                        <Calendar className="ml-auto h-4 w-4 text-primary" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <CalendarComponent
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      initialFocus
                      disabled={(date) => isAfter(date, new Date())}
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />

          {/*Price per KG*/}
          <FormField
            control={control}
            name="pricePerKg"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Price per KG</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    step="0.01"
                    placeholder="ex: 100"
                    className="bg-background"
                    {...field}
                    value={field.value ?? ""}
                    onChange={(e) => {
                      const value =
                        e.target.value === ""
                          ? undefined
                          : Number(e.target.value)
                      field.onChange(value)
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/*Honey Type*/}
          <FormField
            control={control}
            name="honeyType"
            render={({ field }) => (
              <FormItem className="space-y-2">
                <FormLabel>Honey type</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className="flex gap-2"
                  >
                    <div
                      className={cn(
                        "flex items-center justify-center bg-white border shadow rounded-md px-4 py-2 gap-2 cursor-pointer",
                        field.value === "light"
                      )}
                    >
                      <RadioGroupItem value="light" id="light" />
                      <FormLabel htmlFor="light" className="cursor-pointer">
                        Light
                      </FormLabel>
                    </div>
                    <div
                      className={cn(
                        "flex items-center justify-center bg-white border shadow rounded-md px-4 py-2 gap-2 cursor-pointer",
                        field.value === "dark"
                      )}
                    >
                      <RadioGroupItem value="dark" id="dark" />
                      <FormLabel htmlFor="dark" className="cursor-pointer">
                        Dark
                      </FormLabel>
                    </div>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/*Add a Bucket*/}
          <div className="grid max-sm:grid-cols-1 grid-cols-2 pt-4 gap-4 border-t border-border">
            {/*Bucket ID*/}
            <FormField
              control={control}
              name="tempInput.bucketId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Bucket ID</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="ex: 1"
                      className="bg-background"
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormMessage />
                  {bucketIdError && (
                    <p className="text-sm font-medium text-destructive">
                      {bucketIdError}
                    </p>
                  )}
                </FormItem>
              )}
            />

            {/*Harvest Date*/}
            <FormField
              control={control}
              name="tempInput.harvestDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Harvest Date</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            format(field.value, "PPP")
                          ) : (
                            <span>Select Date</span>
                          )}
                          <Calendar className="ml-auto h-4 w-4 text-primary" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <CalendarComponent
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        initialFocus
                        disabled={(date) => isAfter(date, new Date())}
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/*Moisture Content*/}
            <FormField
              control={control}
              name="tempInput.moistureContent"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Moisture Content</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      placeholder="ex: 10%"
                      className="bg-background"
                      {...field}
                      onChange={(e) => {
                        const value =
                          e.target.value === ""
                            ? undefined
                            : Number(e.target.value)
                        field.onChange(value)
                      }}
                      value={field.value !== undefined ? field.value : ""} // Assure-toi de définir '' si undefined
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/*Weight*/}
            <FormField
              control={control}
              name="tempInput.weight"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Weight</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      placeholder="ex: 10Kg"
                      className="bg-background"
                      {...field}
                      onChange={(e) => {
                        const value =
                          e.target.value === ""
                            ? undefined
                            : Number(e.target.value)
                        field.onChange(value)
                      }}
                      value={field.value !== undefined ? field.value : ""} // Assure-toi de définir '' si undefined
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {errors.buckets && (
              <span className="text-red-500 text-sm">
                {errors.buckets.message}
              </span>
            )}
          </div>
          <Button
            type="button"
            onClick={addBucket}
            className="w-full bg-background border shadow-sm hover:bg-primary hover:text-primary-foreground"
            variant="ghost"
          >
            Add a Bucket
          </Button>
        </div>

        {/* List of Buckets Added */}
        {buckets.length > 0 && (
          <div className="space-y-4">
            {buckets.map((bucket) => (
              <div
                key={bucket.bucketId}
                className="relative bg-background shadow rounded-lg p-4 pr-12 w-full"
              >
                <div className="grid max-sm:grid-cols-2 grid-cols-[auto_auto_auto_auto] gap-2 text-center items-center">
                  <div>
                    <div className="text-sm text-gray-400">ID</div>
                    <div className="">#{bucket.bucketId}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-400">Harvest Date</div>
                    <div className="">{format(bucket.harvestDate, "PPP")}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-400">
                      Moisture Content
                    </div>
                    <div className="">{`${bucket.moistureContent}%`}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-400">Weight</div>
                    <div className="">{`${bucket.weight} KG`}</div>
                  </div>
                </div>

                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="absolute max-sm:top-16 right-2 top-6 hover:bg-primary-foreground"
                  onClick={() => deleteBucket(bucket.bucketId)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        )}

        {/*Totals*/}
        <div className="grid grid-cols-2 gap-4 pt-4 border-t border-border">
          <div>
            <div className="text-sm text-muted-foreground">TOTAL WEIGHT</div>
            <div className="text-lg font-semibold">{totalWeight} KGs</div>
          </div>
          <div>
            <div className="text-sm text-muted-foreground">
              TOTAL AMOUNT PAID
            </div>
            <div className="text-lg font-semibold">
              ${totalAmount.toFixed(2)} ZD
            </div>
          </div>
        </div>
      </div>

      <NextButton
        isLoading={isLoading}
        isLast={isLast}
        handleNext={handleNext}
        formPath={formPath}
      />
    </div>
  )
}
