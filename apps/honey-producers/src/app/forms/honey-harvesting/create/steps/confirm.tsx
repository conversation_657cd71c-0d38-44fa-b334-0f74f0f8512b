"use client"

import type { <PERSON> } from "react"
import { useFormContext } from "react-hook-form"
import type { FormValues } from "@repo/validation/honey-harvesting"
import { Card, CardHeader, CardTitle } from "@/components/ui/card"
import { NextButton } from "@/components/form/next-button"
import FormDetails from "../../components/form-details"

interface Props {
  handleNext: () => void
  isLast: boolean
  formPath?: string
  handleEdit: (sectionId: number) => void
  isLoading?: boolean
}

export const ConfirmStep: FC<Props> = ({
  handleNext,
  isLast,
  formPath,
  handleEdit,
  isLoading,
}) => {
  const { getValues } = useFormContext<FormValues>()
  const formData = getValues()

  return (
    <div className="space-y-4">
      <Card className="bg-primary-foreground border">
        <CardHeader>
          <CardTitle>Confirm your data</CardTitle>
        </CardHeader>
        <FormDetails formData={formData} handleEdit={handleEdit} />
      </Card>

      <NextButton
        isLoading={isLoading}
        isLast={isLast}
        handleNext={handleNext}
        formPath={formPath}
      />
    </div>
  )
}
