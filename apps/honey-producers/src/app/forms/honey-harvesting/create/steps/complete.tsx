"use client"

import type { FC } from "react"
import { Card, CardContent, CardTitle } from "@/components/ui/card"
import { NextButton } from "@/components/form/next-button"
import {
  Weight,
  Banknote,
  Box,
  CheckCircle,
  WifiOff,
  Wifi,
  AlertTriangle,
} from "lucide-react"
import { format } from "date-fns"
import { useOffline } from "@/hooks/use-offline"

export interface CompleteStepFormData {
  transactionId: string
  totalWeight: number
  totalAmount: number
  totalBuckets: number
}

interface Props {
  handleNext: () => void
  handlePrevious: () => void
  isLast: boolean
  formPath?: string
  formData?: CompleteStepFormData
  isLoading: boolean
}

export const CompleteStep: FC<Props> = ({
  handleNext,
  isLast,
  formPath,
  formData,
  isLoading,
}) => {
  const { isOffline } = useOffline()
  return (
    <div className="w-full mx-auto flex flex-col gap-6 items-center justify-center min-h-[calc(100vh-14rem)]">
      <Card className="w-full mx-auto bg-primary-foreground">
        <CardContent className="text-center">
          <div className="flex flex-col items-center justify-center pt-6 mb-2">
            <div className="relative w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <CheckCircle className="w-10 h-10 text-green-600" />
              <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-white rounded-full flex items-center justify-center border-2 border-green-100">
                {isOffline ? (
                  <WifiOff className="w-3 h-3 text-orange-500" />
                ) : (
                  <Wifi className="w-3 h-3 text-green-500" />
                )}
              </div>
            </div>

            <CardTitle className="text-2xl font-semibold mb-2">
              {isOffline
                ? "Harvest saved locally !"
                : "Harvest added successfully !"}
            </CardTitle>
            <p className="text-gray-500 mb-6">
              A Transaction has been added to the system on{" "}
              {format(new Date(), "do MMM yyyy")} as Honey Harvesting.
            </p>

            {isOffline ? (
              <div className="bg-orange-50 border border-yellow-200 rounded-lg p-4 w-full">
                <div className="flex justify-center items-center gap-2 mb-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-500" />
                  <h3 className="font-bold text-yellow-800">Offline mode</h3>
                </div>
                <p className="text-sm text-yellow-700">
                  Your data is stored locally and will automatically sync when
                  you're back online.
                </p>
              </div>
            ) : (
              <div className="bg-blue-500/10 rounded-lg p-4 w-full">
                <h3 className="font-medium text-blue-800 mb-2">
                  Harvest ID:{" "}
                  <span className="font-bold">#{formData?.transactionId}</span>
                </h3>
                <p className="text-sm text-blue-700">
                  This ID can be used to identify the honey harvesting in the
                  system.
                </p>
              </div>
            )}
          </div>

          <div className="p-4 max-x-md justify-center">
            <h2 className="text-left text-xl font-bold mb-6">
              Honey Harvesting
            </h2>
            {/* Farmer Details */}
            <div className="space-y-3">
              <div className="grid grid-cols-3 gap-4">
                <div className="flex items-center gap-2">
                  <Weight className="h-5 w-5 text-muted-foreground" />
                  <div className="space-y-1">
                    <p className="text-muted-foreground text-sm">
                      Total Weight
                    </p>
                    <p className="font-medium">
                      {formData?.totalWeight.toFixed(2)} KGs
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Banknote className="h-5 w-5 text-muted-foreground" />
                  <div className="space-y-1">
                    <p className="text-muted-foreground text-sm">
                      Total Amount Paid
                    </p>
                    <p className="font-medium">
                      {formData?.totalAmount.toFixed(2)} ZD
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Box className="h-5 w-5 text-muted-foreground" />
                  <div className="space-y-1">
                    <p className="text-muted-foreground text-sm">
                      Total Number of Buckets
                    </p>
                    <p className="font-medium">
                      {formData?.totalBuckets} Buckets
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="w-full">
        <NextButton
          isLast={isLast}
          handleNext={handleNext}
          formPath={formPath}
          isLoading={isLoading}
        />
      </div>
    </div>
  )
}
