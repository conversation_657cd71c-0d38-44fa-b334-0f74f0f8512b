"use client"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Loader } from "lucide-react"
import { format } from "date-fns"
import { capitalize } from "@/lib/utils"
import { useRouter } from "next/navigation"
import { paths } from "@repo/api-specs"

type Props = {
  isLoading: boolean
  records: paths["/honey-harvesting"]["get"]["responses"]["200"]["content"]["application/json"]["results"]
}

export const OnlineRecordsTable = ({ isLoading, records }: Props) => {
  const router = useRouter()

  return (
    <div className="border shadow rounded-lg">
      <Table>
        <TableHeader className="bg-primary-foreground">
          <TableRow>
            <TableHead>Farmer Name</TableHead>
            <TableHead>Location</TableHead>
            <TableHead>Date</TableHead>
            <TableHead>Moisture</TableHead>
            <TableHead>Buckets</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Total Weight</TableHead>
            <TableHead>Price per KGs</TableHead>
            <TableHead>Amount Paid</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {isLoading ? (
            <TableRow>
              <TableCell colSpan={9} className="text-center h-24">
                <div className="flex justify-center items-center">
                  <Loader className="h-6 w-6 animate-spin text-primary" />
                  <span className="ml-2">Loading records...</span>
                </div>
              </TableCell>
            </TableRow>
          ) : records && records.length > 0 ? (
            records.map((record) => (
              <TableRow
                key={record.id}
                className="cursor-pointer hover:bg-muted/50"
                onClick={() =>
                  router.push(`/forms/honey-harvesting/form#${record.id}`)
                }
              >
                <TableCell className="font-mono">{record.farmerName}</TableCell>
                <TableCell>
                  {record.region}, {record.zone}
                </TableCell>
                <TableCell>
                  {format(new Date(record.createdAt), "yyyy-MM-dd HH:mm:ss")}
                </TableCell>
                <TableCell>{record.moistureRange}</TableCell>
                <TableCell>{record.bucketCount}</TableCell>
                <TableCell>{capitalize(record.honeyType)}</TableCell>
                <TableCell>{record.totalWeight}</TableCell>
                <TableCell>{record.pricePerKg}</TableCell>
                <TableCell>{record.amountPaid}</TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={9} className="text-center h-24">
                No records found
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  )
}
