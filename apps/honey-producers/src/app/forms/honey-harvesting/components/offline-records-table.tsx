"use client"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Loader } from "lucide-react"
import { format } from "date-fns"
import type { RecordType } from "../page"
import { useRouter } from "next/navigation"

type Props = {
  isLoading: boolean
  offlineRecords: RecordType[]
}

export const OfflineRecordsTable = ({ isLoading, offlineRecords }: Props) => {
  const router = useRouter()
  return (
    <div className="border shadow rounded-lg">
      <Table>
        <TableHeader className="bg-primary-foreground">
          <TableRow>
            <TableHead>Farmer ID</TableHead>
            <TableHead>Date</TableHead>
            <TableHead>Buckets</TableHead>
            <TableHead>Price per KGs</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {isLoading ? (
            <TableRow>
              <TableCell colSpan={6} className="text-center h-24">
                <div className="flex justify-center items-center">
                  <Loader className="h-6 w-6 animate-spin text-primary" />
                  <span className="ml-2">Loading records...</span>
                </div>
              </TableCell>
            </TableRow>
          ) : offlineRecords.length > 0 ? (
            offlineRecords.map((record) => (
              <TableRow
                key={record.id}
                className="cursor-pointer hover:bg-muted/50"
                onClick={() =>
                  router.push(`/forms/honey-harvesting/form#${record.id}`)
                }
              >
                <TableCell>{record.farmerId}</TableCell>
                <TableCell>
                  {format(new Date(record.createdAt), "yyyy-MM-dd HH:mm:ss")}
                </TableCell>
                <TableCell>{record.buckets.length}</TableCell>
                <TableCell>{record.pricePerKg}</TableCell>
                <TableCell />
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={9} className="text-center h-24">
                No records found
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  )
}
