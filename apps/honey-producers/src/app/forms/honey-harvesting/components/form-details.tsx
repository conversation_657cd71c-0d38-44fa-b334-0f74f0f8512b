"use client"

import React, { useState } from "react"
import { <PERSON>, Co<PERSON>, ExternalLink, Pencil } from "lucide-react"
import { CardContent } from "@/components/ui/card"
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { DetailsValue } from "@/components/details-value"
import { FormValues } from "@repo/validation/honey-harvesting"
import { format } from "date-fns"
import { paths } from "@repo/api-specs"
import {
  TooltipProvider,
  Tooltip,
  TooltipTrigger,
} from "@radix-ui/react-tooltip"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

interface FormDetailsProps {
  formData:
    | FormValues
    | paths["/honey-harvesting/{id}"]["get"]["responses"]["200"]["content"]["application/json"]
  handleEdit?: (section: number) => void
}

const FormDetails: React.FC<FormDetailsProps> = ({ formData, handleEdit }) => {
  const [copied, setCopied] = useState(false)

  const copyHarvestingTransactionCode = async () => {
    try {
      await navigator.clipboard.writeText(formData.palmyraHash ?? "")
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error("Failed to copy transaction code:", error)
    }
  }

  return (
    <CardContent className="max-sm:p-2 space-y-6">
      <Accordion
        type="multiple"
        defaultValue={["general", "buckets"]}
        className="w-full"
      >
        {/* General Section */}
        <AccordionItem value="general">
          <AccordionTrigger className="group py-2">
            <div className="flex items-center gap-2">
              <span className="h-2 w-2 rounded-full bg-red-500" />
              <span className="font-semibold">General</span>
            </div>
            {handleEdit && (
              <div
                role="button"
                className="ml-auto h-7 w-7 rounded-md hover:bg-accent hover:text-accent-foreground p-1"
                onClick={(e) => {
                  e.stopPropagation()
                  handleEdit(1)
                }}
              >
                <Pencil className="h-4 w-4" />
              </div>
            )}
          </AccordionTrigger>
          <AccordionContent className="space-y-4 pt-4">
            <div>
              <h3 className="text-sm text-gray-500 mb-2">BLOCKCHAIN RECORD</h3>

              <div className="flex max-sm:flex-col gap-2">
                <div className="relative w-full">
                  <Input
                    value={
                      !formData?.palmyraHash
                        ? "Hash will be generated within 24 hours..."
                        : formData?.palmyraHash
                    }
                    readOnly
                    className={`${
                      !formData?.palmyraHash ? "italic text-gray-400 " : ""
                    }pr-10 text-sm text-gray-600 font-mono bg-white`}
                  />
                  <TooltipProvider>
                    <Tooltip open={copied}>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3 py-2 text-gray-500 hover:text-gray-700"
                          onClick={copyHarvestingTransactionCode}
                          disabled={!formData.palmyraHash}
                        >
                          {copied ? (
                            <Check className="h-4 w-4 text-green-500" />
                          ) : (
                            <Copy className="h-4 w-4" />
                          )}
                        </Button>
                      </TooltipTrigger>
                    </Tooltip>
                  </TooltipProvider>
                </div>

                <Button
                  disabled={!formData.palmyraHash}
                  variant="default"
                  onClick={() =>
                    window.open(
                      `https://preview.cardanoscan.io/transaction/${formData.palmyraHash}`,
                      "_blank"
                    )
                  }
                >
                  <ExternalLink className="h-4 w-4" />
                  View
                </Button>
              </div>
              <h3 className="text-sm text-gray-500 mt-3 mb-1">IPFS</h3>
              <div className="flex max-sm:flex-col gap-2">
                <div className="relative w-full">
                  <Input
                    value={
                      !formData?.ipfsHash
                        ? "Hash will be generated within 24 hours..."
                        : formData?.ipfsHash
                    }
                    readOnly
                    className={`${
                      !formData?.ipfsHash ? "italic text-gray-400 " : ""
                    }pr-10 text-sm text-gray-600 font-mono bg-white`}
                  />
                  <TooltipProvider>
                    <Tooltip open={copied}>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3 py-2 text-gray-500 hover:text-gray-700"
                          onClick={copyHarvestingTransactionCode}
                          disabled={!formData.ipfsHash}
                        >
                          {copied ? (
                            <Check className="h-4 w-4 text-green-500" />
                          ) : (
                            <Copy className="h-4 w-4" />
                          )}
                        </Button>
                      </TooltipTrigger>
                    </Tooltip>
                  </TooltipProvider>
                </div>

                <Button
                  disabled={!formData.ipfsHash}
                  variant="default"
                  onClick={() =>
                    window.open(
                      `https://ipfsviewer.com/?hash=${formData.ipfsHash}`,
                      "_blank"
                    )
                  }
                >
                  <ExternalLink className="h-4 w-4" />
                  View
                </Button>
              </div>
            </div>

            <div className="bg-background border shadow rounded-lg p-6 grid grid-cols-2 gap-4">
              <DetailsValue label="Region ID" value={formData.regionId} />
              <DetailsValue label="Chiefdom ID" value={formData.chiefdomId} />
              <DetailsValue label="Zone ID" value={formData.zoneId} />
              <DetailsValue label="Village ID" value={formData.villageId} />
              <DetailsValue
                label="Business or Small Holder"
                // TODO: Implementer dynamiquement
                value="Natures Nectar"
              />
              <DetailsValue label="Farmer Name" value={formData.farmer?.name} />
              <DetailsValue
                label="Date"
                value={
                  formData.saleDate
                    ? format(formData.saleDate, "d-MMM-yyyy")
                    : ""
                }
              />
              <DetailsValue
                label="Beehives"
                value={String(formData.beehives)}
              />
            </div>
          </AccordionContent>
        </AccordionItem>

        {/* Buckets Section */}
        <AccordionItem value="buckets">
          <AccordionTrigger className="group">
            <div className="flex items-center gap-2">
              <span className="h-2 w-2 rounded-full bg-green-500" />
              <span className="font-semibold">Buckets</span>
            </div>
            {handleEdit && (
              <div
                role="button"
                className="ml-auto h-7 w-7 rounded-md hover:bg-accent hover:text-accent-foreground p-1"
                onClick={(e) => {
                  e.stopPropagation()
                  handleEdit(1)
                }}
              >
                <Pencil className="h-4 w-4" />
              </div>
            )}
          </AccordionTrigger>
          <AccordionContent>
            <div className="space-y-4 pt-2">
              {formData.buckets?.map((bucket) => (
                <div
                  key={bucket.bucketId}
                  className="relative bg-background border shadow rounded-lg p-4 w-full"
                >
                  <div className="grid max-sm:grid-cols-2 grid-cols-[auto_auto_auto_auto] gap-2 text-center items-center">
                    <DetailsValue label="ID" value={`#${bucket.bucketId}`} />
                    <DetailsValue
                      label="Harvest Date"
                      value={format(bucket.harvestDate, "PPP")}
                    />
                    <DetailsValue
                      label="Moisture Content"
                      value={`${bucket.moistureContent}%`}
                    />
                    <DetailsValue
                      label="Weight"
                      value={`${bucket.weight} KG`}
                    />
                  </div>
                </div>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </CardContent>
  )
}

export default FormDetails
