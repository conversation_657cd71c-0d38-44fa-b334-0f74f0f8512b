"use client"

import { useEffect, useState } from "react"
import { FormDetailsContainer } from "./form-details-container"

export default function HoneyHarvestingPage() {
  const [formId, setFormId] = useState<string | null>(null)

  useEffect(() => {
    const updateFormId = () => {
      const hash = window.location.hash.replace("#", "")
      setFormId(hash || null)
    }

    updateFormId()
    window.addEventListener("hashchange", updateFormId)
    return () => window.removeEventListener("hashchange", updateFormId)
  }, [])

  if (!formId) {
    return <div>Loading...</div>
  }

  return (
    <div className="container bg-primary-foreground border shadow rounded-lg mx-auto max-w-screen-xl pt-6 pb-2 space-y-8">
      <FormDetailsContainer formId={formId} />
    </div>
  )
}
