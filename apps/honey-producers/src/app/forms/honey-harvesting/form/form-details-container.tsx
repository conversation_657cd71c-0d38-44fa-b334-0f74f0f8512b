"use client"
import React from "react"
import { useState, useEffect } from "react"
import FormDetails from "../components/form-details"
import api from "@/lib/api"
import { Loader, ArrowLeft } from "lucide-react"
import { useRouter } from "next/navigation"
import { format } from "date-fns"
import { Button } from "@/components/ui/button"
import { CardContent, CardHeader } from "@/components/ui/card"
import { FormValues } from "@repo/validation/honey-harvesting"
import { useOffline } from "@/hooks/use-offline"
import { getAllFromIDB } from "@/lib/idb"

interface Props {
  formId: string
}

export const FormDetailsContainer: React.FC<Props> = ({ formId }) => {
  const router = useRouter()
  const [harvestOfflineData, setHarvestOfflineData] =
    useState<FormValues | null>()
  const { isOffline } = useOffline()

  useEffect(() => {
    if (!formId) {
      router.push("/404")
    }
  }, [formId, router])

  if (!formId) {
    return null
  }

  const { data: honeyHarvestingData, isLoading } = api.useQuery(
    "get",
    "/honey-harvesting/{id}",
    {
      params: {
        path: {
          id: formId,
        },
      },
    }
  )

  useEffect(() => {
    const fetchOfflineData = async () => {
      try {
        const localData = await getAllFromIDB<FormValues>("honey-harvesting")
        const harvestOffline = localData.find((f: any) => f.id === formId)
        setHarvestOfflineData(harvestOffline)
      } catch (error) {
        console.error("Failed to load offline farmer data:", error)
        setHarvestOfflineData(null)
      }
    }
    fetchOfflineData()
  }, [formId])

  if (isLoading) {
    return (
      <div className="flex justify-center items-center">
        <Loader className="h-6 w-6 animate-spin text-primary" />
        <span className="ml-2">Loading honey harvesting data...</span>
      </div>
    )
  }

  if (!honeyHarvestingData && !harvestOfflineData) {
    return (
      <div className="flex items-center justify-center">
        <CardContent className="pt-6 flex flex-col items-center text-center space-y-4">
          <p className="text-red-500 font-medium">
            An unknown error occurred while fetching the honey harvesting data.
          </p>
          <Button
            className="w-full"
            onClick={() => router.push(`/forms/honey-harvesting`)}
          >
            Back to Honey Harvesting List
          </Button>
        </CardContent>
      </div>
    )
  }

  return (
    <div>
      <CardHeader className="space-y-2">
        {/* Header */}
        <Button
          variant="outline"
          size="sm"
          className="font-semibold w-fit"
          onClick={() => router.push("/forms/honey-harvesting")}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        {isOffline ? (
          <div className="space-y-6">
            <h1 className="text-2xl font-semibold text-center">
              Honey Harvesting
            </h1>
            <div className="flex max-sm:flex-col max-sm:gap-1 justify-between text-sm text-muted-foreground px-6">
              <p>By: {harvestOfflineData?.createdBy}</p>
              <p>
                {harvestOfflineData?.createdAt
                  ? format(
                      new Date(harvestOfflineData?.createdAt),
                      "yyyy-MM-dd HH:mm:ss"
                    )
                  : ""}
              </p>
              <p>ID: {formId}</p>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            <h1 className="text-2xl font-semibold text-center">
              Honey Harvesting
            </h1>
            <div className="flex max-sm:flex-col max-sm:gap-1 justify-between text-sm text-muted-foreground px-6">
              <p>By: {honeyHarvestingData?.createdBy?.name}</p>
              <p>
                {honeyHarvestingData?.createdAt
                  ? format(
                      new Date(honeyHarvestingData.createdAt),
                      "yyyy-MM-dd HH:mm:ss"
                    )
                  : ""}
              </p>
              <p>ID: {formId}</p>
            </div>
          </div>
        )}
      </CardHeader>
      <FormDetails
        formData={(!isOffline ? honeyHarvestingData : harvestOfflineData)!}
      />
    </div>
  )
}
