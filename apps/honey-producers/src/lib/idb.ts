import { openDB, type IDBPDatabase } from "idb"

// Types
export type Farmer = {
  id: string
  firstName: string
  lastName: string
  gender: string
  phone: string
  nrc: string
  maritalStatus: string
  dob?: string
  householdSize: string
  estimatedAnnualIncome: number
  sourceOfIncome: string
  countryId: string
  role: string
  regionId: string
  chiefdomId: string
  zoneId: string
  villageId: string
  createdAt?: string
  updatedAt?: string
  is_deleted?: boolean
}

export type Beehive = {
  id: string
  name: string
  status: string
  latitude: number
  longitude: number
  isFunctional: boolean
  needsToBeRepaired: boolean
  isOccupied: boolean
  observation?: string
  farmerId: string
  createdAt?: string
  updatedAt?: string
  is_deleted?: boolean
}

export type HoneySubmission = {
  id: string
  farmerId: string
  beehives: string[]
  saleDate: string
  pricePerKg: number
  honeyType: "light" | "dark"
  buckets: {
    bucketId: string
    harvestDate: string
    moistureContent: number
    weight: number
    honeyType: string
  }[]
  createdBy: string
  createdAt?: string
  updatedAt?: string
  is_deleted?: boolean
}

export type Location = {
  id: string
  name: string
  children?: Location[]
}

// DB constants
const DB_NAME = "palmyra-pro-db-1"
const DB_VERSION = 2

export const STORE_NAMES = {
  farmersDownloaded: "farmersDownloaded",
  farmersToCreate: "farmersToCreate",
  farmersToUpdate: "farmersToUpdate",
  beehivesDownloaded: "beehivesDownloaded",
  beehivesToCreate: "beehivesToCreate",
  beehivesToUpdate: "beehivesToUpdate",
  honey: "honey-harvesting",
  auth: "auth",
  location: "location",
} as const

// Init DB
let dbPromise: Promise<IDBPDatabase>

const initDB = () => {
  if (!dbPromise) {
    dbPromise = openDB(DB_NAME, DB_VERSION, {
      upgrade(db) {
        if (!db.objectStoreNames.contains(STORE_NAMES.farmersDownloaded)) {
          db.createObjectStore(STORE_NAMES.farmersDownloaded, {
            keyPath: "id",
          })
        }

        if (!db.objectStoreNames.contains(STORE_NAMES.farmersToCreate)) {
          db.createObjectStore(STORE_NAMES.farmersToCreate, {
            keyPath: "id",
          })
        }

        if (!db.objectStoreNames.contains(STORE_NAMES.farmersToUpdate)) {
          db.createObjectStore(STORE_NAMES.farmersToUpdate, {
            keyPath: "id",
          })
        }

        if (!db.objectStoreNames.contains(STORE_NAMES.beehivesDownloaded)) {
          db.createObjectStore(STORE_NAMES.beehivesDownloaded, {
            keyPath: "id",
          })
        }

        if (!db.objectStoreNames.contains(STORE_NAMES.beehivesToCreate)) {
          db.createObjectStore(STORE_NAMES.beehivesToCreate, {
            keyPath: "id",
          })
        }

        if (!db.objectStoreNames.contains(STORE_NAMES.beehivesToUpdate)) {
          db.createObjectStore(STORE_NAMES.beehivesToUpdate, {
            keyPath: "id",
          })
        }

        if (!db.objectStoreNames.contains(STORE_NAMES.honey)) {
          db.createObjectStore(STORE_NAMES.honey, {
            keyPath: "id",
          })
        }

        if (!db.objectStoreNames.contains(STORE_NAMES.auth)) {
          db.createObjectStore(STORE_NAMES.auth)
        }

        if (!db.objectStoreNames.contains(STORE_NAMES.location)) {
          db.createObjectStore(STORE_NAMES.location, {
            keyPath: "id",
          })
        }
      },
    })
  }
  return dbPromise
}

// Generic Functions

// Create
export const saveToIDB = async <T extends { id?: string }>(
  storeName: string,
  data: Partial<Omit<T, "createdAt" | "updatedAt" | "is_deleted">> & {
    id?: string
  }
): Promise<T> => {
  const db = await initDB()

  const item: T = {
    ...(data as any),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    is_deleted: false,
  }

  await db.put(storeName, item)
  return item
}

// Update
export const updateInIDB = async <T extends { id: string }>(
  storeName: string,
  data: Partial<Omit<T, "updatedAt">> & { id: string }
): Promise<T> => {
  const db = await initDB()

  const existingItem = await db.get(storeName, data.id)

  if (!existingItem) {
    throw new Error(`No item with id ${data.id} found in store "${storeName}"`)
  }

  const updatedItem: T = {
    ...existingItem,
    ...data,
    updatedAt: new Date().toISOString(),
  }

  await db.put(storeName, updatedItem)
  return updatedItem
}

// Read all
export const getAllFromIDB = async <T>(storeName: string): Promise<T[]> => {
  const db = await initDB()
  return db.getAll(storeName)
}

// Delete
export const deleteFromIDB = async (storeName: string, id: string) => {
  const db = await initDB()
  await db.delete(storeName, id)
}

// Store farmers downloaded from API
export const storeFarmersFromAPI = async (
  farmers: Farmer[]
): Promise<Farmer[]> => {
  try {
    const savedFarmers = await Promise.all(
      farmers.map((farmer) =>
        saveToIDB<Farmer>(STORE_NAMES.farmersDownloaded, farmer)
      )
    )
    return savedFarmers
  } catch (error) {
    console.error("Error storing downloaded farmers:", error)
    throw new Error("Failed to store downloaded farmers")
  }
}

// Store new farmers created offline
export const storeOfflineCreatedFarmer = async (
  farmer: Partial<Farmer>
): Promise<Farmer> => {
  try {
    // 1. Save in farmersToCreate
    await saveToIDB<Farmer>(STORE_NAMES.farmersToCreate, farmer)

    // 2. Save in farmersDownloaded
    return await saveToIDB<Farmer>(STORE_NAMES.farmersDownloaded, farmer)
  } catch (error) {
    console.error("Error storing offline-created farmer:", error)
    throw new Error("Failed to store offline-created farmer")
  }
}

// Store farmers to updated offline
export const storeOfflineUpdatedFarmer = async (
  farmer: Partial<Farmer>
): Promise<Farmer> => {
  try {
    // 1. Save in farmersToUpdate
    await saveToIDB<Farmer>(STORE_NAMES.farmersToUpdate, farmer)

    // 2. Save in farmersDownloaded
    return await saveToIDB<Farmer>(STORE_NAMES.farmersDownloaded, farmer)
  } catch (error) {
    console.error("Error storing offline-updated farmer:", error)
    throw new Error("Failed to store offline-updated farmer")
  }
}

// Store beehives downloaded from API
export const storeBeehivesFromAPI = async (
  beehives: Beehive[]
): Promise<Beehive[]> => {
  try {
    const savedBeehives = await Promise.all(
      beehives.map((beehive) =>
        saveToIDB<Beehive>(STORE_NAMES.beehivesDownloaded, beehive)
      )
    )
    return savedBeehives
  } catch (error) {
    console.error("Error storing downloaded beehives:", error)
    throw new Error("Failed to store downloaded beehives")
  }
}

// Store new beehives created offline
export const storeOfflineCreatedBeehive = async (
  beehive: Partial<Beehive>
): Promise<Beehive> => {
  try {
    // 1. Save in beehivesToCreate
    await saveToIDB<Beehive>(STORE_NAMES.beehivesToCreate, beehive)

    // 2. Save in beehivesDownloaded
    return await saveToIDB<Beehive>(STORE_NAMES.beehivesDownloaded, beehive)
  } catch (error) {
    console.error("Error storing offline-created beehive:", error)
    throw new Error("Failed to store offline-created beehive")
  }
}

// Store beehives to updated offline
export const storeOfflineUpdatedBeehive = async (
  beehive: Partial<Beehive>
): Promise<Beehive> => {
  try {
    // 1. Save in beehivesToUpdate
    await saveToIDB<Beehive>(STORE_NAMES.beehivesToUpdate, beehive)

    // 2. Save in beehivesDownloaded
    return await saveToIDB<Beehive>(STORE_NAMES.beehivesDownloaded, beehive)
  } catch (error) {
    console.error("Error storing offline-updated beehive:", error)
    throw new Error("Failed to store offline-updated beehive")
  }
}

// Store new Location in IndexedDB
export const storeLocationsInIndexedDB = async (
  locations: Location[]
): Promise<Location[]> => {
  try {
    const savedLocations = await Promise.all(
      locations.map((location) =>
        saveToIDB<Location>(STORE_NAMES.location, location)
      )
    )
    return savedLocations
  } catch (error) {
    console.error("Error storing locations:", error)
    throw new Error("Failed to store locations")
  }
}

// 🔐 Auth session management
export const saveUserSession = async (user: any): Promise<void> => {
  const db = await initDB()
  await db.put(STORE_NAMES.auth, user, "session")
}

export const getUserSession = async (): Promise<any | null> => {
  const db = await initDB()
  return db.get(STORE_NAMES.auth, "session")
}

export const clearUserSession = async (): Promise<void> => {
  const db = await initDB()
  await db.delete(STORE_NAMES.auth, "session")
}

// Clear all data from a given store
export const clearIDBStore = async (storeName: string) => {
  const db = await initDB()
  await db.clear(storeName)
}
