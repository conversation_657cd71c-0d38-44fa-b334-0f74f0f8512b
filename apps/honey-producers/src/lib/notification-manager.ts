"use client"

import { useState, useEffect } from "react"
import { generateUUID } from "@/lib/utils"

export type NotificationType = "success" | "error" | "warning" | "info"

export interface Notification {
  id: string
  type: NotificationType
  title: string
  description: string
  timestamp: Date
  read: boolean
  action?: {
    label: string
    onClick: () => void
  }
}

type NotificationListener = () => void

const STORAGE_KEY = "app-notifications"

// Singleton pattern
class NotificationManager {
  private notifications: Notification[] = []
  private listeners: NotificationListener[] = []
  private initialized = false

  constructor() {
    // Delay initialization to ensure we're in the browser
    if (typeof window !== "undefined") {
      this.initialize()
    }
  }

  private initialize(): void {
    if (this.initialized) return
    this.loadFromStorage()
    this.initialized = true
    console.log(
      "NotificationManager initialized with",
      this.notifications.length,
      "notifications"
    )
  }

  // Load notifications from localStorage
  private loadFromStorage(): void {
    try {
      const stored = localStorage.getItem(STORAGE_KEY)
      if (stored) {
        const parsed = JSON.parse(stored)
        this.notifications = parsed.map((notif: any) => ({
          ...notif,
          timestamp: new Date(notif.timestamp),
        }))
        console.log("Loaded notifications from storage:", this.notifications)
      }
    } catch (error) {
      console.error("Failed to load notifications from localStorage:", error)
    }
  }

  // Save notifications to localStorage
  private saveToStorage(): void {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(this.notifications))
      console.log("Saved notifications to storage:", this.notifications.length)
    } catch (error) {
      console.error("Failed to save notifications to localStorage:", error)
    }
  }

  // Notify all listeners of changes
  private notifyListeners(): void {
    console.log("Notifying", this.listeners.length, "listeners")
    this.listeners.forEach((listener) => listener())
  }

  // Subscribe to changes
  subscribe(listener: NotificationListener): () => void {
    this.listeners.push(listener)
    console.log("Listener subscribed, total:", this.listeners.length)
    return () => {
      this.listeners = this.listeners.filter((l) => l !== listener)
      console.log("Listener unsubscribed, total:", this.listeners.length)
    }
  }

  // Get all notifications
  getNotifications(): Notification[] {
    if (!this.initialized && typeof window !== "undefined") {
      this.initialize()
    }
    return [...this.notifications]
  }

  // Add a new notification
  addNotification(
    notification: Omit<Notification, "id" | "timestamp" | "read">
  ): void {
    if (!this.initialized && typeof window !== "undefined") {
      this.initialize()
    }

    const newNotification: Notification = {
      ...notification,
      id: generateUUID(),
      timestamp: new Date(),
      read: false,
    }

    console.log("Adding notification:", newNotification)
    this.notifications = [newNotification, ...this.notifications].slice(0, 50)
    this.saveToStorage()
    this.notifyListeners()
  }

  // Mark a notification as read
  markAsRead(id: string): void {
    this.notifications = this.notifications.map((notif) =>
      notif.id === id ? { ...notif, read: true } : notif
    )
    this.saveToStorage()
    this.notifyListeners()
  }

  // Mark all notifications as read
  markAllAsRead(): void {
    this.notifications = this.notifications.map((notif) => ({
      ...notif,
      read: true,
    }))
    this.saveToStorage()
    this.notifyListeners()
  }

  // Remove a notification
  removeNotification(id: string): void {
    this.notifications = this.notifications.filter((notif) => notif.id !== id)
    this.saveToStorage()
    this.notifyListeners()
  }

  // Clear all notifications
  clearAll(): void {
    this.notifications = []
    this.saveToStorage()
    this.notifyListeners()
  }

  // Get unread count
  getUnreadCount(): number {
    return this.notifications.filter((notif) => !notif.read).length
  }
}

// Create a singleton instance
export const notificationManager = new NotificationManager()

// Custom hook to use notifications
export function useNotifications() {
  const [, setForceUpdate] = useState({})

  useEffect(() => {
    console.log("useNotifications hook mounted")
    // Subscribe to changes and force re-render when notifications change
    const unsubscribe = notificationManager.subscribe(() => {
      console.log("Notification change detected, forcing update")
      setForceUpdate({})
    })

    return unsubscribe
  }, [])

  const notifications = notificationManager.getNotifications()
  console.log(
    "useNotifications returning",
    notifications.length,
    "notifications"
  )

  return {
    notifications,
    addNotification: (
      notif: Omit<Notification, "id" | "timestamp" | "read">
    ) => {
      console.log("Adding notification via hook:", notif)
      notificationManager.addNotification(notif)
    },
    markAsRead: notificationManager.markAsRead.bind(notificationManager),
    markAllAsRead: notificationManager.markAllAsRead.bind(notificationManager),
    removeNotification:
      notificationManager.removeNotification.bind(notificationManager),
    clearAll: notificationManager.clearAll.bind(notificationManager),
    getUnreadCount:
      notificationManager.getUnreadCount.bind(notificationManager),
  }
}
