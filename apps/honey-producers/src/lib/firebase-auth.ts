import { signInWithEmailAndPassword } from "firebase/auth"
import { auth } from "@/lib/auth-client"

export const loginWithEmail = async (
  tenantId: string,
  email: string,
  password: string
) => {
  auth.tenantId = tenantId
  const userCredential = await signInWithEmailAndPassword(auth, email, password)
  const token = await userCredential.user.getIdToken()

  return {
    user: userCredential.user,
    token,
  }
}
