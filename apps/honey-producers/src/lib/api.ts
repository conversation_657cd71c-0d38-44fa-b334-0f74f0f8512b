import createFetchClient, { Middleware } from "openapi-fetch"
import createClient from "openapi-react-query"
import { paths } from "@repo/api-specs"
import { auth } from "./auth-client"

const fetchClient = createFetchClient<paths>({
  baseUrl: process.env.NEXT_PUBLIC_BACKEND_URL,
  credentials: "include",
})

const middleware: Middleware = {
  async onRequest({ request }) {
    const user = auth.currentUser
    if (!user) {
      console.warn("No user is authenticated.")
      return request
    }

    const token = await user.getIdToken()
    if (!token) {
      console.warn("Failed to get Firebase ID token.")
      return request
    }

    request.headers.set("Authorization", `Bearer ${token}`)
    return request
  },
}

fetchClient.use(middleware)

const api = createClient(fetchClient)

export default api
