import createFetchClient, { Middleware } from "openapi-fetch"
import createClient from "openapi-react-query"
import { paths } from "@repo/api-specs"
import { authClient } from "./auth-client"

const fetchClient = createFetchClient<paths>({
  baseUrl: process.env.NEXT_PUBLIC_BACKEND_URL,
  credentials: "include",
})

const middleware: Middleware = {
  async onRequest({ request }) {
    const tokenResponse = await authClient.$fetch<{ token: string }>("/token")

    if (!tokenResponse.data?.token) {
      console.error("No token available for authorization.")
      return request
    }

    request.headers.set("Authorization", `Bearer ${tokenResponse.data.token}`)
    return request
  },
}

fetchClient.use(middleware)

const api = createClient(fetchClient)

export default api
