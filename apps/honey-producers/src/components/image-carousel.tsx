"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, X, Trash2 } from "lucide-react"

interface ImageCarouselProps {
  images: string[]
  onRemoveImage?: (index: number) => void
}

export function ImageCarousel({ images, onRemoveImage }: ImageCarouselProps) {
  const [open, setOpen] = useState(false)
  const [currentIndex, setCurrentIndex] = useState(0)

  const handlePrevious = () => {
    setCurrentIndex((prev) => (prev === 0 ? images.length - 1 : prev - 1))
  }

  const handleNext = () => {
    setCurrentIndex((prev) => (prev === images.length - 1 ? 0 : prev + 1))
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "ArrowLeft") {
      handlePrevious()
    } else if (e.key === "ArrowRight") {
      handleNext()
    } else if (e.key === "Escape") {
      setOpen(false)
    }
  }

  // Render different layouts based on the number of images
  const renderCarouselGrid = () => {
    if (images.length === 0) {
      return (
        <div className="flex items-center justify-center bg-gray-100 rounded-lg h-40">
          <p className="text-gray-500">No images available</p>
        </div>
      )
    }

    if (images.length === 1) {
      return (
        <div
          className="relative rounded-lg overflow-hidden cursor-pointer group h-80"
          onClick={() => {
            setCurrentIndex(0)
            setOpen(true)
          }}
        >
          <img
            src={images[0] || "/placeholder.svg"}
            alt="Beehive image"
            className="w-full h-full object-cover"
          />
          {onRemoveImage && (
            <Button
              variant="destructive"
              size="icon"
              className="h-8 w-8 absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity z-10"
              onClick={(e) => {
                e.stopPropagation()
                onRemoveImage(0)
              }}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      )
    }

    if (images.length === 2) {
      return (
        <div className="grid grid-cols-2 gap-2 h-60">
          {images.map((image, index) => (
            <div
              key={index}
              className="relative rounded-lg overflow-hidden cursor-pointer group"
              onClick={() => {
                setCurrentIndex(index)
                setOpen(true)
              }}
            >
              <img
                src={image || "/placeholder.svg"}
                alt={`Beehive image ${index + 1}`}
                className="w-full h-full object-cover"
              />
              {onRemoveImage && (
                <Button
                  variant="destructive"
                  size="icon"
                  className="h-8 w-8 absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity z-10"
                  onClick={(e) => {
                    e.stopPropagation()
                    onRemoveImage(index)
                  }}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          ))}
        </div>
      )
    }

    if (images.length === 3) {
      return (
        <div className="grid grid-cols-3 gap-2 h-60">
          {images.map((image, index) => (
            <div
              key={index}
              className="relative rounded-lg overflow-hidden cursor-pointer group"
              onClick={() => {
                setCurrentIndex(index)
                setOpen(true)
              }}
            >
              <img
                src={image || "/placeholder.svg"}
                alt={`Beehive image ${index + 1}`}
                className="w-full h-full object-cover"
              />
              {onRemoveImage && (
                <Button
                  variant="destructive"
                  size="icon"
                  className="h-8 w-8 absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity z-10"
                  onClick={(e) => {
                    e.stopPropagation()
                    onRemoveImage(index)
                  }}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          ))}
        </div>
      )
    }

    if (images.length === 4) {
      return (
        <div className="grid grid-cols-12 grid-rows-2 gap-2 h-[400px]">
          {/* First row - 3 images */}
          {[0, 1, 2].map((index) => (
            <div
              key={index}
              className="col-span-4 row-span-1 relative rounded-lg overflow-hidden cursor-pointer group"
              onClick={() => {
                setCurrentIndex(index)
                setOpen(true)
              }}
            >
              <img
                src={images[index] || "/placeholder.svg"}
                alt={`Beehive image ${index + 1}`}
                className="w-full h-full object-cover"
              />
              {onRemoveImage && (
                <Button
                  variant="destructive"
                  size="icon"
                  className="h-8 w-8 absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity z-10"
                  onClick={(e) => {
                    e.stopPropagation()
                    onRemoveImage(index)
                  }}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          ))}

          {/* Second row - 1 large image */}
          <div
            className="col-span-12 row-span-1 relative rounded-lg overflow-hidden cursor-pointer group"
            onClick={() => {
              setCurrentIndex(3)
              setOpen(true)
            }}
          >
            <img
              src={images[3] || "/placeholder.svg"}
              alt="Beehive image 4"
              className="w-full h-full object-cover"
            />
            {onRemoveImage && (
              <Button
                variant="destructive"
                size="icon"
                className="h-8 w-8 absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity z-10"
                onClick={(e) => {
                  e.stopPropagation()
                  onRemoveImage(3)
                }}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      )
    }

    if (images.length === 5) {
      return (
        <div className="grid grid-cols-12 grid-rows-2 gap-2 h-[400px]">
          {/* First row - 3 images */}
          {[0, 1, 2].map((index) => (
            <div
              key={index}
              className="col-span-4 row-span-1 relative rounded-lg overflow-hidden cursor-pointer group"
              onClick={() => {
                setCurrentIndex(index)
                setOpen(true)
              }}
            >
              <img
                src={images[index] || "/placeholder.svg"}
                alt={`Beehive image ${index + 1}`}
                className="w-full h-full object-cover"
              />
              {onRemoveImage && (
                <Button
                  variant="destructive"
                  size="icon"
                  className="h-8 w-8 absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity z-10"
                  onClick={(e) => {
                    e.stopPropagation()
                    onRemoveImage(index)
                  }}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          ))}

          {/* Second row - 2 images */}
          <div
            className="col-span-8 row-span-1 relative rounded-lg overflow-hidden cursor-pointer group"
            onClick={() => {
              setCurrentIndex(3)
              setOpen(true)
            }}
          >
            <img
              src={images[3] || "/placeholder.svg"}
              alt="Beehive image 4"
              className="w-full h-full object-cover"
            />
            {onRemoveImage && (
              <Button
                variant="destructive"
                size="icon"
                className="h-8 w-8 absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity z-10"
                onClick={(e) => {
                  e.stopPropagation()
                  onRemoveImage(3)
                }}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
          </div>
          <div
            className="col-span-4 row-span-1 relative rounded-lg overflow-hidden cursor-pointer group"
            onClick={() => {
              setCurrentIndex(4)
              setOpen(true)
            }}
          >
            <img
              src={images[4] || "/placeholder.svg"}
              alt="Beehive image 5"
              className="w-full h-full object-cover"
            />
            {onRemoveImage && (
              <Button
                variant="destructive"
                size="icon"
                className="h-8 w-8 absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity z-10"
                onClick={(e) => {
                  e.stopPropagation()
                  onRemoveImage(4)
                }}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      )
    }

    // Default layout for 6+ images (3 on top, 1 large + 1 with counter on bottom)
    return (
      <div className="grid grid-cols-12 grid-rows-2 gap-2 h-[400px]">
        {/* First row - 3 images */}
        {[0, 1, 2].map((index) => (
          <div
            key={index}
            className="col-span-4 row-span-1 relative rounded-lg overflow-hidden cursor-pointer group"
            onClick={() => {
              setCurrentIndex(index)
              setOpen(true)
            }}
          >
            <img
              src={images[index] || "/placeholder.svg"}
              alt={`Beehive image ${index + 1}`}
              className="w-full h-full object-cover"
            />
            {onRemoveImage && (
              <Button
                variant="destructive"
                size="icon"
                className="h-8 w-8 absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity z-10"
                onClick={(e) => {
                  e.stopPropagation()
                  onRemoveImage(index)
                }}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
          </div>
        ))}

        {/* Second row - 2 images */}
        <div
          className="col-span-8 row-span-1 relative rounded-lg overflow-hidden cursor-pointer group"
          onClick={() => {
            setCurrentIndex(3)
            setOpen(true)
          }}
        >
          <img
            src={images[3] || "/placeholder.svg"}
            alt="Beehive image 4"
            className="w-full h-full object-cover"
          />
          {onRemoveImage && (
            <Button
              variant="destructive"
              size="icon"
              className="h-8 w-8 absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity z-10"
              onClick={(e) => {
                e.stopPropagation()
                onRemoveImage(3)
              }}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
        <div
          className="col-span-4 row-span-1 relative rounded-lg overflow-hidden cursor-pointer group"
          onClick={() => {
            setCurrentIndex(4)
            setOpen(true)
          }}
        >
          <div className="absolute inset-0 bg-black/60 flex items-center justify-center z-10">
            <span className="text-white text-2xl font-bold">
              +{images.length - 5}
            </span>
          </div>
          <img
            src={images[4] || "/placeholder.svg"}
            alt="More beehive images"
            className="w-full h-full object-cover"
          />
          {onRemoveImage && images.length <= 5 && (
            <Button
              variant="destructive"
              size="icon"
              className="h-8 w-8 absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity z-20"
              onClick={(e) => {
                e.stopPropagation()
                onRemoveImage(4)
              }}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
    )
  }

  return (
    <>
      {renderCarouselGrid()}

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent
          className="max-w-4xl p-0 bg-black/90 border-none"
          onKeyDown={handleKeyDown}
          tabIndex={0}
        >
          <DialogTitle />
          <div className="relative h-[80vh] flex items-center justify-center">
            <Button
              variant="ghost"
              size="icon"
              className="absolute top-2 right-2 text-white z-20 hover:bg-white/20"
              onClick={() => setOpen(false)}
            >
              <X className="h-6 w-6" />
            </Button>

            <Button
              variant="ghost"
              size="icon"
              className="absolute left-2 top-1/2 -translate-y-1/2 text-white z-20 hover:bg-white/20"
              onClick={handlePrevious}
            >
              <ChevronLeft className="h-8 w-8" />
            </Button>

            <div className="w-full h-full flex items-center justify-center p-8">
              <img
                src={images[currentIndex] || "/placeholder.svg"}
                alt={`Beehive image ${currentIndex + 1}`}
                className="max-h-full max-w-full object-contain"
              />
            </div>

            <Button
              variant="ghost"
              size="icon"
              className="absolute right-2 top-1/2 -translate-y-1/2 text-white z-20 hover:bg-white/20"
              onClick={handleNext}
            >
              <ChevronRight className="h-8 w-8" />
            </Button>

            <div className="absolute bottom-4 left-0 right-0 flex justify-center gap-1">
              {images.map((_, index) => (
                <button
                  key={index}
                  className={`w-2 h-2 rounded-full ${index === currentIndex ? "bg-white" : "bg-white/40"}`}
                  onClick={() => setCurrentIndex(index)}
                />
              ))}
            </div>

            {onRemoveImage && (
              <Button
                variant="destructive"
                size="sm"
                className="absolute bottom-4 right-4 z-20"
                onClick={() => {
                  onRemoveImage(currentIndex)
                  if (images.length <= 1) {
                    setOpen(false)
                  } else if (currentIndex === images.length - 1) {
                    setCurrentIndex(currentIndex - 1)
                  }
                }}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Image
              </Button>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
