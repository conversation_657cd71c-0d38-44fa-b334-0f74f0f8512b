import React from "react"
import { <PERSON><PERSON> } from "./ui/button"

interface OfflineSyncButtonProps {
  isOnline: boolean
  hasPendingData: boolean
  isSyncing: boolean
  syncError: string | null
  onSync: () => void
}

export const OfflineSyncButton: React.FC<OfflineSyncButtonProps> = ({
  isOnline,
  hasPendingData,
  isSyncing,
  syncError,
  onSync,
}) => {
  if (!hasPendingData) return null

  return (
    <Button onClick={onSync} disabled={isSyncing || !isOnline}>
      {isSyncing ? "Syncing..." : "Sync"}
    </Button>
  )
}
