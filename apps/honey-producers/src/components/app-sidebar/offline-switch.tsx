"use client"

import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Loader2 } from "lucide-react"
import { useOffline } from "@/hooks/use-offline"
import {
  SidebarMenu,
  SidebarMenuItem,
  SidebarGroup,
} from "@/components/ui/sidebar"

export function OfflineSwitch() {
  const { isOffline, isLoading, toggleOfflineMode } = useOffline()
  return (
    <SidebarGroup className="p-0">
      <SidebarMenu>
        <SidebarMenuItem className="border-none">
          <div>
            <div className="flex items-center gap-2">
              <div className="flex items-center relative">
                {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
                <Switch
                  id="offline-mode"
                  checked={isOffline}
                  onCheckedChange={toggleOfflineMode}
                  disabled={isLoading}
                />
              </div>
              <div>
                <Label htmlFor="offline-mode" className="text-base">
                  Offline mode
                </Label>
              </div>
            </div>
          </div>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarGroup>
  )
}
