"use client"

import {
  Bell,
  Check,
  X,
  AlertCircle,
  CheckCircle,
  Info,
  AlertTriangle,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  useNotifications,
  type Notification,
  type NotificationType,
} from "@/lib/notification-manager"
import { formatDistanceToNow } from "date-fns"
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarGroup,
  useSidebar,
} from "@/components/ui/sidebar"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Collapsible, CollapsibleContent } from "@/components/ui/collapsible"
import { useState, useEffect } from "react"

const getNotificationIcon = (type: NotificationType) => {
  switch (type) {
    case "success":
      return <CheckCircle className="h-4 w-4 text-green-500" />
    case "error":
      return <AlertCircle className="h-4 w-4 text-red-500" />
    case "warning":
      return <AlertTriangle className="h-4 w-4 text-yellow-500" />
    case "info":
      return <Info className="h-4 w-4 text-blue-500" />
    default:
      return <Bell className="h-4 w-4" />
  }
}

const getNotificationBgColor = (type: NotificationType) => {
  switch (type) {
    case "success":
      return "bg-green-50 border-green-200"
    case "error":
      return "bg-red-50 border-red-200"
    case "warning":
      return "bg-yellow-50 border-yellow-200"
    case "info":
      return "bg-blue-50 border-blue-200"
    default:
      return "bg-gray-50 border-gray-200"
  }
}

interface NotificationItemProps {
  notification: Notification
  onMarkAsRead: (id: string) => void
  onRemove: (id: string) => void
}

function NotificationItem({
  notification,
  onMarkAsRead,
  onRemove,
}: NotificationItemProps) {
  return (
    <div
      className={`p-4 border rounded-lg ${getNotificationBgColor(notification.type)} ${
        !notification.read ? "ring-2 ring-blue-100" : ""
      }`}
    >
      <div className="space-y-2">
        <div className="flex justify-between">
          {getNotificationIcon(notification.type)}
          <div className="flex items-center gap-3">
            {!notification.read && (
              <Button
                size="sm"
                variant="ghost"
                onClick={() => onMarkAsRead(notification.id)}
                className="h-3 w-3 p-0"
              >
                <Check className="h-3 w-3" />
              </Button>
            )}
            <Button
              size="sm"
              variant="ghost"
              onClick={() => onRemove(notification.id)}
              className="h-3 w-3 p-0"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        </div>
        <div className="flex items-start gap-3 flex-1">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <h4 className="font-semibold text-sm">{notification.title}</h4>
              {!notification.read && (
                <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0" />
              )}
            </div>
            <p className="text-sm text-gray-600 mt-1">
              {notification.description}
            </p>
            <p className="text-xs text-gray-500 mt-2">
              {formatDistanceToNow(new Date(notification.timestamp), {
                addSuffix: true,
              })}
            </p>
            {notification.action && (
              <Button
                size="sm"
                variant="outline"
                className="mt-2"
                onClick={notification.action.onClick}
              >
                {notification.action.label}
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export function NavNotifications() {
  const { state, setOpen } = useSidebar()
  const {
    notifications,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAll,
    getUnreadCount,
  } = useNotifications()

  const [isCollapsibleOpen, setIsCollapsibleOpen] = useState(false)
  const unreadCount = getUnreadCount()
  const recentNotifications = notifications.slice(0, 10)

  useEffect(() => {
    if (state === "collapsed") {
      setIsCollapsibleOpen(false)
    }
  }, [state])

  const handleTriggerClick = () => {
    if (state === "collapsed") {
      setOpen(true)
      setIsCollapsibleOpen(true)
    } else {
      setIsCollapsibleOpen(!isCollapsibleOpen)
    }
  }

  return (
    <SidebarGroup className="p-0">
      <Collapsible
        open={isCollapsibleOpen}
        onOpenChange={setIsCollapsibleOpen}
        className="group/collapsible"
      >
        <SidebarMenu>
          <SidebarMenuItem value="notifications" className="border-none">
            <div className="w-full">
              <SidebarMenuButton
                size="lg"
                tooltip={
                  state === "collapsed"
                    ? `${unreadCount} notifications`
                    : undefined
                }
                className="relative gap-4 ml-2 w-full"
                onClick={handleTriggerClick}
              >
                <div className="relative flex">
                  <Bell className="h-4 w-4" />
                  {unreadCount > 0 && (
                    <Badge
                      variant="destructive"
                      className="absolute -top-2 -right-2 h-4 w-4 flex items-center justify-center p-0 text-xs"
                    >
                      {unreadCount > 9 ? "9+" : unreadCount}
                    </Badge>
                  )}
                </div>
                <Label className="text-base">Notifications</Label>
              </SidebarMenuButton>

              <CollapsibleContent className="pb-0">
                <div className="space-y-1">
                  {/* Header Actions */}
                  {notifications.length > 0 && (
                    <div className="flex justify-between">
                      <div className="flex">
                        {unreadCount > 0 && (
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={markAllAsRead}
                            className="text-xs h-7"
                          >
                            Mark all read
                          </Button>
                        )}
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={clearAll}
                          className="text-xs h-7"
                        >
                          Clear all
                        </Button>
                      </div>
                    </div>
                  )}

                  <Separator />

                  {/* Notifications List */}
                  <ScrollArea className="h-[240px] w-full">
                    {recentNotifications.length === 0 ? (
                      <div className="p-8 text-center text-gray-500">
                        <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">No notifications yet</p>
                      </div>
                    ) : (
                      <div className="space-y-3 pr-4">
                        {recentNotifications.map((notification) => (
                          <NotificationItem
                            key={notification.id}
                            notification={notification}
                            onMarkAsRead={markAsRead}
                            onRemove={removeNotification}
                          />
                        ))}
                      </div>
                    )}
                  </ScrollArea>

                  {/* Footer */}
                  {notifications.length > 10 && (
                    <>
                      <Separator />
                      <div className="text-center pb-2">
                        <Button variant="ghost" size="sm" className="text-xs">
                          View all {notifications.length} notifications
                        </Button>
                      </div>
                    </>
                  )}
                </div>
              </CollapsibleContent>
            </div>
          </SidebarMenuItem>
        </SidebarMenu>
      </Collapsible>
    </SidebarGroup>
  )
}
