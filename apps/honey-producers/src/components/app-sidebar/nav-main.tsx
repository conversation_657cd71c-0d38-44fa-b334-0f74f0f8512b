"use client"

import { FC } from "react"
import { type LucideIcon } from "lucide-react"

import {
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuItem,
} from "@/components/ui/sidebar"

import SidebarMenuButton from "./sidebar-menu-button/sidebar-menu-button"

interface NavItem {
  title: string
  url: string
  icon?: LucideIcon
}

interface Props {
  items: NavItem[]
}

export const NavMain: FC<Props> = ({ items }) => {
  return (
    <SidebarGroupContent>
      <SidebarMenu className="space-y-1 px-1">
        {items.map((item) => (
          <SidebarMenuItem key={item.url}>
            <SidebarMenuButton href={item.url}>
              {item.icon && <item.icon />}
              <span className="items-center px-1">{item.title}</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        ))}
      </SidebarMenu>
    </SidebarGroupContent>
  )
}
