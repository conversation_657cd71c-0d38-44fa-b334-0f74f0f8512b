"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

import { countryDialCodes } from "@repo/consts"

export interface PhoneInputProps
  extends Omit<
    React.InputHTMLAttributes<HTMLInputElement>,
    "value" | "onChange"
  > {
  value: string
  onChange: (value: string) => void
}

export function PhoneInput({
  className,
  value,
  onChange,
  placeholder,
  ...props
}: PhoneInputProps) {
  const inputRef = React.useRef<HTMLInputElement>(null)
  const [isFocused, setIsFocused] = React.useState(false)
  const dialCodeWithSpace = `+${countryDialCodes.Zambia} `

  // Extract only the digits entered by the user (without the country prefix)
  const extractUserDigits = (phoneNumber: string) => {
    // If the number starts with +265, remove it
    if (phoneNumber.startsWith(`+${countryDialCodes.Zambia}`)) {
      phoneNumber = phoneNumber.substring(4)
    }
    // Remove all non-numeric characters
    return phoneNumber.replace(/\D/g, "")
  }

  // Format the phone number with the prefix and spaces
  const formatPhoneNumber = (userDigits: string) => {
    let formatted = `+${countryDialCodes.Zambia}`

    // Add a space after the prefix if we have digits
    if (userDigits.length > 0) {
      formatted += " "
    }

    // Add the first 3 digits
    if (userDigits.length > 0) {
      formatted += userDigits.substring(0, Math.min(3, userDigits.length))
    }

    // Add a space and the next 3 digits
    if (userDigits.length > 3) {
      formatted += " " + userDigits.substring(3, Math.min(6, userDigits.length))
    }

    // Add a space and the last 3 digits
    if (userDigits.length > 6) {
      formatted += " " + userDigits.substring(6, Math.min(9, userDigits.length))
    }

    return formatted
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const input = e.target.value

    // If the input is empty and we're deleting the prefix, reset
    if (input === "") {
      onChange("")
      return
    }

    // If the input only contains the prefix, consider it empty
    if (
      input === `+${countryDialCodes.Zambia}` ||
      input === dialCodeWithSpace
    ) {
      onChange("")
      return
    }

    // Extract only the digits entered by the user
    const userDigits = extractUserDigits(input)

    // Limit to 9 digits (not counting the country prefix)
    const trimmedDigits = userDigits.substring(0, 9)

    // Format the number
    const formattedValue = formatPhoneNumber(trimmedDigits)

    // Call onChange with the formatted value
    onChange(formattedValue)
  }

  // Handle focus
  const handleFocus = () => {
    setIsFocused(true)

    // If the field is empty, add the prefix
    if (!value) {
      onChange(" ")
    }

    // Position the cursor after the prefix

    if (inputRef.current) {
      inputRef.current.setSelectionRange(5, 5)
    }
  }

  // Handle blur (losing focus)
  const handleBlur = () => {
    setIsFocused(false)

    // If the field only contains the prefix, consider it empty
    if (
      value === `+${countryDialCodes.Zambia}` ||
      value === dialCodeWithSpace
    ) {
      onChange("")
    }
  }

  // Position the cursor after the prefix if the user clicks at the beginning
  const handleClick = (e: React.MouseEvent<HTMLInputElement>) => {
    const input = e.target as HTMLInputElement
    if (
      input.selectionStart !== null &&
      input.selectionStart < 5 &&
      value.startsWith(countryDialCodes.Zambia)
    ) {
      input.setSelectionRange(5, 5)
    }
  }

  // Prevent deletion of the prefix
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    const input = e.target as HTMLInputElement

    // If the user tries to delete the prefix
    if (
      (e.key === "Backspace" || e.key === "Delete") &&
      input.selectionStart !== null &&
      input.selectionStart <= 5 &&
      value.startsWith(`+${countryDialCodes.Zambia}`)
    ) {
      // If the selection starts before or in the prefix
      if (input.selectionEnd !== null && input.selectionEnd <= 5) {
        e.preventDefault()
      } else {
        // If the selection starts in the prefix but ends after
        input.setSelectionRange(5, input.selectionEnd as number)
      }
    }
  }

  // Format the value for display
  const displayValue = React.useMemo(() => {
    if (!value && !isFocused) {
      return ""
    }

    // If the field is empty but focused, show the prefix
    if (!value && isFocused) {
      return dialCodeWithSpace
    }

    // If the value doesn't start with +265, add it
    if (!value.startsWith(`+${countryDialCodes.Zambia}`)) {
      const userDigits = extractUserDigits(value)
      return formatPhoneNumber(userDigits)
    }

    return value
  }, [value, isFocused])

  return (
    <div className="relative">
      <input
        type="text"
        ref={inputRef}
        className={cn(
          "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          className
        )}
        value={displayValue}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onClick={handleClick}
        onKeyDown={handleKeyDown}
        placeholder={placeholder || "999 123 456"}
        {...props}
      />
    </div>
  )
}
