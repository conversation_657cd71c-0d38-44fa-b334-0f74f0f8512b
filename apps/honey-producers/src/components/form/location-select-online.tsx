"use client"

import { useFormContext } from "react-hook-form"
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Loader } from "lucide-react"
import api from "@/lib/api"

interface LocationSelectorProps {
  required?: boolean
  showLabels?: boolean
  className?: string
}

export function LocationSelectorOnline({
  required = true,
  showLabels = true,
  className = "",
}: LocationSelectorProps) {
  const { control, watch } = useFormContext()

  // Watch location selections to trigger dependent dropdowns
  const selectedRegion = watch("regionId")
  const selectedChiefdom = watch("chiefdomId")
  const selectedZone = watch("zoneId")

  // Reset dependent fields when parent selection changes

  // Fetch location data
  const { data: regions, isLoading: regionsLoading } = api.useQuery(
    "get",
    "/regions"
  )

  const { data: chiefdoms, isLoading: chiefdomsLoading } = api.useQuery(
    "get",
    "/regions/{id}/chiefdoms",
    {
      params: {
        path: {
          id: selectedRegion,
        },
      },
    },
    { enabled: !!selectedRegion }
  )

  const { data: zones, isLoading: zonesLoading } = api.useQuery(
    "get",
    "/chiefdoms/{id}/zones",
    {
      params: {
        path: {
          id: selectedChiefdom,
        },
      },
    },
    { enabled: !!selectedChiefdom }
  )

  const { data: villages, isLoading: villagesLoading } = api.useQuery(
    "get",
    "/zones/{id}/villages",
    {
      params: {
        path: {
          id: selectedZone,
        },
      },
    },
    { enabled: !!selectedZone }
  )

  const requiredIndicator = required ? (
    <span className="text-red-500">*</span>
  ) : null

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 gap-4 ${className}`}>
      {/* Region */}
      <FormField
        control={control}
        name="regionId"
        render={({ field }) => (
          <FormItem>
            {showLabels && (
              <FormLabel className="font-medium">
                Region {requiredIndicator}
              </FormLabel>
            )}
            {regionsLoading ? (
              <div className="flex justify-center items-center gap-2 pt-1">
                <Loader className="h-6 w-6 animate-spin text-primary" />
                <span>Loading regions...</span>
              </div>
            ) : (
              <Select
                onValueChange={(value) => {
                  field.onChange(value)
                }}
                value={field.value}
                defaultValue={field.value}
              >
                <FormControl>
                  <SelectTrigger className="bg-background">
                    <SelectValue placeholder="Select Region" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {regions?.map((region) => (
                    <SelectItem
                      key={`region-${region.id}`}
                      value={region.id || ""}
                    >
                      {region.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
            <FormMessage className="text-xs" />
          </FormItem>
        )}
      />

      {/* Chiefdom */}
      <FormField
        control={control}
        name="chiefdomId"
        render={({ field }) => (
          <FormItem>
            {showLabels && (
              <FormLabel className="font-medium">
                Chiefdom {requiredIndicator}
              </FormLabel>
            )}
            {chiefdomsLoading ? (
              <div className="flex justify-center items-center gap-2 pt-1">
                <Loader className="h-6 w-6 animate-spin text-primary" />
                <span>Loading chiefdoms...</span>
              </div>
            ) : (
              <Select
                onValueChange={field.onChange}
                value={field.value}
                defaultValue={field.value}
                disabled={!chiefdoms?.length}
              >
                <FormControl>
                  <SelectTrigger className="bg-background">
                    <SelectValue placeholder="Select Chiefdom" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {chiefdoms?.map((chiefdom) => (
                    <SelectItem
                      key={`chiefdom-${chiefdom.id}`}
                      value={chiefdom.id || ""}
                    >
                      {chiefdom.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
            <FormMessage className="text-xs" />
          </FormItem>
        )}
      />

      {/* Zone */}
      <FormField
        control={control}
        name="zoneId"
        render={({ field }) => (
          <FormItem>
            {showLabels && (
              <FormLabel className="font-medium">
                Zone {requiredIndicator}
              </FormLabel>
            )}
            {zonesLoading ? (
              <div className="flex justify-center items-center gap-2 pt-1">
                <Loader className="h-6 w-6 animate-spin text-primary" />
                <span>Loading zones...</span>
              </div>
            ) : (
              <Select
                onValueChange={field.onChange}
                value={field.value}
                defaultValue={field.value}
                disabled={!zones?.length}
              >
                <FormControl>
                  <SelectTrigger className="bg-background">
                    <SelectValue placeholder="Select Zone" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {zones?.map((zone) => (
                    <SelectItem key={`zone-${zone.id}`} value={zone.id || ""}>
                      {zone.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
            <FormMessage className="text-xs" />
          </FormItem>
        )}
      />

      {/* Village */}
      <FormField
        control={control}
        name="villageId"
        render={({ field }) => (
          <FormItem>
            {showLabels && (
              <FormLabel className="font-medium">
                Village {requiredIndicator}
              </FormLabel>
            )}
            {villagesLoading ? (
              <div className="flex justify-center items-center gap-2 pt-1">
                <Loader className="h-6 w-6 animate-spin text-primary" />
                <span>Loading Villages...</span>
              </div>
            ) : (
              <Select
                onValueChange={field.onChange}
                value={field.value}
                defaultValue={field.value}
                disabled={!villages?.length}
              >
                <FormControl>
                  <SelectTrigger className="bg-background">
                    <SelectValue placeholder="Select Village" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {villages?.map((village) => (
                    <SelectItem
                      key={`village-${village.id}`}
                      value={village.id || ""}
                    >
                      {village.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
            <FormMessage className="text-xs" />
          </FormItem>
        )}
      />
    </div>
  )
}
