"use client"

import type React from "react"
import { use<PERSON>em<PERSON>, type <PERSON> } from "react"
import { Loader, Search } from "lucide-react"
import { InputWithIcon } from "./input-with-icon"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { BucketCard } from "./bucket-card"
import { Bucket } from "@/app/forms/honey-processing/steps/sourcing"

interface Props {
  className?: string
  buckets: Bucket[]
  selectedBuckets: Bucket[]
  selectedBucketIds: string[]
  onSelectBucket: (bucket: Bucket) => void
  onSelectAll: () => void
  selectAll: boolean
  searchInput: string
  onSearchChange: (search: string) => void
  isLoading?: boolean
  hasMultipleTypes?: boolean
}

export const BucketSearch: FC<Props> = ({
  className,
  buckets,
  selectedBuckets,
  selectedBucketIds,
  onSelectBucket,
  onSelectAll,
  selectAll,
  searchInput,
  onSearch<PERSON><PERSON><PERSON>,
  isLoading,
  hasMultipleTypes,
}) => {
  return (
    <div className={className}>
      <div className="flex max-sm:flex-col max-sm:items-start gap-2 max-sm:mb-4 items-center justify-between mb-2">
        <div className="text-sm font-medium">
          {buckets.length} buckets available
        </div>
        <div className="flex items-center space-x-2">
          <Checkbox
            id="selectAll"
            checked={selectAll}
            onCheckedChange={onSelectAll}
          />
          <label
            htmlFor="selectAll"
            className="text-sm font-medium cursor-pointer"
          >
            Select All
          </label>
        </div>
      </div>

      <InputWithIcon
        icon={Search}
        placeholder="Search by bucket ID or by farmer ID"
        value={searchInput}
        onChange={(event) => onSearchChange(event.target.value)}
      />

      <ScrollArea className="h-[300px] mt-4">
        <div className="space-y-2">
          {buckets.map((bucket) => (
            <BucketCard
              key={bucket.id}
              bucket={bucket}
              isSelected={selectedBucketIds.includes(bucket.id)}
              onSelect={() => onSelectBucket(bucket)}
            />
          ))}
          {isLoading && (
            <div className="flex justify-center items-center gap-2 pt-1">
              <Loader className="h-6 w-6 animate-spin text-primary" />
              <span>Loading buckets...</span>
            </div>
          )}
          {buckets.length === 0 && (
            <p className="text-sm text-muted-foreground mt-4 text-center">
              No buckets found
            </p>
          )}
        </div>
      </ScrollArea>

      {selectedBuckets.length > 0 && (
        <div className="mt-4">
          <div className="text-sm font-medium mb-2">Selected buckets:</div>
          <div className="flex flex-wrap gap-2">
            {selectedBuckets.map((bucket) => (
              <Badge key={bucket.id} className="py-1">
                {bucket.bucketId}
                <button className="ml-1" onClick={() => onSelectBucket(bucket)}>
                  ×
                </button>
              </Badge>
            ))}
          </div>
        </div>
      )}

      {hasMultipleTypes && (
        <div className="mt-2 text-red-500">
          Buckets with different honey types selected. Please select buckets
          from the same type to proceed.
        </div>
      )}
    </div>
  )
}
