import React from "react"
import { MultiSelect } from "./multi-select"
import api from "@/lib/api"
import { Loader } from "lucide-react"

interface BeehiveSelectProps {
  farmerId: string
  onChange: (value: string[]) => void
  defaultValue: string[]
}

export const BeehiveSelect: React.FC<BeehiveSelectProps> = ({
  farmerId,
  onChange,
  defaultValue,
}) => {
  const { data: beehives, isLoading } = api.useQuery(
    "get",
    "/beehives",
    {
      params: {
        query: {
          farmerId,
        },
      },
    },
    {
      enabled: !!farmerId,
    }
  )

  if (isLoading) {
    return (
      <div className="flex justify-center items-center gap-2">
        <Loader className="h-6 w-6 animate-spin text-primary" />
        <span>Loading beehive data...</span>
      </div>
    )
  }

  return (
    <MultiSelect
      options={
        beehives?.results?.map((beehive) => ({
          value: beehive.id || "",
          label: beehive.name || "",
        })) || []
      }
      onValueChange={onChange}
      defaultValue={defaultValue}
      placeholder="Select beehive id's"
      variant="inverted"
      maxCount={20}
      className="bg-background"
    />
  )
}
