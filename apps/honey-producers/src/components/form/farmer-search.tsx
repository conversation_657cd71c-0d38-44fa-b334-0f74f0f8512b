import { useState, FC } from "react"
import { Loader, Search } from "lucide-react"
import ProfileCard from "../profile-card"
import { InputWithIcon } from "./input-with-icon"
import api from "@/lib/api"
import { components } from "@repo/api-specs"

interface Props {
  className?: string
  selectedFarmerId?: string
  onSelect: (farmer: components["schemas"]["Farmer"]) => void
}

export const FarmerSearch: FC<Props> = ({
  className,
  selectedFarmerId,
  onSelect,
}) => {
  const [searchInput, setSearchInput] = useState("")

  const { data, isLoading } = api.useQuery(
    "get",
    "/farmers/search",
    {
      params: {
        query: {
          query: searchInput,
        },
      },
    },
    { enabled: !!searchInput }
  )

  return (
    <div className={className}>
      <InputWithIcon
        icon={Search}
        placeholder="Search..."
        onChange={(event) => setSearchInput(event.target.value)}
      />

      {isLoading && (
        <div className="flex justify-center items-center mt-6">
          <Loader className="h-6 w-6 animate-spin text-primary" />
          <span className="ml-2">Loading farmers...</span>
        </div>
      )}

      <ul className="mt-4">
        {data?.results?.map((farmer) => (
          <ProfileCard
            key={farmer.id}
            id={farmer.id}
            firstName={farmer.firstName}
            lastName={farmer.lastName}
            location={farmer.regionId || ""}
            role={farmer.role || ""}
            isSelected={selectedFarmerId === farmer.id}
            onSelect={() => onSelect(farmer)}
          />
        ))}
        {searchInput && data?.results?.length === 0 && (
          <p className="text-sm text-muted-foreground mt-4">No farmers found</p>
        )}
      </ul>
    </div>
  )
}
