"use client"

import { useEffect, useState, type FC, useCallback } from "react"
import { Loader, Search, WifiOff } from "lucide-react"
import ProfileCard from "../profile-card"
import { InputWithIcon } from "./input-with-icon"
import { Badge } from "@/components/ui/badge"
import type { components } from "@repo/api-specs"
import { getAllFromIDB, STORE_NAMES } from "@/lib/idb"

interface Props {
  className?: string
  selectedFarmerId?: string
  onSelect: (farmer: components["schemas"]["Farmer"]) => void
}

type Farmer = components["schemas"]["Farmer"]

export const FarmerSearchOffline: FC<Props> = ({
  className,
  selectedFarmerId,
  onSelect,
}) => {
  const [searchInput, setSearchInput] = useState("")
  const [filteredFarmers, setFilteredFarmers] = useState<Farmer[]>([])
  const [allFarmers, setAllFarmers] = useState<Farmer[]>([])
  const [isLoading, setIsLoading] = useState(false)

  // Load farmers from IndexedDB
  const loadFarmers = useCallback(async () => {
    setIsLoading(true)

    try {
      const localFarmers = await getAllFromIDB<Farmer>(
        STORE_NAMES.farmersDownloaded
      )
      setAllFarmers(localFarmers)
      setFilteredFarmers(localFarmers)
    } catch (error) {
      console.error("Error loading farmers from IndexedDB:", error)
      setAllFarmers([])
      setFilteredFarmers([])
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Filter farmers based on search input
  const filterFarmers = useCallback(() => {
    if (!searchInput.trim()) {
      setFilteredFarmers(allFarmers)
      return
    }

    const query = searchInput.toLowerCase().trim()
    const results = allFarmers.filter((farmer) => {
      const fullName =
        `${farmer.firstName || ""} ${farmer.lastName || ""}`.toLowerCase()
      const id = farmer.id?.toLowerCase() || ""

      return fullName.includes(query) || id.includes(query)
    })

    setFilteredFarmers(results)
  }, [searchInput, allFarmers])

  // Load farmers on component mount
  useEffect(() => {
    loadFarmers()
  }, [loadFarmers])

  // Filter farmers when search input changes
  useEffect(() => {
    filterFarmers()
  }, [filterFarmers])

  return (
    <div className={className}>
      <InputWithIcon
        icon={Search}
        placeholder="Search farmers by name, ID..."
        value={searchInput}
        onChange={(event) => setSearchInput(event.target.value)}
        disabled={isLoading}
      />

      <div className="flex items-center gap-2 mb-4">
        <Badge variant="secondary" className="flex items-center gap-1">
          <WifiOff className="h-3 w-3" />
          Offline Mode
        </Badge>
      </div>

      {isLoading && (
        <div className="flex justify-center items-center mt-6">
          <Loader className="h-6 w-6 animate-spin text-primary" />
          <span className="ml-2">Loading offline farmers...</span>
        </div>
      )}

      {!isLoading && (
        <>
          <div className="text-sm text-muted-foreground mb-2">
            {searchInput
              ? `${filteredFarmers.length} farmer${filteredFarmers.length !== 1 ? "s" : ""} found`
              : `${allFarmers.length} farmer${allFarmers.length !== 1 ? "s" : ""} available offline`}
          </div>

          <ul className="space-y-2 max-h-96 overflow-y-auto">
            {filteredFarmers.map((farmer) => (
              <ProfileCard
                key={farmer.id}
                id={farmer.id}
                firstName={farmer.firstName}
                lastName={farmer.lastName}
                location={farmer.regionId || ""}
                role={farmer.role || "Farmer"}
                isSelected={selectedFarmerId === farmer.id}
                onSelect={() => onSelect(farmer)}
              />
            ))}
          </ul>

          {searchInput && filteredFarmers.length === 0 && (
            <div className="text-center py-8">
              <Search className="h-12 w-12 mx-auto text-muted-foreground/50 mb-4" />
              <p className="text-sm text-muted-foreground">
                No farmers found matching "{searchInput}"
              </p>
              <p className="text-xs text-muted-foreground mt-2">
                Try searching by name, phone number, NRC, or farmer ID
              </p>
            </div>
          )}

          {allFarmers.length === 0 && !isLoading && (
            <div className="text-center py-8">
              <WifiOff className="h-12 w-12 mx-auto text-muted-foreground/50 mb-4" />
              <p className="text-sm text-muted-foreground">
                No farmers stored offline
              </p>
              <p className="text-xs text-muted-foreground mt-2">
                Download farmers data when online to search offline
              </p>
            </div>
          )}
        </>
      )}
    </div>
  )
}
