"use client"

import type { <PERSON> } from "react"
import { Checkbox } from "@/components/ui/checkbox"
import { cn } from "@/lib/utils"
import { Card, CardContent } from "@/components/ui/card"
import { Bucket } from "@/app/forms/honey-processing/steps/sourcing"
import { format } from "date-fns"
import { Badge } from "../ui/badge"

interface BucketCardProps {
  bucket: Bucket
  isSelected: boolean
  onSelect: () => void
}

export const BucketCard: FC<BucketCardProps> = ({
  bucket,
  isSelected,
  onSelect,
}) => {
  const honeyTypeColor =
    bucket.honeyType === "light"
      ? {
          bg: "bg-yellow-300",
          text: "text-yellow-800",
          dot: "bg-yellow-700",
        }
      : {
          bg: "bg-amber-700",
          text: "text-amber-200",
          dot: "bg-amber-200",
        }

  return (
    <Card
      className={cn(
        "border bg-background rounded-lg p-4 cursor-pointer hover:bg-primary-foreground transition-colors",
        isSelected && "border-blue-500"
      )}
      onClick={onSelect}
    >
      <CardContent className="flex justify-between items-center p-0">
        <div className="flex max-sm:flex-col max-sm:gap-2 gap-24">
          <Badge className="w-fit">
            <p className="max-sm:hidden text-xs">Bucket ID</p>
            <p className="font-medium text-xs p-1">#{bucket.bucketId}</p>
          </Badge>

          <div>
            <div className="max-sm:hidden text-muted-foreground text-xs">
              Bucket Weight
            </div>
            <div className="font-bold text-center text-sm">
              {bucket.weight} Kgs
            </div>
          </div>
        </div>

        <div className="text-right">
          <div className="flex items-center gap-2">
            <div
              className={cn(
                "text-xs px-2 py-0.5 rounded-md flex items-center font-bold capitalize",
                honeyTypeColor.bg,
                honeyTypeColor.text
              )}
            >
              <div
                className={cn("w-2 h-2 rounded-full mr-1", honeyTypeColor.dot)}
              ></div>
              {bucket.honeyType}
            </div>
          </div>
          <div className="text-muted-foreground text-xs mt-1">
            {format(bucket.harvestDate, "d MMM yyyy")}
          </div>
        </div>

        <div onClick={(e) => e.stopPropagation()}>
          <Checkbox checked={isSelected} onCheckedChange={() => onSelect()} />
        </div>
      </CardContent>
    </Card>
  )
}
