"use client"

import { useEffect, useState, use<PERSON><PERSON>back, useMemo, useRef } from "react"
import { useFormContext } from "react-hook-form"
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Loader } from "lucide-react"
import { getAllFromIDB, STORE_NAMES } from "@/lib/idb"
import type { components } from "@repo/api-specs"

interface LocationSelectorProps {
  required?: boolean
  showLabels?: boolean
  className?: string
}

type Location = components["schemas"]["Region"] & {
  children?: Location[]
}

export function LocationSelectorOffline({
  required = true,
  showLabels = true,
  className = "",
}: LocationSelectorProps) {
  const { control, watch, setValue } = useFormContext()
  const [isLoading, setIsLoading] = useState(false)
  const [locationTree, setLocationTree] = useState<Location | null>(null)

  // Track if changes are from user interaction or programmatic
  const isUserInteraction = useRef(false)

  const selectedRegion = watch("regionId")
  const selectedChiefdom = watch("chiefdomId")
  const selectedZone = watch("zoneId")

  const loadLocations = useCallback(async () => {
    setIsLoading(true)
    try {
      const localLocations = await getAllFromIDB<Location>(STORE_NAMES.location)
      setLocationTree(localLocations[0] || null)
    } catch (error) {
      console.error("Error loading locations from IndexedDB:", error)
      setLocationTree(null)
    } finally {
      setIsLoading(false)
    }
  }, [])

  useEffect(() => {
    loadLocations()
  }, [loadLocations])

  const regions = locationTree?.children || []

  const chiefdoms = useMemo(() => {
    const region = regions.find((r) => r.id === selectedRegion)
    return region?.children || []
  }, [regions, selectedRegion])

  const zones = useMemo(() => {
    const chiefdom = chiefdoms.find((c) => c.id === selectedChiefdom)
    return chiefdom?.children || []
  }, [chiefdoms, selectedChiefdom])

  const villages = useMemo(() => {
    const zone = zones.find((z) => z.id === selectedZone)
    return zone?.children || []
  }, [zones, selectedZone])

  // Only clear child fields if the current values are invalid for the new parent
  useEffect(() => {
    if (!isUserInteraction.current) return

    // Check if current chiefdom is valid for the selected region
    const isChiefdomValid = chiefdoms.some((c) => c.id === selectedChiefdom)
    if (!isChiefdomValid && selectedChiefdom) {
      setValue("chiefdomId", "")
      setValue("zoneId", "")
      setValue("villageId", "")
    }

    isUserInteraction.current = false
  }, [selectedRegion, setValue, chiefdoms, selectedChiefdom])

  useEffect(() => {
    if (!isUserInteraction.current) return

    // Check if current zone is valid for the selected chiefdom
    const isZoneValid = zones.some((z) => z.id === selectedZone)
    if (!isZoneValid && selectedZone) {
      setValue("zoneId", "")
      setValue("villageId", "")
    }

    isUserInteraction.current = false
  }, [selectedChiefdom, setValue, zones, selectedZone])

  useEffect(() => {
    if (!isUserInteraction.current) return

    // Check if current village is valid for the selected zone
    const isVillageValid = villages.some((v) => v.id === watch("villageId"))
    if (!isVillageValid && watch("villageId")) {
      setValue("villageId", "")
    }

    isUserInteraction.current = false
  }, [selectedZone, setValue, villages, watch])

  const requiredIndicator = required ? (
    <span className="text-red-500">*</span>
  ) : null

  if (isLoading)
    return (
      <div className={`flex justify-center items-center gap-2 ${className}`}>
        <Loader className="h-6 w-6 animate-spin text-primary" />
        <span>Loading locations...</span>
      </div>
    )

  if (!locationTree)
    return (
      <div className={`text-center text-red-500 ${className}`}>
        Failed to load locations
      </div>
    )

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 gap-6 ${className}`}>
      {/* Region */}
      <FormField
        control={control}
        name="regionId"
        render={({ field }) => (
          <FormItem>
            {showLabels && (
              <FormLabel className="font-medium">
                Region {requiredIndicator}
              </FormLabel>
            )}
            <Select
              onValueChange={(value) => {
                isUserInteraction.current = true
                field.onChange(value)
              }}
              value={field.value}
              defaultValue={field.value}
            >
              <FormControl>
                <SelectTrigger className="bg-background">
                  <SelectValue placeholder="Select Region" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {regions.map((region) => (
                  <SelectItem
                    key={`region-${region.id}`}
                    value={region.id || ""}
                  >
                    {region.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage className="text-xs" />
          </FormItem>
        )}
      />

      {/* Chiefdom */}
      <FormField
        control={control}
        name="chiefdomId"
        render={({ field }) => (
          <FormItem>
            {showLabels && (
              <FormLabel className="font-medium">
                Chiefdom {requiredIndicator}
              </FormLabel>
            )}
            <Select
              onValueChange={(value) => {
                isUserInteraction.current = true
                field.onChange(value)
              }}
              value={field.value}
              defaultValue={field.value}
              disabled={!chiefdoms.length}
            >
              <FormControl>
                <SelectTrigger className="bg-background">
                  <SelectValue placeholder="Select Chiefdom" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {chiefdoms.map((chiefdom) => (
                  <SelectItem
                    key={`chiefdom-${chiefdom.id}`}
                    value={chiefdom.id || ""}
                  >
                    {chiefdom.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage className="text-xs" />
          </FormItem>
        )}
      />

      {/* Zone */}
      <FormField
        control={control}
        name="zoneId"
        render={({ field }) => (
          <FormItem>
            {showLabels && (
              <FormLabel className="font-medium">
                Zone {requiredIndicator}
              </FormLabel>
            )}
            <Select
              onValueChange={(value) => {
                isUserInteraction.current = true
                field.onChange(value)
              }}
              value={field.value}
              defaultValue={field.value}
              disabled={!zones.length}
            >
              <FormControl>
                <SelectTrigger className="bg-background">
                  <SelectValue placeholder="Select Zone" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {zones.map((zone) => (
                  <SelectItem key={`zone-${zone.id}`} value={zone.id || ""}>
                    {zone.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage className="text-xs" />
          </FormItem>
        )}
      />

      {/* Village */}
      <FormField
        control={control}
        name="villageId"
        render={({ field }) => (
          <FormItem>
            {showLabels && (
              <FormLabel className="font-medium">
                Village {requiredIndicator}
              </FormLabel>
            )}
            <Select
              onValueChange={(value) => {
                isUserInteraction.current = true
                field.onChange(value)
              }}
              value={field.value}
              defaultValue={field.value}
              disabled={!villages.length}
            >
              <FormControl>
                <SelectTrigger className="bg-background">
                  <SelectValue placeholder="Select Village" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {villages.map((village) => (
                  <SelectItem
                    key={`village-${village.id}`}
                    value={village.id || ""}
                  >
                    {village.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage className="text-xs" />
          </FormItem>
        )}
      />
    </div>
  )
}
