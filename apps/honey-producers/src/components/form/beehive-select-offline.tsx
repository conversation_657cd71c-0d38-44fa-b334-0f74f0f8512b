"use client"

import type React from "react"
import { useEffect, useState, useCallback } from "react"
import { MultiSelect } from "./multi-select"
import { Loader, WifiOff } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { getAllFromIDB, STORE_NAMES } from "@/lib/idb"
import type { components } from "@repo/api-specs"

interface BeehiveSelectOfflineProps {
  farmerId: string
  onChange: (value: string[]) => void
  defaultValue: string[]
}

type Farmer = components["schemas"]["Farmer"]

interface BeehiveOption {
  value: string
  label: string
}

export const BeehiveSelectOffline: React.FC<BeehiveSelectOfflineProps> = ({
  farmerId,
  onChange,
  defaultValue,
}) => {
  const [beehiveOptions, setBeehiveOptions] = useState<BeehiveOption[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const loadBeehives = useCallback(async () => {
    if (!farmerId) {
      setBeehiveOptions([])
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      // Get all farmers from IndexedDB
      const farmers = await getAllFromIDB<Farmer>(STORE_NAMES.farmersDownloaded)

      // Find the specific farmer
      const farmer = farmers.find((f) => f.id === farmerId)

      if (!farmer) {
        setError("Farmer not found in offline data")
        setBeehiveOptions([])
        return
      }

      // Extract beehives from farmer data
      const beehives = farmer.beehives || []

      if (!Array.isArray(beehives)) {
        setError("Invalid beehive data format")
        setBeehiveOptions([])
        return
      }

      // Transform beehives to options format
      const options: BeehiveOption[] = beehives.map((beehive, index) => {
        // Handle different possible beehive data structures
        if (typeof beehive === "string") {
          return {
            value: beehive,
            label: `Beehive ${beehive}`,
          }
        } else if (beehive && typeof beehive === "object") {
          return {
            value: beehive.id || beehive.name || `beehive-${index}`,
            label: beehive.name || beehive.id || `Beehive ${index + 1}`,
          }
        } else {
          return {
            value: `beehive-${index}`,
            label: `Beehive ${index + 1}`,
          }
        }
      })

      setBeehiveOptions(options)
    } catch (err) {
      console.error("Error loading beehives from IndexedDB:", err)
      setError("Failed to load beehive data")
      setBeehiveOptions([])
    } finally {
      setIsLoading(false)
    }
  }, [farmerId])

  useEffect(() => {
    loadBeehives()
  }, [loadBeehives])

  if (isLoading) {
    return (
      <div className="flex justify-center items-center gap-2 p-4 border rounded-md bg-muted/50">
        <Loader className="h-4 w-4 animate-spin text-primary" />
        <span className="text-sm">Loading beehives from offline data...</span>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-4 border rounded-md bg-destructive/10 border-destructive/20">
        <div className="flex items-center gap-2 text-destructive text-sm">
          <WifiOff className="h-4 w-4" />
          <span>{error}</span>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <Badge variant="secondary" className="flex items-center gap-1">
          <WifiOff className="h-3 w-3" />
          Offline Mode
        </Badge>
        <Badge variant="outline" className="text-xs">
          {beehiveOptions.length} beehive
          {beehiveOptions.length !== 1 ? "s" : ""} available
        </Badge>
      </div>

      {beehiveOptions.length === 0 ? (
        <div className="p-4 border rounded-md bg-muted/50 text-center">
          <WifiOff className="h-8 w-8 mx-auto text-muted-foreground/50 mb-2" />
          <p className="text-sm text-muted-foreground">
            No beehives found for this farmer
          </p>
          <p className="text-xs text-muted-foreground mt-1">
            Beehive data may not be available offline
          </p>
        </div>
      ) : (
        <MultiSelect
          options={beehiveOptions}
          onValueChange={onChange}
          defaultValue={defaultValue}
          placeholder="Select beehive IDs"
          variant="inverted"
          maxCount={20}
          className="bg-background"
        />
      )}
    </div>
  )
}
