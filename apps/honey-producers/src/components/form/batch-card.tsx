"use client"

import type { <PERSON> } from "react"
import type { Batch } from "@repo/dummy/honey-processing"
import { Card } from "../ui/card"

interface BatchCardProps {
  batch: Batch
  isSelected: boolean
  onSelect: () => void
}

export const BatchCard: FC<BatchCardProps> = ({ batch, onSelect }) => {
  return (
    <Card
      className="flex justify-between items-center p-3 cursor-pointer"
      onClick={onSelect}
    >
      <div className="flex items-center">
        <span className="font-medium">{batch.id}</span>
      </div>
      <div className="text-right">
        <div>{batch.weight} Kgs</div>
      </div>
    </Card>
  )
}
