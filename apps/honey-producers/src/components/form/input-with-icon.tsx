"use client"

import { forwardRef, ComponentProps } from "react"
import { Input } from "@/components/ui/input"
import { LucideIcon } from "lucide-react"

interface InputWithIconProps extends ComponentProps<typeof Input> {
  icon: LucideIcon
}

export const InputWithIcon = forwardRef<HTMLInputElement, InputWithIconProps>(
  ({ icon: Icon, ...inputProps }, ref) => {
    return (
      <div className="relative w-full">
        <Icon className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-5 w-5" />
        <Input
          ref={ref}
          type="text"
          className="pl-10 bg-background"
          {...inputProps}
        />
      </div>
    )
  }
)

InputWithIcon.displayName = "InputWithIcon"
