"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON><PERSON>nt,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Camera, Upload, Trash2, X } from "lucide-react"

interface ProfilePhotoDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentPhotoUrl: string
  userName: string
  onPhotoUpload?: (photoUrl: string | null) => void
}

export function ProfilePhotoDialog({
  open,
  onOpenChange,
  currentPhotoUrl,
  userName,
  onPhotoUpload,
}: ProfilePhotoDialogProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [cameraActive, setCameraActive] = useState(false)
  const [cameraError, setCameraError] = useState<string | null>(null)

  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const streamRef = useRef<MediaStream | null>(null)

  // Reset camera state when dialog opens
  useEffect(() => {
    if (open) {
      setCameraActive(false)
      setCameraError(null)
      if (streamRef.current) {
        streamRef.current.getTracks().forEach((track) => track.stop())
        streamRef.current = null
      }
    }
  }, [open])

  // Clean up camera stream when component unmounts or dialog closes
  useEffect(() => {
    return () => {
      stopCamera()
    }
  }, [])

  // Get initials for avatar fallback
  const getInitials = () => {
    return userName
      .split(" ")
      .map((n) => n[0])
      .join("")
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    setSelectedFile(file)

    // Create a preview URL
    const reader = new FileReader()
    reader.onloadend = () => {
      setPreviewUrl(reader.result as string)
    }
    reader.readAsDataURL(file)

    // If camera was active, stop it
    stopCamera()
  }

  const startCamera = async () => {
    try {
      // Reset error state
      setCameraError(null)

      // Stop any existing stream first
      stopCamera()

      // Make sure video element exists
      if (!videoRef.current) {
        console.error("Video element not found")
        setCameraError("Video element not found. Please try again.")
        return
      }

      console.log("Starting camera...")

      // Get access to the camera
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: "user" },
        audio: false,
      })

      console.log("Camera access granted:", stream)

      // Store the stream reference
      streamRef.current = stream

      // Set the video source to the stream
      videoRef.current.srcObject = stream

      // Set up event handlers for the video element
      videoRef.current.onloadedmetadata = () => {
        console.log("Video metadata loaded")
        if (videoRef.current) {
          videoRef.current
            .play()
            .then(() => {
              console.log("Video playback started")
              setCameraActive(true)
            })
            .catch((err) => {
              console.error("Error playing video:", err)
              setCameraError("Could not play video stream. Please try again.")
            })
        }
      }

      videoRef.current.onerror = (e) => {
        console.error("Video element error:", e)
        setCameraError("Error with video element. Please try again.")
      }
    } catch (err) {
      console.error("Error accessing camera:", err)
      setCameraError(
        "Could not access camera. Please make sure you have granted camera permissions."
      )
    }
  }

  const stopCamera = () => {
    console.log("Stopping camera...")

    if (streamRef.current) {
      streamRef.current.getTracks().forEach((track) => {
        console.log("Stopping track:", track.label)
        track.stop()
      })
      streamRef.current = null
    }

    if (videoRef.current) {
      videoRef.current.srcObject = null
      videoRef.current.onloadedmetadata = null
      videoRef.current.onerror = null
    }

    setCameraActive(false)
    console.log("Camera stopped")
  }

  const capturePhoto = () => {
    if (!videoRef.current || !canvasRef.current || !streamRef.current) {
      console.error("Missing refs for capture:", {
        video: !!videoRef.current,
        canvas: !!canvasRef.current,
        stream: !!streamRef.current,
      })
      setCameraError("Cannot capture photo. Please try again.")
      return
    }

    console.log("Capturing photo...")

    const video = videoRef.current
    const canvas = canvasRef.current

    // Set canvas dimensions to match video
    canvas.width = video.videoWidth || 640
    canvas.height = video.videoHeight || 480

    console.log("Canvas dimensions:", canvas.width, canvas.height)

    // Draw the current video frame on the canvas
    const context = canvas.getContext("2d")
    if (context) {
      try {
        context.drawImage(video, 0, 0, canvas.width, canvas.height)

        // Convert canvas to blob
        canvas.toBlob(
          (blob) => {
            if (blob) {
              console.log("Blob created:", blob.size, "bytes")

              // Create a File object from the blob
              const file = new File([blob], "camera-photo.jpg", {
                type: "image/jpeg",
              })
              setSelectedFile(file)

              // Create preview URL
              const imageUrl = URL.createObjectURL(blob)
              setPreviewUrl(imageUrl)

              // Stop the camera
              stopCamera()
            } else {
              console.error("Failed to create blob")
              setCameraError(
                "Failed to process captured image. Please try again."
              )
            }
          },
          "image/jpeg",
          0.9
        )
      } catch (err) {
        console.error("Error capturing from video:", err)
        setCameraError("Error capturing image. Please try again.")
      }
    } else {
      console.error("Could not get canvas context")
      setCameraError("Could not process image. Please try again.")
    }
  }

  const handleUpload = () => {
    if (!selectedFile) return

    setIsUploading(true)

    // Simulate upload delay
    setTimeout(() => {
      // TODO: Upload the file to server
      console.log("Uploading file:", selectedFile.name)
      setIsUploading(false)
      onOpenChange(false)

      // Reset state
      setSelectedFile(null)
      setPreviewUrl(null)
    }, 1500)
  }

  const handleRemovePhoto = () => {
    //TODO: Remove the photo from your server
    console.log("Removing photo")

    if (onPhotoUpload) {
      onPhotoUpload(null) // Null means no photo
    }

    onOpenChange(false)
  }

  const handleDialogClose = (open: boolean) => {
    if (!open) {
      stopCamera()
    }
    onOpenChange(open)
  }

  return (
    <Dialog open={open} onOpenChange={handleDialogClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Change Profile Photo</DialogTitle>
          <DialogDescription>
            Upload a new profile photo or take a picture with your camera.
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col items-center gap-4 py-4">
          {/* Always render video and canvas elements, but hide them when not active */}
          <div
            className={`relative w-full max-w-[300px] aspect-video bg-black rounded-lg overflow-hidden ${cameraActive ? "block" : "hidden"}`}
          >
            <video
              ref={videoRef}
              autoPlay
              playsInline
              muted
              className="w-full h-full object-cover"
            />

            {cameraError && (
              <div className="absolute inset-0 flex items-center justify-center bg-black/70 text-white p-4 text-center">
                <p>{cameraError}</p>
              </div>
            )}

            <div className="absolute bottom-2 left-0 right-0 flex justify-center gap-2">
              <Button
                onClick={capturePhoto}
                variant="secondary"
                size="sm"
                className="rounded-full h-10 w-10 p-0 bg-white hover:bg-gray-200"
                disabled={!!cameraError}
              >
                <Camera className="h-5 w-5" />
              </Button>
              <Button
                onClick={stopCamera}
                variant="destructive"
                size="sm"
                className="rounded-full h-10 w-10 p-0"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>
          </div>

          {/* Canvas for capturing photos */}
          <canvas ref={canvasRef} className="hidden" />

          {/* Show avatar when camera is not active */}
          {!cameraActive && (
            <div className="h-24 w-24 rounded-full border-2 border-gray-200 overflow-hidden flex items-center justify-center bg-blue-600 text-white">
              {previewUrl || currentPhotoUrl ? (
                <img
                  src={previewUrl || currentPhotoUrl || ""}
                  alt={userName}
                  className="h-full w-full object-cover"
                />
              ) : (
                <span className="text-xl font-medium">{getInitials()}</span>
              )}
            </div>
          )}

          {!cameraActive && (
            <div className="grid w-full gap-2">
              <Label htmlFor="picture" className="sr-only">
                Picture
              </Label>
              <div className="grid grid-cols-2 gap-2">
                <div className="relative">
                  <input
                    id="picture"
                    type="file"
                    accept="image/*"
                    className="absolute inset-0 opacity-0 cursor-pointer"
                    onChange={handleFileChange}
                  />
                  <Button variant="outline" className="w-full" type="button">
                    <Upload className="mr-2 h-4 w-4" />
                    Upload
                  </Button>
                </div>
                <Button
                  variant="outline"
                  className="w-full"
                  type="button"
                  onClick={startCamera}
                >
                  <Camera className="mr-2 h-4 w-4" />
                  Camera
                </Button>
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="flex flex-col sm:flex-row gap-2">
          <Button
            variant="destructive"
            type="button"
            className="sm:order-first"
            onClick={handleRemovePhoto}
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Remove Photo
          </Button>
          <div className="flex gap-2 w-full sm:w-auto">
            <Button
              variant="outline"
              type="button"
              className="flex-1 sm:flex-initial"
              onClick={() => {
                stopCamera()
                setSelectedFile(null)
                setPreviewUrl(null)
                onOpenChange(false)
              }}
            >
              Cancel
            </Button>
            <Button
              type="button"
              className="flex-1 sm:flex-initial"
              disabled={!selectedFile || isUploading || cameraActive}
              onClick={handleUpload}
            >
              {isUploading ? "Uploading..." : "Save"}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
