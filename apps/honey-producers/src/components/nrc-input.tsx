"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

export interface NrcInputProps
  extends Omit<
    React.InputHTMLAttributes<HTMLInputElement>,
    "value" | "onChange"
  > {
  value: string
  onChange: (value: string) => void
}

export const NrcInput = React.forwardRef<HTMLInputElement, NrcInputProps>(
  ({ className, value, onChange, placeholder, ...props }, ref) => {
    const inputRef = React.useRef<HTMLInputElement>(null)

    React.useImperativeHandle(ref, () => inputRef.current as HTMLInputElement)

    const extractDigits = (nrcNumber: string) => nrcNumber.replace(/\D/g, "")

    const formatNrcNumber = (digits: string) => {
      let formatted = ""
      if (digits.length > 0) {
        formatted += digits.substring(0, Math.min(6, digits.length))
      }
      if (digits.length > 6) {
        formatted += "/" + digits.substring(6, Math.min(8, digits.length))
      }
      if (digits.length > 8) {
        formatted += "/" + digits.substring(8, Math.min(9, digits.length))
      }
      return formatted
    }

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const input = e.target.value
      const digits = extractDigits(input)
      const trimmedDigits = digits.substring(0, 9)
      const formattedValue = formatNrcNumber(trimmedDigits)
      onChange(formattedValue)

      if (inputRef.current) {
        const cursorPosition = calculateCursorPosition(
          input,
          formattedValue,
          e.target.selectionStart || 0
        )
        inputRef.current.setSelectionRange(cursorPosition, cursorPosition)
      }
    }

    const calculateCursorPosition = (
      oldValue: string,
      newValue: string,
      oldPosition: number
    ) => {
      const oldValueBeforeCursor = oldValue.substring(0, oldPosition)
      const oldDigitsBeforeCursor = extractDigits(oldValueBeforeCursor).length
      let newPosition = 0
      let digitCount = 0

      for (let i = 0; i < newValue.length; i++) {
        if (/\d/.test(newValue[i])) {
          digitCount++
        }
        if (digitCount > oldDigitsBeforeCursor) break
        newPosition = i + 1
      }
      return newPosition
    }

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (
        e.key === "Backspace" ||
        e.key === "Delete" ||
        e.key === "ArrowLeft" ||
        e.key === "ArrowRight" ||
        e.key === "Tab" ||
        e.key === "Home" ||
        e.key === "End"
      ) {
        return
      }
      if (!/^\d$/.test(e.key)) {
        e.preventDefault()
      }

      const digits = extractDigits(value)
      if (
        digits.length >= 9 &&
        !/^Arrow|Backspace|Delete|Tab|Home|End/.test(e.key)
      ) {
        e.preventDefault()
      }
    }

    return (
      <div className="relative">
        <input
          type="text"
          ref={inputRef}
          className={cn(
            "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
            className
          )}
          value={value}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          placeholder={placeholder || "123456/78/9"}
          {...props}
        />
      </div>
    )
  }
)

NrcInput.displayName = "NrcInput"
