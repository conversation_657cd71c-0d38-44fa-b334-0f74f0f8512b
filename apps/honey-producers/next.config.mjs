import serwistNext from "@serwist/next"

/** @type {(phase: string, defaultConfig: import("next").NextConfig) => Promise<import("next").NextConfig>} */
export default async (phase) => {
  /** @type {import("next").NextConfig} */
  const nextConfig = {
    output: "export",
    distDir: "dist",
    reactStrictMode: true,
    swcMinify: true,
    trailingSlash: true,
    eslint: {
      ignoreDuringBuilds: false,
    },
  }

  const withSerwist = serwistNext({
    swSrc: "src/app/sw.ts",
    swDest: "public/sw.js",
    cacheOnNavigation: false,
    additionalPrecacheEntries: [
      { url: "/offline", revision: "v1" },
      { url: "/manifest.json", revision: "v1" },
      { url: "/forms/honey-harvesting", revision: "v1" },
      { url: "/forms/honey-harvesting/create", revision: "v1" },
      { url: "/forms/honey-harvesting/form", revision: "v1" },
      { url: "/farmers", revision: "v1" },
      { url: "/farmers/create", revision: "v1" },
      { url: "/farmers/farmer", revision: "v1" },
      { url: "/farmers/farmer/create", revision: "v1" },
      { url: "/farmers/farmer/edit", revision: "v1" },
      { url: "/farmers/farmer/beehive", revision: "v1" },
      { url: "/farmers/farmer/beehive/edit", revision: "v1" },
    ],
  })

  return withSerwist(nextConfig)
}
