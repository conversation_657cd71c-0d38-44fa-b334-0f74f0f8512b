{"name": "palmyra-pro-monorepo", "private": true, "author": "zenGate", "version": "1.2.10", "repository": {"type": "git", "url": "git+https://github.com/zenGate-Global/palmyra-pro.git"}, "scripts": {"build": "turbo run build", "clean": "turbo run clean", "dev": "turbo run dev", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "lint": "turbo run lint", "test": "turbo run test", "check-types": "turbo run check-types"}, "devDependencies": {"@types/gradient-string": "^1.1.6", "prettier": "^3.5.0", "turbo": "^2.4.2"}, "engines": {"node": ">=22"}, "workspaces": ["apps/*", "packages/*"], "packageManager": "npm@10.9.2"}